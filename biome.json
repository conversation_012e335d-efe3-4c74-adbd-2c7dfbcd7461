{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "files": {"ignoreUnknown": true}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "formatter": {"enabled": true, "lineWidth": 100, "indentWidth": 2}, "javascript": {"formatter": {"arrowParentheses": "asNeeded", "enabled": true, "indentStyle": "tab", "quoteProperties": "asNeeded", "quoteStyle": "single", "semicolons": "asNeeded", "trailingCommas": "es5"}, "parser": {"unsafeParameterDecoratorsEnabled": true}, "globals": ["process", "<PERSON><PERSON><PERSON>", "__dirname", "__filename", "global", "console"]}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "warn", "noThenProperty": "off", "noConsole": "warn", "noDebugger": "error"}, "style": {"noDefaultExport": "off", "noParameterProperties": "off", "noUselessElse": "error", "useAsConstAssertion": "warn", "useImportType": "off", "useConst": "error", "useNamingConvention": "warn", "useShorthandAssign": "warn"}, "correctness": {"useHookAtTopLevel": "off", "useExhaustiveDependencies": "off", "noUnusedVariables": "error", "noUnusedImports": "error"}, "security": {"noGlobalEval": "error"}, "performance": {"noDelete": "error"}, "complexity": {"noExcessiveCognitiveComplexity": "warn", "noForEach": "off"}, "a11y": "off"}}}