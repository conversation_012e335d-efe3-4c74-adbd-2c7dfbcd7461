{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "resolveJsonModule": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "esnext", "sourceMap": true, "outDir": "./dist", "baseUrl": "./src", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./node_modules/@types", "./global.d.ts"], "types": ["node"], "noUncheckedIndexedAccess": true, "noFallthroughCasesInSwitch": true, "paths": {"@app": ["app"], "@app/*": ["app/*"], "@seed": ["seed"], "@seed/*": ["seed/*"], "@config": ["config"], "@config/*": ["config/*"], "@shared": ["shared"], "@shared/*": ["shared/*"], "@functions": ["functions"], "@functions/*": ["functions/*"]}}, "exclude": ["logs", "dist", "uploads"], "include": ["src/**/*.ts"]}