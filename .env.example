# App Configuration
APP_PORT=3000
NODE_ENV=development
ORIGIN=http://localhost:3000,http://localhost:3001

# Database Configuration
DB_HOST_URL=mongodb://localhost:27017
DB_NAME=icb

# Session Configuration
SESSION_SECRET=your_session_secret

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your_jwt_secret

# Hash Configuration
HASH_SECRET=your_hash_secret

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback
GOOGLE_AUTH_URL=https://accounts.google.com/o/oauth2/v2/auth

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token

# OpenAI Configuration (for embeddings)
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# AI Configuration (OpenRouter for chat models)
OPENROUTER_API_KEY=your_openrouter_api_key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
AI_DEFAULT_MODEL=meta-llama/llama-3.1-405b-instruct:free
AI_MAX_TOKENS=4000
AI_TEMPERATURE=0.7
AI_CHUNK_SIZE=1000
AI_CHUNK_OVERLAP=200
AI_MAX_CONTEXT_MESSAGES=10

# Qdrant Vector Database Configuration
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_qdrant_api_key_optional
QDRANT_COLLECTION_NAME=chat_context
QDRANT_VECTOR_SIZE=1536
QDRANT_DISTANCE=Cosine
QDRANT_SEARCH_LIMIT=5
QDRANT_SEARCH_THRESHOLD=0.7
