# Google OAuth Authentication Setup

This guide will help you set up Google OAuth authentication for the ICB Backend application.

## Prerequisites

1. A Google account
2. Access to the [Google Cloud Console](https://console.cloud.google.com/)

## Steps to Set Up Google OAuth

### 1. Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Click on the project dropdown at the top of the page
3. Click on "New Project"
4. Enter a name for your project and click "Create"
5. Wait for the project to be created and then select it from the project dropdown

### 2. Configure the OAuth Consent Screen

1. In the Google Cloud Console, navigate to "APIs & Services" > "OAuth consent screen"
2. Select "External" as the user type (unless you have a Google Workspace account)
3. Click "Create"
4. Fill in the required information:
   - App name: "ICB Backend" (or your preferred name)
   - User support email: Your email address
   - Developer contact information: Your email address
5. Click "Save and Continue"
6. Add the following scopes:
   - `./auth/userinfo.email`
   - `./auth/userinfo.profile`
7. Click "Save and Continue"
8. Add test users if you're in testing mode
9. Click "Save and Continue"
10. Review your settings and click "Back to Dashboard"

### 3. Create OAuth 2.0 Credentials

1. In the Google Cloud Console, navigate to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Select "Web application" as the application type
4. Enter a name for your OAuth client
5. Add the following authorized JavaScript origins:
   - `http://localhost:3000` (for local development)
   - Your production domain (if applicable)
6. Add the following authorized redirect URIs:
   - `http://localhost:3000/auth/google/callback` (for local development)
   - Your production callback URL (if applicable)
7. Click "Create"
8. Note down the Client ID and Client Secret

### 4. Configure Environment Variables

1. Open your `.env` file
2. Add the following variables with the values from the previous step:
   ```
   GOOGLE_CLIENT_ID=your_client_id
   GOOGLE_CLIENT_SECRET=your_client_secret
   GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback
   ```
3. Restart your application

## Testing the Google OAuth Authentication

1. Start your application
2. Navigate to `/auth/google` in your browser
3. You should be redirected to the Google login page
4. After logging in, you should be redirected back to your application

## Using Referral Codes with Google Authentication

The application supports referral tracking with Google authentication. When a user clicks on a referral link and then authenticates with Google, the referral information is preserved.

### How to Use Referral Codes

1. Generate a referral link that includes the referral code:
   ```
   https://yourdomain.com/auth/google?referralCode=USER123
   ```

2. When a user clicks this link and completes the Google authentication flow, they will be associated with the referrer identified by the referral code.

### How It Works

1. The referral code is passed as a query parameter to the Google authentication endpoint
2. The code is stored in the OAuth state parameter during the authentication flow
3. After successful authentication, the user is associated with the referrer

### Testing Referral Tracking

1. Create a user with a referral code
2. Generate a referral link with that code: `/auth/google?referralCode=USER123`
3. Open the link in an incognito window or different browser
4. Complete the Google authentication flow
5. The new user should now be associated with the referrer

## Troubleshooting

- If you encounter a "redirect_uri_mismatch" error, make sure the callback URL in your `.env` file exactly matches the one you configured in the Google Cloud Console.
- If you encounter a "invalid_client" error, make sure your Client ID and Client Secret are correct.
- If you encounter a "access_denied" error, make sure you've configured the OAuth consent screen correctly and added the required scopes.
