import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { StoreCategory, StoreCategorySchema } from 'shared/entities'
import { StoreCategoryService } from './store-category.service'

@Module({
	imports: [
		MongooseModule.forFeature([
			{
				name: StoreCategory.name,
				schema: StoreCategorySchema,
			},
		]),
	],
	providers: [StoreCategoryService],
	exports: [StoreCategoryService],
})
export class StoreCategoryModule {}
