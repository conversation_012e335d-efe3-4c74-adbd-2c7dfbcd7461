import {
	Body,
	Controller,
	HttpStatus,
	Logger,
	Post,
	UploadedFile,
	UseInterceptors,
	ValidationPipe,
} from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger'
import { ChatService } from './chat.service'
import { ChatRequestDto, ChatResponseDto, EmbedResponseDto } from './dto/chat.dto'
import { AIService, VectorStorageService } from './services'

@ApiTags('Chat')
@Controller('chat')
export class ChatController {
	private readonly logger = new Logger(ChatController.name)

	constructor(
		private readonly chatService: ChatService,
		private readonly vectorStorageService: VectorStorageService,
		private readonly aiService: AIService
	) {}

	@Post()
	@ApiOperation({ summary: 'Send a chat message and receive complete response' })
	@ApiResponse({ status: HttpStatus.OK, type: ChatResponseDto, description: 'Chat response' })
	@ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid request' })
	@ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'Internal server error' })
	async chat(@Body(new ValidationPipe()) chatRequest: ChatRequestDto): Promise<ChatResponseDto> {
		try {
			this.logger.log(`Received chat request: ${chatRequest.message.substring(0, 100)}...`)

			// Generate or use provided session ID
			const sessionId = chatRequest.sessionId || this.chatService.generateSessionId()

			// Save user message
			this.chatService.saveMessage(sessionId, 'user', chatRequest.message)

			// Get chat history
			const chatHistory = await this.chatService.getChatHistory(sessionId)

			// Search for relevant context
			const context = await this.vectorStorageService.searchContext(chatRequest.message)

			// Convert chat history to AI service format
			const aiChatHistory = chatHistory.map(msg => ({
				role: msg.role as 'user' | 'assistant' | 'system',
				content: msg.content,
			}))

			// Generate complete response
			const aiResponse = await this.aiService.generateResponse(
				chatRequest.message,
				aiChatHistory,
				context
			)

			// Save assistant response
			this.chatService.saveMessage(sessionId, 'assistant', aiResponse.text, {
				model: aiResponse.metadata.model,
				contextRetrieved: aiResponse.metadata.contextRetrieved,
				contextSources: aiResponse.metadata.contextSources,
				tokensUsed: aiResponse.metadata.tokensUsed,
				latency: aiResponse.metadata.latency,
			})

			this.logger.log(`Successfully processed chat request for session: ${sessionId}`)

			// Return complete response
			return {
				sessionId,
				message: aiResponse.text,
			}
		} catch (error) {
			this.logger.error('Chat request failed:', error)
			throw new Error(
				`Failed to process chat request: ${error instanceof Error ? error.message : 'Unknown error'}`
			)
		}
	}

	@Post('embed')
	@UseInterceptors(FileInterceptor('file'))
	@ApiOperation({ summary: 'Upload and embed a TXT file for context' })
	@ApiResponse({ status: HttpStatus.OK, type: EmbedResponseDto })
	@ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid file or processing error' })
	async embedFile(@UploadedFile() file: Express.Multer.File): Promise<EmbedResponseDto> {
		try {
			this.logger.log(`Received file upload: ${file?.originalname}`)

			// Validate file
			if (!file) {
				throw new Error('No file provided')
			}

			if (!file.originalname.toLowerCase().endsWith('.txt')) {
				throw new Error('Only TXT files are supported')
			}

			if (file.size > 10 * 1024 * 1024) {
				// 10MB limit
				throw new Error('File size exceeds 10MB limit')
			}

			const startTime = Date.now()

			// Process the file
			const processedDoc = await this.vectorStorageService.storeTextContent(
				file.buffer.toString('utf-8'),
				file.originalname,
				{
					uploadedAt: new Date(),
					fileSize: file.size,
					mimeType: file.mimetype,
				}
			)

			const processingTime = Date.now() - startTime

			this.logger.log(
				`Successfully processed file ${file.originalname}: ${processedDoc.length} chunks stored`
			)

			return {
				message: 'File processed and embedded successfully',
				source: file.originalname,
				chunksProcessed: processedDoc.length,
				documentsStored: processedDoc.length,
				processingTime,
				metadata: {
					originalLength: file.buffer.length,
					chunkSize: processedDoc[0]?.metadata.chunkIndex !== undefined ? 1000 : 0, // Default from config
					chunkOverlap: 200, // Default from config
					processedAt: new Date(),
				},
			}
		} catch (error) {
			this.logger.error('File embedding failed:', error)
			throw new Error(
				`Failed to process file: ${error instanceof Error ? error.message : 'Unknown error'}`
			)
		}
	}
}
