import { createOpenAI } from '@ai-sdk/openai'
import { Injectable, Logger } from '@nestjs/common'
import { generateText } from 'ai'
import { env } from 'config'
import { ContextSearchResult } from './vector-storage.service'

export interface ChatMessage {
	role: 'user' | 'assistant' | 'system'
	content: string
}

export interface GenerationMetadata {
	tokensUsed?: number
	model: string
	latency: number
	contextRetrieved: boolean
	contextSources: string[]
}

@Injectable()
export class AIService {
	private readonly logger = new Logger(AIService.name)
	private readonly model: any

	constructor() {
		// Create OpenAI provider with OpenRouter configuration
		const provider = createOpenAI({
			apiKey: env.AI.openRouterApiKey,
			baseURL: env.AI.openRouterBaseUrl,
			headers: {
				'HTTP-Referer': 'https://indiancashback.com', // Replace with your app URL
				'X-Title': 'IndianCashback',
			},
		})

		// Get the specific model
		this.model = provider(env.AI.defaultModel)
	}

	/**
	 * Generate non-streaming response (for testing/fallback)
	 */
	async generateResponse(
		userMessage: string,
		chatHistory: ChatMessage[],
		context: ContextSearchResult[]
	): Promise<{ text: string; metadata: GenerationMetadata }> {
		try {
			this.logger.log('Generating non-streaming response')

			const startTime = Date.now()
			const messages = this.buildMessages(userMessage, chatHistory, context)
			const contextSources = context.map(c => (c.metadata.source as string) || 'unknown')

			const result = await generateText({
				model: this.model,
				messages,
				temperature: env.AI.temperature,
				maxTokens: env.AI.maxTokens,
			})

			const latency = Date.now() - startTime

			return {
				text: result.text,
				metadata: {
					tokensUsed: result.usage?.totalTokens,
					model: env.AI.defaultModel,
					latency,
					contextRetrieved: context.length > 0,
					contextSources,
				},
			}
		} catch (error) {
			this.logger.error('Failed to generate response:', error)
			throw error
		}
	}

	/**
	 * Build messages array for AI model
	 */
	private buildMessages(
		userMessage: string,
		chatHistory: ChatMessage[],
		context: ContextSearchResult[]
	): Array<{ role: 'user' | 'assistant' | 'system'; content: string }> {
		const messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }> = []

		// System message with context
		const systemMessage = this.buildSystemMessage(context)
		messages.push({
			role: 'system',
			content: systemMessage,
		})

		// Add chat history (limited to maxContextMessages)
		const recentHistory = chatHistory.slice(-env.AI.maxContextMessages)
		for (const msg of recentHistory) {
			messages.push({
				role: msg.role as 'user' | 'assistant' | 'system',
				content: msg.content,
			})
		}

		// Add current user message
		messages.push({
			role: 'user',
			content: userMessage,
		})

		return messages
	}

	/**
	 * Build system message with context
	 */
	private buildSystemMessage(context: ContextSearchResult[]): string {
		let systemMessage = `You are a helpful AI customer support assistant for IndianCashback, India's #1 Most Trusted Cashback Portal. You help users with questions about earning cashback, tracking rewards, payouts, deals, and general platform usage.

About IndianCashback:
- Professional cashback and deals app for online shoppers in India
- Offers unmatched savings with some of the highest cashback rates in the country
- Supports 500+ top online stores (Amazon, Flipkart, Myntra, AJIO, Shopsy, etc.)
- Covers all major categories: fashion, electronics, beauty, home, travel
- Provides instant payouts for ICB card users with 5% extra cashback
- Features seamless automatic cashback tracking (appears within 1-48 hours)
- Offers multiple payout options: bank account, Paytm wallet, UPI, cheque
- ICB InstantPay card holders get expedited payouts (approval in ~10 days)
- Provides exclusive deals, coupons, and 24/7 customer support

Guidelines:
- Be concise in your responses
- Focus on helping users maximize their cashback and savings
- If you don't know something specific, say so honestly and suggest contacting support
- Use the provided context to answer questions when relevant
- Maintain a professional, friendly, and helpful tone
- Emphasize IndianCashback's benefits and trustworthiness when appropriate
- Help users understand how to earn more cashback and use platform features effectively`

		if (context.length > 0) {
			systemMessage += `\n\nRelevant context information:\n`
			context.forEach((ctx, index) => {
				systemMessage += `\n${index + 1}. ${ctx.content}\n`
			})
			systemMessage += `\nUse this context to provide accurate and relevant answers. If the context doesn't contain relevant information for the user's question, you can still provide general helpful responses.`
		}

		return systemMessage
	}

	/**
	 * Health check for AI service
	 */
	async healthCheck(): Promise<boolean> {
		try {
			const result = await generateText({
				model: this.model,
				messages: [{ role: 'user', content: 'Hello' }],
				maxTokens: 10,
			})
			return !!result.text
		} catch (error) {
			this.logger.error('AI service health check failed:', error)
			return false
		}
	}

	/**
	 * Get model configuration
	 */
	getModelConfig() {
		return {
			model: env.AI.defaultModel,
			baseUrl: env.AI.openRouterBaseUrl,
			temperature: env.AI.temperature,
			maxTokens: env.AI.maxTokens,
		}
	}
}
