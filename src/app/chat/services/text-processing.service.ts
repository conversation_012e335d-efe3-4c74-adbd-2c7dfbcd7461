import { promises as fs } from 'node:fs'
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters'
import { Injectable, Logger } from '@nestjs/common'
import { env } from 'config'
import { hash } from 'ohash'

export interface TextChunk {
	id: string
	content: string
	metadata: {
		source: string
		chunkIndex: number
		totalChunks: number
		originalLength: number
		chunkLength: number
	}
}

export interface ProcessedDocument {
	chunks: TextChunk[]
	metadata: {
		source: string
		originalLength: number
		totalChunks: number
		chunkSize: number
		chunkOverlap: number
		processedAt: Date
	}
}

@Injectable()
export class TextProcessingService {
	private readonly logger = new Logger(TextProcessingService.name)
	private readonly textSplitter: RecursiveCharacterTextSplitter

	constructor() {
		this.textSplitter = new RecursiveCharacterTextSplitter({
			chunkSize: env.AI.chunkSize,
			chunkOverlap: env.AI.chunkOverlap,
			separators: ['\n\n', '\n', '. ', '! ', '? ', ' ', ''],
		})
	}

	/**
	 * Process a text file and split it into chunks
	 */
	async processTextFile(filePath: string): Promise<ProcessedDocument> {
		try {
			this.logger.log(`Processing text file: ${filePath}`)

			// Read file content
			const content = await fs.readFile(filePath, 'utf-8')
			return await this.processText(content, filePath)
		} catch (error) {
			this.logger.error(`Failed to process text file ${filePath}:`, error)
			throw error
		}
	}

	/**
	 * Process text content directly and split it into chunks
	 */
	async processText(content: string, source: string = 'direct_input'): Promise<ProcessedDocument> {
		try {
			this.logger.log(`Processing text content from: ${source}`)

			// Validate input
			if (!content || content.trim().length === 0) {
				throw new Error('Content is empty or invalid')
			}

			// Split text into chunks
			const documents = await this.textSplitter.createDocuments([content])

			// Create chunks with metadata
			const chunks: TextChunk[] = documents.map((doc, index) => {
				const chunkId = this.generateChunkId(source, index, doc.pageContent)

				return {
					id: chunkId,
					content: doc.pageContent,
					metadata: {
						source,
						chunkIndex: index,
						totalChunks: documents.length,
						originalLength: content.length,
						chunkLength: doc.pageContent.length,
					},
				}
			})

			const processedDocument: ProcessedDocument = {
				chunks,
				metadata: {
					source,
					originalLength: content.length,
					totalChunks: chunks.length,
					chunkSize: env.AI.chunkSize,
					chunkOverlap: env.AI.chunkOverlap,
					processedAt: new Date(),
				},
			}

			this.logger.log(`Successfully processed text into ${chunks.length} chunks`)
			return processedDocument
		} catch (error) {
			this.logger.error(`Failed to process text content:`, error)
			throw error
		}
	}

	/**
	 * Process multiple text files
	 */
	async processMultipleFiles(filePaths: string[]): Promise<ProcessedDocument[]> {
		try {
			this.logger.log(`Processing ${filePaths.length} text files`)

			const results = await Promise.all(filePaths.map(filePath => this.processTextFile(filePath)))

			this.logger.log(`Successfully processed ${results.length} files`)
			return results
		} catch (error) {
			this.logger.error('Failed to process multiple files:', error)
			throw error
		}
	}

	/**
	 * Validate file type and size
	 */
	async validateTextFile(
		filePath: string,
		maxSizeBytes: number = 10 * 1024 * 1024
	): Promise<boolean> {
		try {
			const stats = await fs.stat(filePath)

			// Check file size
			if (stats.size > maxSizeBytes) {
				throw new Error(`File size ${stats.size} bytes exceeds maximum ${maxSizeBytes} bytes`)
			}

			// Check if file is readable
			await fs.access(filePath, fs.constants.R_OK)

			return true
		} catch (error) {
			this.logger.error(`File validation failed for ${filePath}:`, error)
			throw error
		}
	}

	/**
	 * Extract text content from buffer (for uploaded files)
	 */
	async processTextBuffer(buffer: Buffer, filename: string): Promise<ProcessedDocument> {
		try {
			this.logger.log(`Processing text buffer for file: ${filename}`)

			// Convert buffer to string
			const content = buffer.toString('utf-8')

			// Validate content
			if (!content || content.trim().length === 0) {
				throw new Error('Buffer content is empty or invalid')
			}

			return await this.processText(content, filename)
		} catch (error) {
			this.logger.error(`Failed to process text buffer for ${filename}:`, error)
			throw error
		}
	}

	/**
	 * Generate unique chunk ID
	 */
	private generateChunkId(source: string, index: number, content: string): string {
		const hashInput = `${source}_${index}_${content.substring(0, 100)}`
		return `chunk_${hash(hashInput)}`
	}

	/**
	 * Get text splitter configuration
	 */
	getConfiguration() {
		return {
			chunkSize: env.AI.chunkSize,
			chunkOverlap: env.AI.chunkOverlap,
			separators: ['\n\n', '\n', '. ', '! ', '? ', ' ', ''],
		}
	}

	/**
	 * Update text splitter configuration
	 */
	updateConfiguration(chunkSize?: number, chunkOverlap?: number) {
		if (chunkSize || chunkOverlap) {
			// Create new splitter with updated configuration
			const newSplitter = new RecursiveCharacterTextSplitter({
				chunkSize: chunkSize || env.AI.chunkSize,
				chunkOverlap: chunkOverlap || env.AI.chunkOverlap,
				separators: ['\n\n', '\n', '. ', '! ', '? ', ' ', ''],
			})

			// Replace the current splitter
			Object.assign(this.textSplitter, newSplitter)

			this.logger.log(
				`Updated text splitter configuration: chunkSize=${chunkSize}, chunkOverlap=${chunkOverlap}`
			)
		}
	}
}
