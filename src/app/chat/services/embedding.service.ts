import { OpenAIEmbeddings } from '@langchain/openai'
import { Injectable, Logger } from '@nestjs/common'
import { env } from 'config'

export interface EmbeddingResult {
	text: string
	embedding: number[]
	metadata?: Record<string, any>
}

export interface BatchEmbeddingResult {
	embeddings: EmbeddingResult[]
	totalTokens?: number
	processingTime: number
}

@Injectable()
export class EmbeddingService {
	private readonly logger = new Logger(EmbeddingService.name)
	private readonly embeddings: OpenAIEmbeddings

	constructor() {
		this.embeddings = new OpenAIEmbeddings({
			openAIApiKey: env.OPENAI.apiKey,
			modelName: env.OPENAI.embeddingModel,
			configuration: {
				baseURL: env.OPENAI.baseUrl,
			},
		})
	}

	/**
	 * Generate embedding for a single text
	 */
	async embedText(text: string, metadata?: Record<string, any>): Promise<EmbeddingResult> {
		try {
			this.logger.log(`Generating embedding for text (${text.length} characters)`)

			if (!text || text.trim().length === 0) {
				throw new Error('Text is empty or invalid')
			}

			const startTime = Date.now()
			const embedding = await this.embeddings.embedQuery(text)
			const processingTime = Date.now() - startTime

			this.logger.log(`Generated embedding in ${processingTime}ms (${embedding.length} dimensions)`)

			return {
				text,
				embedding,
				metadata: {
					...metadata,
					processingTime,
					dimensions: embedding.length,
					model: env.OPENAI.embeddingModel,
				},
			}
		} catch (error) {
			this.logger.error('Failed to generate embedding:', error)
			throw error
		}
	}

	/**
	 * Generate embeddings for multiple texts in batch
	 */
	async embedTexts(
		texts: string[],
		metadata?: Record<string, any>[]
	): Promise<BatchEmbeddingResult> {
		try {
			this.logger.log(`Generating embeddings for ${texts.length} texts`)

			if (!texts || texts.length === 0) {
				throw new Error('Texts array is empty')
			}

			// Validate all texts
			const validTexts = texts.filter(text => text && text.trim().length > 0)
			if (validTexts.length !== texts.length) {
				this.logger.warn(`Filtered out ${texts.length - validTexts.length} empty texts`)
			}

			const startTime = Date.now()
			const embeddings = await this.embeddings.embedDocuments(validTexts)
			const processingTime = Date.now() - startTime

			const results: EmbeddingResult[] = embeddings.map((embedding, index) => {
				const text = validTexts[index]
				if (!text) {
					throw new Error(`Missing text at index ${index}`)
				}
				return {
					text,
					embedding,
					metadata: {
						...(metadata?.[index] || {}),
						batchIndex: index,
						dimensions: embedding.length,
						model: env.OPENAI.embeddingModel,
					},
				}
			})

			this.logger.log(`Generated ${results.length} embeddings in ${processingTime}ms`)

			return {
				embeddings: results,
				processingTime,
			}
		} catch (error) {
			this.logger.error('Failed to generate batch embeddings:', error)
			throw error
		}
	}

	/**
	 * Generate embeddings with retry logic
	 */
	async embedTextWithRetry(
		text: string,
		maxRetries: number = 3,
		retryDelay: number = 1000,
		metadata?: Record<string, any>
	): Promise<EmbeddingResult> {
		let lastError: Error

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				return await this.embedText(text, metadata)
			} catch (error) {
				lastError = error as Error
				this.logger.warn(`Embedding attempt ${attempt}/${maxRetries} failed:`, error)

				if (attempt < maxRetries) {
					await this.delay(retryDelay * attempt) // Exponential backoff
				}
			}
		}

		this.logger.error(`All ${maxRetries} embedding attempts failed`)
		throw lastError!
	}

	/**
	 * Generate embeddings for batch with retry logic
	 */
	async embedTextsWithRetry(
		texts: string[],
		maxRetries: number = 3,
		retryDelay: number = 1000,
		metadata?: Record<string, any>[]
	): Promise<BatchEmbeddingResult> {
		let lastError: Error

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				return await this.embedTexts(texts, metadata)
			} catch (error) {
				lastError = error as Error
				this.logger.warn(`Batch embedding attempt ${attempt}/${maxRetries} failed:`, error)

				if (attempt < maxRetries) {
					await this.delay(retryDelay * attempt) // Exponential backoff
				}
			}
		}

		this.logger.error(`All ${maxRetries} batch embedding attempts failed`)
		throw lastError!
	}

	/**
	 * Calculate cosine similarity between two embeddings
	 */
	calculateSimilarity(embedding1: number[], embedding2: number[]): number {
		if (embedding1.length !== embedding2.length) {
			throw new Error('Embeddings must have the same dimensions')
		}

		let dotProduct = 0
		let norm1 = 0
		let norm2 = 0

		for (let i = 0; i < embedding1.length; i++) {
			const val1 = embedding1[i] || 0
			const val2 = embedding2[i] || 0
			dotProduct += val1 * val2
			norm1 += val1 * val1
			norm2 += val2 * val2
		}

		const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2))
		return similarity
	}

	/**
	 * Get embedding model information
	 */
	getModelInfo() {
		return {
			model: env.OPENAI.embeddingModel,
			baseURL: env.OPENAI.baseUrl,
			expectedDimensions: env.QDRANT.vectorSize,
		}
	}

	/**
	 * Validate embedding dimensions
	 */
	validateEmbedding(embedding: number[]): boolean {
		const expectedDimensions = env.QDRANT.vectorSize
		if (embedding.length !== expectedDimensions) {
			this.logger.error(
				`Embedding dimension mismatch: got ${embedding.length}, expected ${expectedDimensions}`
			)
			return false
		}
		return true
	}

	/**
	 * Health check for embedding service
	 */
	async healthCheck(): Promise<boolean> {
		try {
			const testText = 'Health check test'
			await this.embedText(testText)
			return true
		} catch (error) {
			this.logger.error('Embedding service health check failed:', error)
			return false
		}
	}

	/**
	 * Utility method for delays
	 */
	private delay(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms))
	}
}
