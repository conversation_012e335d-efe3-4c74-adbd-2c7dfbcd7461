import { Injectable, Logger } from '@nestjs/common'
import { EmbeddingService } from './embedding.service'
import { QdrantService, VectorPoint } from './qdrant.service'
import { TextProcessingService } from './text-processing.service'

export interface StoredDocument {
	id: string
	content: string
	embedding: number[]
	metadata: {
		source: string
		chunkIndex: number
		totalChunks: number
		storedAt: Date
		[key: string]: unknown
	}
}

export interface ContextSearchResult {
	content: string
	score: number
	metadata: Record<string, unknown>
	id: string | number
}

@Injectable()
export class VectorStorageService {
	private readonly logger = new Logger(VectorStorageService.name)

	constructor(
		private readonly qdrantService: QdrantService,
		private readonly embeddingService: EmbeddingService,
		private readonly textProcessingService: TextProcessingService
	) {}

	/**
	 * Store text content by processing, embedding, and storing to Qdrant
	 */
	async storeTextContent(
		content: string,
		source: string,
		metadata?: Record<string, unknown>
	): Promise<StoredDocument[]> {
		try {
			this.logger.log(`Storing text content from source: ${source}`)

			// Process text into chunks
			const processedDoc = await this.textProcessingService.processText(content, source)

			// Generate embeddings for all chunks
			const texts = processedDoc.chunks.map(chunk => chunk.content)
			const batchResult = await this.embeddingService.embedTexts(texts)

			// Prepare vector points for Qdrant
			const vectorPoints: VectorPoint[] = batchResult.embeddings.map((embeddingResult, index) => {
				const chunk = processedDoc.chunks[index]
				if (!chunk) {
					throw new Error(`Missing chunk at index ${index}`)
				}
				return {
					id: chunk.id,
					vector: embeddingResult.embedding,
					payload: {
						content: chunk.content,
						source: chunk.metadata.source,
						chunkIndex: chunk.metadata.chunkIndex,
						totalChunks: chunk.metadata.totalChunks,
						originalLength: chunk.metadata.originalLength,
						chunkLength: chunk.metadata.chunkLength,
						storedAt: new Date().toISOString(),
						...metadata,
					},
				}
			})

			// Store in Qdrant
			await this.qdrantService.upsertVectors(vectorPoints)

			// Return stored documents
			const storedDocs: StoredDocument[] = vectorPoints.map(point => ({
				id: point.id.toString(),
				content: (point.payload?.content as string) || '',
				embedding: point.vector,
				metadata: {
					source: (point.payload?.source as string) || source,
					chunkIndex: (point.payload?.chunkIndex as number) || 0,
					totalChunks: (point.payload?.totalChunks as number) || 1,
					storedAt: new Date((point.payload?.storedAt as string) || new Date()),
					...point.payload,
				},
			}))

			this.logger.log(`Successfully stored ${storedDocs.length} document chunks`)
			return storedDocs
		} catch (error) {
			this.logger.error(`Failed to store text content from ${source}:`, error)
			throw error
		}
	}

	/**
	 * Search for relevant context based on query
	 */
	async searchContext(
		query: string,
		limit?: number,
		scoreThreshold?: number
	): Promise<ContextSearchResult[]> {
		try {
			this.logger.log(`Searching context for query: "${query.substring(0, 100)}..."`)

			// Generate embedding for the query
			const queryEmbedding = await this.embeddingService.embedText(query)

			// Search in Qdrant
			const searchResults = await this.qdrantService.searchVectors(
				queryEmbedding.embedding,
				limit,
				scoreThreshold
			)

			// Transform results
			const contextResults: ContextSearchResult[] = searchResults.map(result => ({
				content: (result.payload?.content as string) || '',
				score: result.score,
				metadata: result.payload || {},
				id: result.id,
			}))

			this.logger.log(`Found ${contextResults.length} relevant context chunks`)
			return contextResults
		} catch (error) {
			this.logger.error('Failed to search context:', error)
			throw error
		}
	}
}
