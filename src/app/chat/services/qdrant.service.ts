import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { QdrantClient } from '@qdrant/js-client-rest'
import { env } from 'config'

export interface VectorPoint {
	id: string | number
	vector: number[]
	payload?: Record<string, unknown>
}

export interface SearchResult {
	id: string | number
	score: number
	payload?: Record<string, unknown>
}

@Injectable()
export class QdrantService implements OnModuleInit {
	private readonly logger = new Logger(QdrantService.name)
	private client: QdrantClient
	private readonly collectionName = env.QDRANT.collectionName

	constructor() {
		this.client = new QdrantClient({
			url: env.QDRANT.url,
			apiKey: env.QDRANT.apiKey,
		})
	}

	async onModuleInit() {
		try {
			await this.ensureCollection()
			this.logger.log('Qdrant service initialized successfully')
		} catch (error) {
			this.logger.error('Failed to initialize Qdrant service:', error)
			throw error
		}
	}

	/**
	 * Ensure the collection exists with proper configuration
	 */
	private async ensureCollection(): Promise<void> {
		try {
			// Check if collection exists
			const collections = await this.client.getCollections()
			const collectionExists = collections.collections.some(
				collection => collection.name === this.collectionName
			)

			if (!collectionExists) {
				this.logger.log(`Creating collection: ${this.collectionName}`)
				await this.client.createCollection(this.collectionName, {
					vectors: {
						size: env.QDRANT.vectorSize,
						distance: env.QDRANT.distance,
					},
				})
				this.logger.log(`Collection ${this.collectionName} created successfully`)
			} else {
				this.logger.log(`Collection ${this.collectionName} already exists`)
			}
		} catch (error) {
			this.logger.error(`Failed to ensure collection ${this.collectionName}:`, error)
			throw error
		}
	}

	/**
	 * Upsert vectors into the collection
	 */
	async upsertVectors(points: VectorPoint[]): Promise<void> {
		try {
			await this.client.upsert(this.collectionName, {
				wait: true,
				points: points.map(point => ({
					id: point.id,
					vector: point.vector,
					payload: point.payload || {},
				})),
			})
			this.logger.log(`Upserted ${points.length} vectors to ${this.collectionName}`)
		} catch (error) {
			this.logger.error('Failed to upsert vectors:', error)
			throw error
		}
	}

	/**
	 * Search for similar vectors
	 */
	async searchVectors(
		queryVector: number[],
		limit: number = env.QDRANT.searchLimit,
		scoreThreshold?: number
	): Promise<SearchResult[]> {
		try {
			const baseParams = {
				vector: queryVector,
				limit,
				with_payload: true,
			}

			const searchParams =
				scoreThreshold !== undefined
					? { ...baseParams, score_threshold: scoreThreshold }
					: env.QDRANT.searchThreshold !== undefined
						? { ...baseParams, score_threshold: env.QDRANT.searchThreshold }
						: baseParams

			const searchResult = await this.client.search(this.collectionName, searchParams)

			const results: SearchResult[] = searchResult.map(result => ({
				id: result.id,
				score: result.score,
				payload: result.payload || {},
			}))

			this.logger.log(`Found ${results.length} similar vectors`)
			return results
		} catch (error) {
			this.logger.error('Failed to search vectors:', error)
			throw error
		}
	}

	/**
	 * Delete vectors by IDs
	 */
	async deleteVectors(ids: (string | number)[]): Promise<void> {
		try {
			await this.client.delete(this.collectionName, {
				wait: true,
				points: ids,
			})
			this.logger.log(`Deleted ${ids.length} vectors from ${this.collectionName}`)
		} catch (error) {
			this.logger.error('Failed to delete vectors:', error)
			throw error
		}
	}

	/**
	 * Get collection info
	 */
	async getCollectionInfo(): Promise<Record<string, unknown>> {
		try {
			const info = await this.client.getCollection(this.collectionName)
			return info
		} catch (error) {
			this.logger.error('Failed to get collection info:', error)
			throw error
		}
	}

	/**
	 * Clear all vectors from collection
	 */
	async clearCollection(): Promise<void> {
		try {
			await this.client.delete(this.collectionName, {
				wait: true,
				filter: {}, // Empty filter deletes all points
			})
			this.logger.log(`Cleared all vectors from ${this.collectionName}`)
		} catch (error) {
			this.logger.error('Failed to clear collection:', error)
			throw error
		}
	}

	/**
	 * Health check for Qdrant connection
	 */
	async healthCheck(): Promise<boolean> {
		try {
			await this.client.getCollections()
			return true
		} catch (error) {
			this.logger.error('Qdrant health check failed:', error)
			return false
		}
	}
}
