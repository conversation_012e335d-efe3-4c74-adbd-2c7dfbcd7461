import { Injectable, Logger } from '@nestjs/common'
import { tool } from 'ai'
import { Types } from 'mongoose'
import { z } from 'zod'
import { ReqUser } from '../../shared/entities/user.entity'
import { OfferTypes, ReviewTypes, SortTypes } from '../../shared/enums/filters'
import { CategoryService } from '../context/category/category.service'
import { OffersService } from '../context/offers/offers.service'
import { SearchService } from '../context/search/search.service'
import { OfferService } from '../offer/offer.service'
import { ReviewService } from '../store/review/review.service'
import { StoreService } from '../store/store.service'
import { StoreCategoryService } from '../store-category/store-category.service'

@Injectable()
export class ContextToolsService {
	private readonly logger = new Logger(ContextToolsService.name)

	constructor(
		private readonly storeService: StoreService,
		private readonly offerService: OfferService,
		private readonly searchService: SearchService,
		private readonly categoryService: CategoryService,
		private readonly reviewService: ReviewService,
		private readonly offersService: OffersService,
		private readonly storeCategoryService: StoreCategoryService
	) {}

	/**
	 * Create a public user object for context operations
	 */
	private createPublicUser(): ReqUser {
		return {
			id: new Types.ObjectId(),
			email: '<EMAIL>',
		}
	}

	/**
	 * Get all available tools for public/context use
	 */
	getTools() {
		return [
			this.searchStoresAndOffers(),
			this.getStoreDetails(),
			this.getTrendingOffers(),
			this.getStoresByCategory(),
			this.getOfferDetails(),
			this.getStoreReviews(),
			this.getCategoriesAndSubcategories(),
			this.getOngoingSales(),
			this.getStoreCashbackRates(),
		]
	}

	/**
	 * Universal search across stores and offers
	 */
	private searchStoresAndOffers() {
		return tool({
			description:
				'Search for stores and offers using keywords. Returns both stores and offers matching the search query.',
			parameters: z.object({
				query: z.string().describe('Search query (e.g., "amazon", "mobile", "fashion")'),
				type: z
					.enum(['stores', 'offers', 'both'])
					.optional()
					.describe('Type of search - stores, offers, or both'),
				page: z.number().optional().default(1).describe('Page number for pagination'),
				pageSize: z.number().optional().default(10).describe('Number of results per page'),
			}),
			execute: async ({ query, type = 'both', page = 1, pageSize = 10 }) => {
				try {
					this.logger.log('Searching stores and offers', { query, type, page, pageSize })

					const results = await this.searchService.getSearchResults(query)

					// Filter results based on type
					let filteredResults = results
					if (type === 'stores') {
						filteredResults = {
							...results,
							deal: { deals: [], dealsCount: 0 },
							coupon: { coupons: [], couponsCount: 0 },
							giftCard: { giftCards: [], giftCardsCount: 0 },
						}
					} else if (type === 'offers') {
						filteredResults = {
							...results,
							store: { stores: [], storesCount: 0 },
							giftCard: { giftCards: [], giftCardsCount: 0 },
						}
					}

					this.logger.log('Successfully retrieved search results', { query, type })
					return {
						success: true,
						data: filteredResults,
						message: `Found search results for "${query}"`,
					}
				} catch (error) {
					this.logger.error('Failed to search stores and offers', {
						query,
						type,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to search stores and offers',
					}
				}
			},
		})
	}

	/**
	 * Get detailed information about a specific store
	 */
	private getStoreDetails() {
		return tool({
			description:
				'Get detailed information about a store including cashback rates, description, and policies.',
			parameters: z.object({
				storeName: z.string().describe('Store name (e.g., "amazon", "flipkart", "myntra")'),
			}),
			execute: async ({ storeName }) => {
				try {
					this.logger.log('Getting store details', { storeName })

					// Create a public user object for context access
					const publicUser = this.createPublicUser()
					const storeDetails = await this.storeService.getStoreDetailsByName(storeName, publicUser)

					this.logger.log('Successfully retrieved store details', { storeName })
					return {
						success: true,
						data: storeDetails,
						message: `Retrieved details for ${storeName}`,
					}
				} catch (error) {
					this.logger.error('Failed to retrieve store details', {
						storeName,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: `Store "${storeName}" not found or error retrieving details`,
					}
				}
			},
		})
	}

	/**
	 * Get trending offers and deals
	 */
	private getTrendingOffers() {
		return tool({
			description: 'Get currently trending offers, deals, and coupons across all stores.',
			parameters: z.object({
				offerType: z
					.enum(['coupons', 'deals', 'both'])
					.optional()
					.default('both')
					.describe('Type of offers to retrieve'),
				category: z.string().optional().describe('Filter by category (optional)'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(15).describe('Number of offers per page'),
			}),
			execute: async ({ offerType = 'both', category, page = 1, pageSize = 15 }) => {
				try {
					const offers = await this.offerService.getAllOffers(
						{
							offerType:
								offerType === 'both'
									? undefined
									: offerType === 'deals'
										? OfferTypes.Deals
										: OfferTypes.Coupons,
							subCategories: category,
							page,
							pageSize,
							sortType: SortTypes.Popular,
							searchParam: '',
							subCategoriesArray: category ? category.split(',').map(Number) : undefined,
						},
						this.createPublicUser()
					)

					return {
						success: true,
						data: offers,
						message: `Retrieved ${offerType} trending offers`,
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve trending offers',
					}
				}
			},
		})
	}

	/**
	 * Get stores filtered by category
	 */
	private getStoresByCategory() {
		return tool({
			description: 'Get stores filtered by category (e.g., fashion, electronics, travel).',
			parameters: z.object({
				category: z.string().describe('Category name or ID'),
				sortBy: z
					.enum(['newest', 'alphabetical', 'highestCbAmount', 'highestCbPercent'])
					.optional()
					.default('alphabetical')
					.describe('Sort order'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(20).describe('Number of stores per page'),
			}),
			execute: async ({ category, sortBy = 'alphabetical', page = 1, pageSize = 20 }) => {
				try {
					// Map sortBy to correct SortTypes enum
					let mappedSortType: SortTypes
					switch (sortBy) {
						case 'newest':
							mappedSortType = SortTypes.Newest
							break
						case 'alphabetical':
							mappedSortType = SortTypes.Alphabetical
							break
						case 'highestCbAmount':
							mappedSortType = SortTypes.HighestCbAmount
							break
						case 'highestCbPercent':
							mappedSortType = SortTypes.HighestCbPercent
							break
						default:
							mappedSortType = SortTypes.Alphabetical
					}

					const stores = await this.storeService.getAllStores(
						{
							subCategories: category,
							sortType: mappedSortType,
							page,
							pageSize,
							searchParam: '',
							minPercent: 0,
							maxPercent: 100,
							saved: false,
						},
						this.createPublicUser()
					)

					return {
						success: true,
						data: stores,
						message: `Retrieved stores in ${category} category`,
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: `Failed to retrieve stores for category "${category}"`,
					}
				}
			},
		})
	}

	/**
	 * Get detailed information about a specific offer
	 */
	private getOfferDetails() {
		return tool({
			description:
				'Get detailed information about a specific offer including terms, cashback rates, and expiry.',
			parameters: z.object({
				offerUid: z.number().describe('Unique identifier of the offer'),
			}),
			execute: async ({ offerUid }) => {
				try {
					const offerDetails = await this.offerService.getOfferAndSimilarOffers(
						offerUid,
						this.createPublicUser()
					)

					return {
						success: true,
						data: offerDetails,
						message: `Retrieved details for offer ${offerUid}`,
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: `Offer with UID ${offerUid} not found`,
					}
				}
			},
		})
	}

	/**
	 * Get store reviews and ratings
	 */
	private getStoreReviews() {
		return tool({
			description: 'Get user reviews and ratings for a specific store.',
			parameters: z.object({
				storeUid: z.number().describe('Store unique identifier'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(10).describe('Number of reviews per page'),
			}),
			execute: async ({ storeUid, page = 1, pageSize = 10 }) => {
				try {
					const reviews = await this.reviewService.getAllReviewsOfStore({
						storeId: storeUid.toString(),
						page,
						pageSize,
						sortType: ReviewTypes.Newest,
					})

					return {
						success: true,
						data: reviews,
						message: `Retrieved reviews for store ${storeUid}`,
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: `Failed to retrieve reviews for store ${storeUid}`,
					}
				}
			},
		})
	}

	/**
	 * Get all available categories and subcategories
	 */
	private getCategoriesAndSubcategories() {
		return tool({
			description: 'Get all available shopping categories and subcategories for browsing.',
			parameters: z.object({
				includeSubcategories: z
					.boolean()
					.optional()
					.default(true)
					.describe('Include subcategories in response'),
			}),
			execute: async ({ includeSubcategories = true }) => {
				try {
					const categories = await this.categoryService.getAllCategories(includeSubcategories)

					return {
						success: true,
						data: categories,
						message: 'Retrieved all categories and subcategories',
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve categories',
					}
				}
			},
		})
	}

	/**
	 * Get ongoing sales and promotions
	 */
	private getOngoingSales() {
		return tool({
			description: 'Get current ongoing sales, festivals, and special promotions.',
			parameters: z.object({
				category: z.string().optional().describe('Filter by category'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(15).describe('Number of sales per page'),
			}),
			execute: async ({ category, page = 1, pageSize = 15 }) => {
				try {
					const ongoingSales = await this.offerService.getOngoingOffers(
						{
							subCategories: category,
							page,
							pageSize,
							searchParam: '',
							subCategoriesArray: category ? category.split(',').map(Number) : undefined,
							saleIdArray: undefined,
						},
						this.createPublicUser()
					)

					return {
						success: true,
						data: ongoingSales,
						message: 'Retrieved ongoing sales and promotions',
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve ongoing sales',
					}
				}
			},
		})
	}

	/**
	 * Get cashback rates for a specific store
	 */
	private getStoreCashbackRates() {
		return tool({
			description: 'Get detailed cashback rates for different categories within a store.',
			parameters: z.object({
				storeId: z.string().describe('Store ID to get cashback rates for'),
			}),
			execute: async ({ storeId }) => {
				try {
					const cashbackRates = await this.storeService.getCashbackRatesByStoreId(storeId)

					return {
						success: true,
						data: cashbackRates,
						message: `Retrieved cashback rates for store ${storeId}`,
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: `Failed to retrieve cashback rates for store ${storeId}`,
					}
				}
			},
		})
	}
}
