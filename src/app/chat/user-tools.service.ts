import { Injectable, Logger } from '@nestjs/common'
import { tool } from 'ai'
import { Types } from 'mongoose'
import { z } from 'zod'
import { ReqUser } from '../../shared/entities/user.entity'
import { PaymentTypes, SortTypes } from '../../shared/enums/filters'
import { ClickService } from '../click/click.service'
import { MissingCashbackService } from '../click/missing-cashback/missing-cashback.service'
import { OfferService } from '../offer/offer.service'
import { PaymentService } from '../payment/payment.service'
import { SavedItemService } from '../saved-item/saved-item.service'
import { StoreService } from '../store/store.service'
import { UserService } from '../user/user.service'

@Injectable()
export class UserToolsService {
	private readonly logger = new Logger(UserToolsService.name)

	constructor(
		private readonly userService: UserService,
		private readonly clickService: ClickService,
		private readonly savedItemService: SavedItemService,
		private readonly paymentService: PaymentService,
		private readonly storeService: StoreService,
		private readonly offerService: OfferService,
		private readonly missingCashbackService: MissingCashbackService
	) {}

	/**
	 * Helper method to get complete user data by email
	 */
	private async getUserByEmail(email: string): Promise<ReqUser> {
		try {
			const user = await this.userService.getUserByEmail(email)
			if (!user) {
				throw new Error(`User not found with email: ${email}`)
			}
			return {
				id: user._id,
				email: user.email,
			}
		} catch (error) {
			this.logger.error('Failed to get user by email', {
				email,
				error: error.message,
				stack: error.stack,
			})
			throw error
		}
	}

	/**
	 * Get all available tools for authenticated users
	 */
	getTools() {
		return [
			this.getUserProfile(),
			this.getUserEarnings(),
			this.createClick(),
			this.getSavedItems(),
			this.saveStoreOrOffer(),
			this.removeSavedItem(),
			this.getUserClicks(),
			this.requestPayment(),
			this.getMissingCashbackReports(),
			this.getReferralStats(),
			this.updateUserProfile(),
			this.getUserBalance(),
			this.getPaymentHistory(),
			this.getUserOverview(),
			this.getClickedStores(),
		]
	}

	/**
	 * Get user profile information
	 */
	private getUserProfile() {
		return tool({
			description:
				'Get user profile information including balance, personal interests, and account details.',
			parameters: z.object({
				userId: z.string().describe('User ID to get profile for'),
			}),
			execute: async ({ userId }) => {
				try {
					this.logger.log('Getting user profile', { userId })

					// Convert string ID to ObjectId
					const objectId = new Types.ObjectId(userId)
					const profile = await this.userService.getUserProfileById(objectId)

					this.logger.log('Successfully retrieved user profile', { userId })
					return {
						success: true,
						data: profile,
						message: 'Retrieved user profile successfully',
					}
				} catch (error) {
					this.logger.error('Failed to retrieve user profile', {
						userId,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve user profile',
					}
				}
			},
		})
	}

	/**
	 * Get user earnings and cashback history
	 */
	private getUserEarnings() {
		return tool({
			description: 'Get user cashback earnings history with filtering options.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				status: z
					.enum(['pending', 'confirmed', 'cancelled', 'tracked_for_confirm'])
					.optional()
					.describe('Filter by earning status'),
				startDate: z.string().optional().describe('Start date for filtering (YYYY-MM-DD)'),
				endDate: z.string().optional().describe('End date for filtering (YYYY-MM-DD)'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(20).describe('Number of earnings per page'),
			}),
			execute: async ({ userEmail, status, startDate, endDate, page = 1, pageSize = 20 }) => {
				try {
					this.logger.log('Getting user earnings', { userEmail, status, page, pageSize })

					const userData = await this.getUserByEmail(userEmail)
					const queryParams: any = {
						page,
						pageSize,
					}
					if (status) queryParams.status = status
					if (startDate) queryParams.startDate = startDate
					if (endDate) queryParams.endDate = endDate

					const earnings = await this.userService.getCashBackHistory(userData, queryParams)

					this.logger.log('Successfully retrieved user earnings', {
						userEmail,
						count: earnings?.cbItems?.length || 0,
					})
					return {
						success: true,
						data: earnings,
						message: 'Retrieved user earnings successfully',
					}
				} catch (error) {
					this.logger.error('Failed to retrieve user earnings', {
						userEmail,
						status,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve user earnings',
					}
				}
			},
		})
	}

	/**
	 * Create a click for cashback tracking
	 */
	private createClick() {
		return tool({
			description:
				'Create a click to track user activity for cashback. Use this when user wants to shop through a store or offer.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				type: z
					.enum(['offer', 'express', 'rates'])
					.describe('Type of click - offer (specific deal), express (store), or rates (category)'),
				uid: z.number().describe('UID of the offer, store, or store category'),
				userIp: z.string().optional().describe('User IP address'),
				userCity: z.string().optional().describe('User city'),
				device: z.string().optional().describe('User device info'),
			}),
			execute: async ({ userEmail, type, uid, userIp, userCity, device }) => {
				try {
					this.logger.log('Creating click', { userEmail, type, uid, userCity })

					const userData = await this.getUserByEmail(userEmail)
					const clientInfo = {
						ip: userIp || '0.0.0.0',
						geo: null, // Set to null as per ClientInfoData interface
						device: device || 'Unknown',
						userAgent: 'ChatBot',
						os: 'Unknown',
						browser: 'ChatBot',
					}

					const click = await this.clickService.create({ type, uid }, clientInfo, userData)

					this.logger.log('Successfully created click', {
						userEmail,
						type,
						uid,
						referenceId: click.referenceId,
					})
					return {
						success: true,
						data: click,
						message: `Click created successfully for ${type} with UID ${uid}`,
					}
				} catch (error) {
					this.logger.error('Failed to create click', {
						userEmail,
						type,
						uid,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to create click',
					}
				}
			},
		})
	}

	/**
	 * Get user's saved items (stores and offers)
	 */
	private getSavedItems() {
		return tool({
			description: 'Get user saved stores and offers.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				type: z
					.enum(['stores', 'offers', 'giftcards'])
					.optional()
					.describe('Type of saved items to retrieve'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(20).describe('Number of items per page'),
			}),
			execute: async ({ userEmail, type, page = 1, pageSize = 20 }) => {
				try {
					this.logger.log('Getting saved items', { userEmail, type, page, pageSize })

					const userData = await this.getUserByEmail(userEmail)

					let savedItems: any
					if (type === 'stores' || !type) {
						const storeParams = {
							page,
							pageSize,
							searchParam: '',
							sortType: SortTypes.Newest,
							minPercent: 0,
							maxPercent: 100,
							subCategories: undefined,
							saved: true,
						}
						savedItems = await this.savedItemService.getAllSavedStores(storeParams, userData)
					} else if (type === 'offers') {
						// For offers, we need to get both deals and coupons
						const offerParams = {
							page,
							pageSize,
							searchParam: '',
							subCategoriesArray: undefined,
						}
						const deals = await this.savedItemService.getAllSavedDeals(offerParams, userData)
						const coupons = await this.savedItemService.getAllSavedCoupons(offerParams, userData)
						savedItems = {
							deals: deals.deals || [],
							coupons: coupons.coupons || [],
							pagination: deals.pagination,
						}
					} else if (type === 'giftcards') {
						const giftCardParams = {
							page,
							pageSize,
							searchParam: '',
							subCategoriesArray: undefined,
						}
						savedItems = await this.savedItemService.getAllSavedGiftCards(giftCardParams, userData)
					}

					this.logger.log('Successfully retrieved saved items', {
						userEmail,
						type,
						count:
							savedItems?.stores?.length ||
							savedItems?.deals?.length ||
							savedItems?.giftCards?.length ||
							0,
					})
					return {
						success: true,
						data: savedItems,
						message: `Retrieved saved ${type || 'items'} successfully`,
					}
				} catch (error) {
					this.logger.error('Failed to retrieve saved items', {
						userEmail,
						type,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve saved items',
					}
				}
			},
		})
	}

	/**
	 * Save a store or offer to user's favorites
	 */
	private saveStoreOrOffer() {
		return tool({
			description: 'Save a store or offer to user favorites for easy access later.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				type: z.enum(['store', 'offer']).describe('Type of item to save'),
				itemUid: z.number().describe('UID of the store or offer to save'),
			}),
			execute: async ({ userEmail, type, itemUid }) => {
				try {
					this.logger.log('Saving item', { userEmail, type, itemUid })

					const userData = await this.getUserByEmail(userEmail)

					let result: any
					if (type === 'store') {
						result = await this.storeService.saveStore({ itemUid }, userData)
					} else {
						result = await this.offerService.saveOffer({ itemUid }, userData)
					}

					this.logger.log('Successfully saved item', { userEmail, type, itemUid })
					return {
						success: true,
						data: result,
						message: `${type} saved successfully`,
					}
				} catch (error) {
					this.logger.error('Failed to save item', {
						userEmail,
						type,
						itemUid,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: `Failed to save ${type}`,
					}
				}
			},
		})
	}

	/**
	 * Remove a saved item from user's favorites
	 */
	private removeSavedItem() {
		return tool({
			description: 'Remove a store or offer from user favorites.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				type: z.enum(['store', 'offer']).describe('Type of item to remove'),
				itemUid: z.number().describe('UID of the store or offer to remove'),
			}),
			execute: async ({ userEmail, type, itemUid }) => {
				try {
					this.logger.log('Removing saved item', { userEmail, type, itemUid })

					const userData = await this.getUserByEmail(userEmail)

					let result: any
					if (type === 'store') {
						result = await this.storeService.removeStore(userData, { itemUid })
					} else {
						result = await this.offerService.removeSavedOffer(userData, { itemUid })
					}

					this.logger.log('Successfully removed saved item', { userEmail, type, itemUid })
					return {
						success: true,
						data: result,
						message: `${type} removed from favorites successfully`,
					}
				} catch (error) {
					this.logger.error('Failed to remove saved item', {
						userEmail,
						type,
						itemUid,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: `Failed to remove ${type} from favorites`,
					}
				}
			},
		})
	}

	/**
	 * Get user's click history
	 */
	private getUserClicks() {
		return tool({
			description:
				'Get user click history with status tracking (pending, tracked, confirmed, etc.).',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				status: z.array(z.string()).optional().describe('Filter by click status'),
				stores: z.array(z.number()).optional().describe('Filter by store UIDs'),
				startDate: z.string().optional().describe('Start date (YYYY-MM-DD)'),
				endDate: z.string().optional().describe('End date (YYYY-MM-DD)'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(20).describe('Number of clicks per page'),
			}),
			execute: async ({
				userEmail,
				status,
				stores,
				startDate,
				endDate,
				page = 1,
				pageSize = 20,
			}) => {
				try {
					this.logger.log('Getting user clicks', { userEmail, page, pageSize })

					const userData = await this.getUserByEmail(userEmail)
					const queryParams: any = {
						page,
						pageSize,
						sortType: 'newest',
					}
					if (status) queryParams.status = status.join(',')
					if (stores) queryParams.stores = stores
					if (startDate) queryParams.startDate = startDate
					if (endDate) queryParams.endDate = endDate

					const clicks = await this.clickService.getClicks(userData, queryParams)

					this.logger.log('Successfully retrieved user clicks', {
						userEmail,
						count: clicks?.clicks?.length || 0,
					})
					return {
						success: true,
						data: clicks,
						message: 'Retrieved user clicks successfully',
					}
				} catch (error) {
					this.logger.error('Failed to retrieve user clicks', {
						userEmail,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve user clicks',
					}
				}
			},
		})
	}

	/**
	 * Request payment/withdrawal
	 */
	private requestPayment() {
		return tool({
			description: 'Request cashback withdrawal to bank account or UPI.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				amount: z.number().describe('Amount to withdraw'),
				paymentMethod: z.enum(['bank', 'upi']).describe('Payment method'),
			}),
			execute: async ({ userEmail, amount, paymentMethod }) => {
				try {
					this.logger.log('Creating payment request', { userEmail, amount, paymentMethod })

					const userData = await this.getUserByEmail(userEmail)
					const paymentRequest = await this.paymentService.requestPayments(
						{
							withdrawAmount: amount,
							paymentType: paymentMethod === 'bank' ? PaymentTypes.Bank : PaymentTypes.Upi,
						},
						userData
					)

					this.logger.log('Successfully created payment request', { userEmail, amount })
					return {
						success: true,
						data: paymentRequest,
						message: `Payment request of ₹${amount} created successfully`,
					}
				} catch (error) {
					this.logger.error('Failed to create payment request', {
						userEmail,
						amount,
						paymentMethod,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to create payment request',
					}
				}
			},
		})
	}

	/**
	 * Report missing cashback
	 */
	private getMissingCashbackReports() {
		return tool({
			description: 'Get list of missing cashback reports submitted by the user.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(10).describe('Number of reports per page'),
			}),
			execute: async ({ userEmail, page = 1, pageSize = 10 }) => {
				try {
					this.logger.log('Getting missing cashback reports', { userEmail })

					const userData = await this.getUserByEmail(userEmail)
					const reports = await this.missingCashbackService.listMissingCashback(userData, {
						page,
						pageSize,
						sortType: 'newest',
						searchParam: '',
						stores: [],
						status: '',
						startDate: '',
						endDate: '',
					})

					this.logger.log('Successfully retrieved missing cashback reports', {
						userEmail,
						count: reports?.missingCashbacks?.length || 0,
					})
					return {
						success: true,
						data: reports,
						message: 'Missing cashback reports retrieved successfully',
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Failed to submit missing cashback report',
					}
				}
			},
		})
	}

	/**
	 * Get referral statistics and earnings
	 */
	private getReferralStats() {
		return tool({
			description: 'Get user referral statistics including referred users and commission earnings.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(20).describe('Number of referrals per page'),
				status: z.string().optional().describe('Filter by referral status'),
			}),
			execute: async ({ userEmail, page = 1, pageSize = 20, status }) => {
				try {
					this.logger.log('Getting referral stats', { userEmail, page, pageSize, status })

					const userData = await this.getUserByEmail(userEmail)
					const queryParams: any = {
						page,
						pageSize,
					}
					if (status) queryParams.status = status

					const referralStats = await this.userService.getReferralHistory(userData, queryParams)

					return {
						success: true,
						data: referralStats,
						message: 'Retrieved referral statistics successfully',
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve referral statistics',
					}
				}
			},
		})
	}

	/**
	 * Update user profile information
	 */
	private updateUserProfile() {
		return tool({
			description:
				'Update user profile information like name, personal interests, and notification preferences.',
			parameters: z.object({
				userId: z.string().describe('User ID'),
				name: z.string().optional().describe('User name'),
				personalInterest: z.string().optional().describe('Personal interest category ID'),
				sendNotification: z.boolean().optional().describe('Notification preference'),
			}),
			execute: async ({ userId, name, personalInterest, sendNotification }) => {
				try {
					this.logger.log('Updating user profile', {
						userId,
						name,
						personalInterest,
						sendNotification,
					})

					const updateData: {
						name?: string
						personalInterest?: Types.ObjectId
						sendNotification?: boolean
					} = {}
					if (name) updateData.name = name
					if (personalInterest) updateData.personalInterest = new Types.ObjectId(personalInterest)
					if (sendNotification !== undefined) updateData.sendNotification = sendNotification

					// Convert string ID to ObjectId
					const objectId = new Types.ObjectId(userId)
					const result = await this.userService.updateProfile(
						updateData,
						objectId,
						Buffer.alloc(0) // Empty buffer for no image
					)

					this.logger.log('Successfully updated user profile', { userId })
					return {
						success: true,
						data: result,
						message: 'Profile updated successfully',
					}
				} catch (error) {
					this.logger.error('Failed to update profile', {
						userId,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to update profile',
					}
				}
			},
		})
	}

	/**
	 * Get user balance information
	 */
	private getUserBalance() {
		return tool({
			description: 'Get user current balance including cashback and gift card balance.',
			parameters: z.object({
				userId: z.string().describe('User ID'),
			}),
			execute: async ({ userId }) => {
				try {
					this.logger.log('Getting user balance', { userId })

					// Convert string ID to ObjectId
					const objectId = new Types.ObjectId(userId)
					const balance = await this.userService.getUserBalance(objectId)

					this.logger.log('Successfully retrieved user balance', { userId })
					return {
						success: true,
						data: balance,
						message: 'Retrieved user balance successfully',
					}
				} catch (error) {
					this.logger.error('Failed to retrieve user balance', {
						userId,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve user balance',
					}
				}
			},
		})
	}

	/**
	 * Get payment history
	 */
	private getPaymentHistory() {
		return tool({
			description: 'Get user payment and withdrawal history.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
				status: z
					.enum(['pending', 'approved', 'rejected', 'processing'])
					.optional()
					.describe('Filter by payment status'),
				page: z.number().optional().default(1).describe('Page number'),
				pageSize: z.number().optional().default(20).describe('Number of payments per page'),
			}),
			execute: async ({ userEmail, status, page = 1, pageSize = 20 }) => {
				try {
					this.logger.log('Getting payment history', { userEmail, status, page, pageSize })

					const userData = await this.getUserByEmail(userEmail)
					const queryParams: any = {
						page,
						pageSize,
					}
					if (status) queryParams.status = status

					const payments = await this.paymentService.getAllPaymentRequestedUser(
						queryParams,
						userData
					)

					this.logger.log('Successfully retrieved payment history', {
						userEmail,
						count: payments?.payments?.length || 0,
					})
					return {
						success: true,
						data: payments,
						message: 'Retrieved payment history successfully',
					}
				} catch (error) {
					this.logger.error('Failed to retrieve payment history', {
						userEmail,
						status,
						error: error.message,
						stack: error.stack,
					})
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve payment history',
					}
				}
			},
		})
	}

	/**
	 * Get user overview with statistics
	 */
	private getUserOverview() {
		return tool({
			description:
				'Get comprehensive user overview including earnings, clicks, and statistics for the last 12 months.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
			}),
			execute: async ({ userEmail }) => {
				try {
					const userData: ReqUser = { email: userEmail } as ReqUser
					const overview = await this.userService.getUsersOverViewDetails(userData)

					return {
						success: true,
						data: overview,
						message: 'Retrieved user overview successfully',
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve user overview',
					}
				}
			},
		})
	}

	/**
	 * Get stores user has clicked on
	 */
	private getClickedStores() {
		return tool({
			description: 'Get list of stores that user has clicked on for shopping.',
			parameters: z.object({
				userEmail: z.string().describe('User email'),
			}),
			execute: async ({ userEmail }) => {
				try {
					const clickedStores = await this.clickService.getClickedStores(userEmail)

					return {
						success: true,
						data: clickedStores,
						message: 'Retrieved clicked stores successfully',
					}
				} catch (error) {
					return {
						success: false,
						error: error.message,
						message: 'Failed to retrieve clicked stores',
					}
				}
			},
		})
	}
}
