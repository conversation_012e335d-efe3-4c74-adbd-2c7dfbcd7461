import { IsNotEmpty, <PERSON><PERSON><PERSON>al, <PERSON>String, <PERSON><PERSON>ength } from 'class-validator'

export class ChatRequestDto {
	@IsNotEmpty()
	@IsString()
	@MaxLength(4000, { message: 'Message is too long (max 4000 characters)' })
	message: string

	@IsOptional()
	@IsString()
	sessionId?: string
}

export class ChatResponseDto {
	sessionId: string
	message: string
}

export class HealthCheckDto {
	status: 'healthy' | 'unhealthy'
	services: {
		qdrant: boolean
		embedding: boolean
		vectorStorage: boolean
	}
	timestamp: Date
}

export class EmbedResponseDto {
	message: string
	source: string
	chunksProcessed: number
	documentsStored: number
	processingTime: number
	metadata: {
		originalLength: number
		chunkSize: number
		chunkOverlap: number
		processedAt: Date
	}
}

export class EmbedStatusDto {
	totalDocuments: number
	uniqueSources: string[]
	averageChunkSize: number
	lastUpdated: Date
}
