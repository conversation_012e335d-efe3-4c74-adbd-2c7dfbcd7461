import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { ChatMessage, ChatMessageSchema } from '@shared/entities'
import { ChatController } from './chat.controller'
import { ChatService } from './chat.service'
import {
	AIService,
	EmbeddingService,
	QdrantService,
	TextProcessingService,
	VectorStorageService,
} from './services'

@Module({
	imports: [
		MongooseModule.forFeature([
			{
				name: ChatMessage.name,
				schema: ChatMessageSchema,
			},
		]),
	],
	controllers: [ChatController],
	providers: [
		ChatService,
		QdrantService,
		TextProcessingService,
		EmbeddingService,
		VectorStorageService,
		AIService,
	],
	exports: [
		ChatService,
		QdrantService,
		TextProcessingService,
		EmbeddingService,
		VectorStorageService,
		AIService,
	],
})
export class ChatModule {}
