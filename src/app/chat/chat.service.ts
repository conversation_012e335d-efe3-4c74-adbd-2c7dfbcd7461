import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { ChatMessage, ChatMessageDocument } from '@shared/entities'
import { Model } from 'mongoose'

export interface MessageMetadata {
	model?: string
	tokensUsed?: number
	latency?: number
	contextRetrieved?: boolean
	contextSources?: string[]
	toolsUsed?: string[]
	[key: string]: unknown
}

@Injectable()
export class ChatService {
	private readonly logger = new Logger(ChatService.name)

	constructor(
		@InjectModel(ChatMessage.name)
		private readonly chatMessageModel: Model<ChatMessageDocument>
	) {}

	/**
	 * Save a chat message to the database
	 */
	async saveMessage(
		sessionId: string,
		role: 'user' | 'assistant' | 'system',
		content: string,
		metadata?: MessageMetadata
	): Promise<ChatMessage> {
		try {
			const message = new this.chatMessageModel({
				sessionId,
				role,
				content,
				metadata,
				createdAt: new Date(),
			})

			const savedMessage = await message.save()
			this.logger.log(`Saved ${role} message for session: ${sessionId}`)
			return savedMessage
		} catch (error) {
			this.logger.error(`Failed to save message for session ${sessionId}:`, error)
			throw error
		}
	}

	/**
	 * Get chat history for a session
	 */
	async getChatHistory(sessionId: string, limit: number = 50): Promise<ChatMessage[]> {
		try {
			const messages = await this.chatMessageModel
				.find({ sessionId })
				.sort({ createdAt: 1 })
				.limit(limit)
				.lean()

			this.logger.log(`Retrieved ${messages.length} messages for session: ${sessionId}`)
			return messages
		} catch (error) {
			this.logger.error(`Failed to get chat history for session ${sessionId}:`, error)
			throw error
		}
	}

	/**
	 * Generate or validate session ID
	 */
	generateSessionId(): string {
		return `chat_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
	}
}
