import { Controller, Get, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { TermsAndPrivacyResponseItem } from '../types/terms-and-privacy.types'
import { GetTermsAndPrivacyDto } from './dto/get-terms-and-privacy.dto'
import { TermsAndPrivacyService } from './terms-and-privacy.service'

@ApiTags('Context')
@Controller('context/terms-and-privacy')
export class TermsAndPrivacyController {
	constructor(private readonly termsAndPrivacyService: TermsAndPrivacyService) {}
	@ApiResponse({ type: TermsAndPrivacyResponseItem })
	@Get()
	async getAllTermsAndConditions(@Query() queryParams: GetTermsAndPrivacyDto): Promise<string> {
		return this.termsAndPrivacyService.getAllTermsAndPrivacy(queryParams.type)
	}
}
