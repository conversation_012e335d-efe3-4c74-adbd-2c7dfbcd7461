import { Controller, Get } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { TestimonialResponseType } from '../types/testimonial.types'
import { TestimonialService } from './testimonial.service'

@ApiTags('Context')
@Controller('context/testimonials')
export class TestimonialController {
	constructor(private readonly testimonialService: TestimonialService) {}

	@ApiResponse({ type: [TestimonialResponseType] })
	@Get()
	async getAllTestimonials() {
		return await this.testimonialService.getAllTestimonials()
	}
}
