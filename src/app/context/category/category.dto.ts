import { ApiProperty } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'

export class CategoryResponse {
	@ApiProperty({ type: String })
	id!: string

	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name!: string

	@ApiProperty({ type: String })
	iconUrl!: string

	@ApiProperty({ type: Number, default: undefined, required: false })
	trendingPriority?: number
}

export class AllSubCategory {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string
}

export class AllCategoriesResponse {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: [AllSubCategory] })
	subCategories: AllSubCategory[]
}

export class SubCategoryStore {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: String })
	imageUrl!: string

	@ApiProperty({ type: String })
	caption!: string

	@ApiProperty({ type: String })
	bgColor!: string

	@IsOptional()
	@ApiProperty({ type: Boolean })
	saved: boolean
}
export class SubCategoryOffer {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: String })
	offerImage!: string

	@ApiProperty({ type: String })
	offerTitle: string

	@IsOptional()
	@ApiProperty({ type: String })
	offerUrl?: string
}

export class SubCategoriesByCategory {
	@ApiProperty({ type: Number })
	uid!: number

	@ApiProperty({ type: String })
	title!: string

	@ApiProperty({ type: String })
	iconUrl!: string
}

export class SubCategoriesByCategoryResponse {
	@ApiProperty({ type: [SubCategoriesByCategory] })
	subCategories!: SubCategoriesByCategory[]

	@ApiProperty({ type: [SubCategoryStore] })
	stores!: SubCategoryStore[]

	@ApiProperty({ type: SubCategoryOffer })
	latestOffer!: SubCategoryOffer
}
