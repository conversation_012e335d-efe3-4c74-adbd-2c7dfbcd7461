import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { SavedItemService } from 'app/saved-item/saved-item.service'
import { UserService } from 'app/user/user.service'
import { Model } from 'mongoose'
import {
	Category,
	CategoryDocument,
	ReqUser,
	SavedItemDocument,
	StoreDocument,
	SubCategory,
	SubCategoryDocument,
} from 'shared/entities'
import { SavedEnum } from 'shared/types'
import { OffersService } from '../offers/offers.service'
import { StoresService } from '../stores/stores.service'
import {
	AllCategoriesResponse,
	AllSubCategory,
	SubCategoriesByCategory,
	SubCategoriesByCategoryResponse,
	SubCategoryOffer,
	SubCategoryStore,
} from './category.dto'

@Injectable()
export class CategoryService {
	constructor(
		@InjectModel(Category.name)
		private category: Model<CategoryDocument>,
		@InjectModel(SubCategory.name)
		private subCategory: Model<SubCategoryDocument>,
		private storeService: StoresService,
		private offerService: OffersService,
		private userService: UserService,
		private savedItemService: SavedItemService
	) {}

	async getAllCategories(trending: boolean) {
		const categories = await this.category.find({ active: true, trending }).exec()
		return categories.map(category => {
			const { _id, name, image, trendingPriority, uid } = category
			return {
				id: _id,
				uid,
				name,
				iconUrl: image.secureUrl,
				trendingPriority: trending ? trendingPriority : undefined,
			}
		})
	}

	async getSubCategorySearch(value: string) {
		const subCategories = await this.subCategory
			.find({ name: new RegExp(`${value}`, 'i') })
			.limit(20)
			.lean()
			.exec()
		return subCategories.map(subCategory => {
			const { uid, name, image } = subCategory
			return {
				uid,
				title: name,
				iconUrl: image.secureUrl,
			}
		})
	}

	async getSubCategoryByCategoryId(
		id: string,
		userSession: ReqUser
	): Promise<SubCategoriesByCategoryResponse> {
		const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null
		const usersSavedStores: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Store], user._id)
			: []

		const category = await this.category.findById(id).lean().exec()
		if (!category) {
			throw new NotFoundException('Category not found')
		}
		const subCategories = await this.subCategory
			.find({ category: category._id })
			.limit(10)
			.lean()
			.exec()

		const subCategoryPromises1 = subCategories.map(async subCategory => {
			const { uid: _uid, image, name } = subCategory
			const subCategoryData: SubCategoriesByCategory = {
				uid: _uid,
				title: name,
				iconUrl: image.secureUrl,
			}

			return subCategoryData
		})
		const resolvedSubCategories = await Promise.all(subCategoryPromises1)

		const subCategoryData: SubCategoriesByCategoryResponse = {
			subCategories: resolvedSubCategories,
			stores: [],
			latestOffer: {
				storeName: '',
				uid: 0,
				offerImage: '',
				offerTitle: '',
				offerUrl: '',
			},
		}
		if (resolvedSubCategories.length > 0 && resolvedSubCategories[0]?.uid) {
			const storesBySubCategoryId = await this.storeService.getStoresBySubCategoryIds(
				[resolvedSubCategories[0].uid],
				10
			)
			subCategoryData.stores = storesBySubCategoryId.map((store: StoreDocument) => {
				const isSaved = usersSavedStores.some(savedStore => savedStore.itemUid === store.uid)
				const { uid, name, logo, storeOffer, bgColor = '#70367C' } = store
				return {
					uid,
					storeName: name,
					imageUrl: logo.secureUrl,
					caption: storeOffer,
					saved: isSaved,
					bgColor,
				} as SubCategoryStore
			})
			for (const store of storesBySubCategoryId) {
				const offer = await this.offerService.getLatestOfferByStoreId(store._id)
				if (offer) {
					subCategoryData.latestOffer = {
						storeName: store.name,
						uid: offer.uid,
						offerImage: offer.productImage?.secureUrl ?? '',
						offerTitle: offer.title,
						offerUrl: offer.url,
					} as SubCategoryOffer
					break
				}
			}
		}
		return subCategoryData
	}

	async getAllCategoriesDetails(): Promise<AllCategoriesResponse[]> {
		const categories = await this.category.find({ active: true }).sort({ priority: -1 }).exec()
		const categoryPromises = categories.map(async category => {
			const { uid, name, _id } = category
			const subCategories = await this.subCategory.find({ category: _id }).lean().exec()
			return {
				uid,
				name,
				subCategories: subCategories.map(subCategory => {
					const { uid, name, _id } = subCategory
					return {
						uid,
						name,
					} as AllSubCategory
				}),
			} as AllCategoriesResponse
		})
		const resolvedCategories = await Promise.all(categoryPromises)
		return resolvedCategories
	}
}
