import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'

export class ContextOfferDealsType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	productImage: string

	@ApiProperty({ type: String })
	storeLogoUrl: string

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: String })
	storeBgColor: string

	@ApiProperty({ type: String })
	endDate!: string

	@ApiProperty({ type: String })
	offerTitle: string

	@ApiProperty({ type: String })
	@IsOptional()
	offerCaption?: string

	@ApiProperty({ type: Number })
	salePrice: number

	@IsOptional()
	@ApiProperty({ type: String })
	offerUrl?: string

	@IsOptional()
	@ApiProperty({ type: Boolean })
	saved: boolean

	@ApiPropertyOptional({
		type: Boolean,
	})
	isAutoGenerated?: boolean

	@ApiPropertyOptional({
		type: Boolean,
	})
	hideCbTag?: boolean

	//
	// @ApiProperty({ type: Boolean })
	// migrated: boolean;
}

export class ContextOfferCouponsType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	productImage: string

	@ApiProperty({ type: String })
	storeLogoUrl: string

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: String })
	storeBgColor: string

	@ApiProperty({ type: String })
	endDate: string

	@ApiProperty({ type: String })
	offerTitle: string

	@ApiProperty({ type: String })
	@IsOptional()
	offerCaption?: string

	@ApiProperty({ type: Number })
	salePrice: number

	@ApiProperty({ type: String })
	couponCode!: string

	@IsOptional()
	@ApiProperty({ type: String })
	offerUrl?: string

	@IsOptional()
	@ApiProperty({ type: Boolean })
	saved: boolean

	@ApiPropertyOptional({
		type: Boolean,
	})
	isAutoGenerated?: boolean

	@ApiPropertyOptional({
		type: Boolean,
	})
	hideCbTag?: boolean

	// @ApiProperty({ type: Boolean })
	// migrated: boolean;
}
export class ContextMissedOfferType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	productImage: string

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: String })
	storeBgColor: string

	@ApiProperty({ type: String })
	storeLogoUrl: string

	@ApiProperty({ type: String })
	offerTitle: string

	//NOTE - currently amount is fields are omitted
	// @ApiProperty({ type: Number })
	// missedAmount?: number;

	@ApiProperty({ type: Number })
	currentAmount?: number
}

export class ContextOngoingOfferType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	productImage: string

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: String })
	storeBgColor: string

	@ApiProperty({ type: String })
	storeLogoUrl: string

	@ApiProperty({ type: String })
	endDate: string

	@ApiProperty({ type: String })
	offerTitle: string

	@ApiProperty({ type: String })
	@IsOptional()
	offerCaption?: string

	@ApiProperty({ type: Number })
	salePrice: number

	@ApiProperty({ type: String })
	saleLogoUrl?: string

	@ApiProperty({ type: String })
	saleCaption?: string

	@IsOptional()
	@ApiProperty({ type: String })
	offerUrl?: string

	@IsOptional()
	@ApiProperty({ type: Boolean })
	saved: boolean

	@ApiPropertyOptional({
		type: Boolean,
	})
	isAutoGenerated?: boolean

	@ApiPropertyOptional({
		type: Boolean,
	})
	hideCbTag?: boolean
}

export class TrendingOfferResponseType {
	@ApiProperty({ type: [ContextOfferDealsType] })
	deals: ContextOfferDealsType[]

	@ApiProperty({ type: [ContextOfferCouponsType] })
	coupons: ContextOfferCouponsType[]
}

export class CategorizedOffers {
	@ApiProperty({ type: TrendingOfferResponseType })
	trendingOffers: TrendingOfferResponseType

	@ApiProperty({ type: [ContextOngoingOfferType] })
	ongoingOffers: ContextOngoingOfferType[]

	@ApiProperty({ type: [ContextMissedOfferType] })
	expiredOffers: ContextMissedOfferType[]
}

export class GetAllOnGoingOffersResponse {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string
}
