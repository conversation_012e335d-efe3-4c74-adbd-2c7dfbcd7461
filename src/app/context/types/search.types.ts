import { ApiProperty } from '@nestjs/swagger'

export class SearchResponseType {
	@ApiProperty()
	uid: number

	@ApiProperty()
	count: number

	@ApiProperty()
	url: string

	@ApiProperty()
	title: string

	@ApiProperty()
	name: string

	@ApiProperty()
	newUserOffer: string

	@ApiProperty()
	oldUserOffer: string

	@ApiProperty()
	caption: string

	@ApiProperty()
	couponCode: string
}

export class SearchStoreResponse {
	@ApiProperty({ type: [SearchResponseType] })
	storeList: SearchResponseType[]

	@ApiProperty({ type: Number })
	total: number
}

export class SearchDealResponse {
	@ApiProperty({ type: [SearchResponseType] })
	dealList: SearchResponseType[]

	@ApiProperty({ type: Number })
	total: number
}

export class SearchCouponResponse {
	@ApiProperty({ type: [SearchResponseType] })
	couponList: SearchResponseType[]

	@ApiProperty({ type: Number })
	total: number
}

export class SearchGiftCardResponse {
	@ApiProperty({ type: [SearchResponseType] })
	giftCardList: SearchResponseType[]

	@ApiProperty({ type: Number })
	total: number
}

export class SearchSuggestion {
	@ApiProperty({
		description: 'Suggested corrected search term (supports multi-word phrases)',
	})
	suggestion: string

	@ApiProperty({
		description: 'Confidence score between 0 and 1 (higher is better)',
	})
	confidence: number
}

export class SearchResponseItem {
	@ApiProperty({ type: SearchStoreResponse })
	stores: SearchStoreResponse

	@ApiProperty({ type: SearchDealResponse })
	deals: SearchDealResponse

	@ApiProperty({ type: SearchCouponResponse })
	coupons: SearchCouponResponse

	@ApiProperty({ type: SearchGiftCardResponse })
	giftCards: SearchGiftCardResponse

	@ApiProperty({
		type: [SearchSuggestion],
		required: false,
		description:
			'Spelling suggestions for misspelled search terms (supports multi-word corrections)',
	})
	suggestions?: SearchSuggestion[]
}
