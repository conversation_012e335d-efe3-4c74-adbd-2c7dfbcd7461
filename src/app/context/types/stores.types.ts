import { ApiProperty } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'

export class ContextStore {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	bgColor: string

	@ApiProperty({ type: String })
	caption?: string

	@ApiProperty({ type: Number })
	offerCount?: number

	@ApiProperty({ type: String })
	imageUrl: string

	@ApiProperty({ type: String })
	storeName: string

	@IsOptional()
	@ApiProperty({ type: Boolean })
	saved?: boolean
}

export class CategoryStoresResponse {
	@ApiProperty({ type: String })
	categoryId: string

	@ApiProperty({ type: String })
	categoryName: string

	@ApiProperty({ type: [ContextStore] })
	stores: ContextStore[]
}

export class StoresByCbContextResponse {
	@ApiProperty({ type: [ContextStore] })
	'100pc': ContextStore[]

	@ApiProperty({ type: [ContextStore] })
	'50pc': ContextStore[]

	@ApiProperty({ type: [ContextStore] })
	'25pc': ContextStore[]
}
