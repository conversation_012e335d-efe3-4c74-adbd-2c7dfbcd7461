import { ApiProperty } from '@nestjs/swagger'
import { MobileStory } from 'shared/entities/mobile-stories.entity'

export type StoriesMongooseResponse = {
	_id: string
	storeName: string
	storeLogo: {
		secureUrl: string
		publicId: string
	}
	stories: MobileStory[]
}

export enum ContentType {
	Image = 'image',
	Video = 'video',
}

export class MobileStoryResponse {
	@ApiProperty({ type: String })
	imageUrl!: string

	@ApiProperty({ enum: ContentType })
	type!: ContentType

	@ApiProperty({ type: Number })
	duration!: number

	@ApiProperty({ type: Number })
	timestamp!: number

	@ApiProperty({ type: String })
	title!: string

	@ApiProperty({ type: String })
	description!: string

	@ApiProperty({ type: String })
	buttonText!: string

	@ApiProperty({ type: String })
	redirectUrl!: string
}

export class ResponseMobileStories {
	@ApiProperty({ type: String })
	storeName!: string

	@ApiProperty({ type: String })
	storeLogo!: string

	@ApiProperty({ type: String })
	storeBgColor!: string

	@ApiProperty({ type: [MobileStoryResponse] })
	stories!: MobileStoryResponse[]
}
