import { Controller, Get } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { ResponseMobileStories } from '../types/stories.types'
import { StoriesService } from './stories.service'

@ApiTags('Context')
@Controller('context/stories')
export class StoriesController {
	constructor(private readonly storiesService: StoriesService) {}

	@ApiResponse({
		type: [ResponseMobileStories],
	})
	@Get()
	async getStories(): Promise<ResponseMobileStories[]> {
		return this.storiesService.getStories()
	}
}
