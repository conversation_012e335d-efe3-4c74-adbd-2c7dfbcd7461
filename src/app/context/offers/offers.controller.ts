import { Controller, Get, Headers } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { AuthOptional, OptionalUser } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { CategorizedOffers, GetAllOnGoingOffersResponse } from '../types/offers.types'
import { OffersService } from './offers.service'

@ApiTags('Context')
@Controller('context/offers')
export class OffersController {
	constructor(private readonly offerService: OffersService) {}

	@ApiResponse({
		type: () => CategorizedOffers,
	})
	@AuthOptional()
	@Get()
	async getLandingOffers(
		@OptionalUser() user: ReqUser,
		@Headers('timezone') _timezone = 'Asia/Kolkata' // Default to 'Asia/Kolkata' if no timezone header is provided
	) {
		return this.offerService.getLandingOffers(user)
	}

	@ApiResponse({
		type: [GetAllOnGoingOffersResponse],
	})
	@Get('all-on-going-offers')
	async getOnGoingSaleOffers() {
		return this.offerService.getOnGoingSaleOffers()
	}
}
