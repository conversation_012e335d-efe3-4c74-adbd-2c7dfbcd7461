import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsOptional, IsString } from 'class-validator'
import { PaginationDto } from 'shared/dto'
import { OfferTypes, SortTypes, UserTypes } from 'shared/enums'

export class CouponsAndDealsDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'and',
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsOptional()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes' })
	sortType?: SortTypes

	@IsEnum(UserTypes)
	@IsOptional()
	@ApiProperty({ enum: UserTypes, enumName: 'UserTypes' })
	userType?: UserTypes

	@IsEnum(OfferTypes)
	@IsOptional()
	@ApiProperty({ enum: OfferTypes, enumName: 'OfferTypes' })
	offerType?: OfferTypes

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	subCategories?: string

	get subCategoriesArray(): number[] | undefined {
		if (this.subCategories) {
			return (
				this.subCategories
					.split(',')
					.map(Number)
					.filter(num => !Number.isNaN(num)) ?? undefined
			)
		}
		return undefined
	}
}
