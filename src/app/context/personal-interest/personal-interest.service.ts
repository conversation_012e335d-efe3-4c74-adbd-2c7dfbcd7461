import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { PersonalInterest, PersonalInterestDocument } from 'shared/entities'

@Injectable()
export class PersonalInterestService {
	constructor(
		@InjectModel(PersonalInterest.name)
		private readonly personalInterest: Model<PersonalInterestDocument>
	) {}

	async getAllPersonalInterestData() {
		const data = await this.personalInterest.find({ active: true }, { _id: 1, name: 1 }).exec()
		return data.map(item => {
			return {
				id: item._id,
				name: item.name,
			}
		})
	}
}
