import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { PersonalInterest, PersonalInterestSchema } from 'shared/entities'
import { PersonalInterestService } from './personal-interest.service'

@Module({
	imports: [
		MongooseModule.forFeature([
			{
				name: PersonalInterest.name,
				schema: PersonalInterestSchema,
			},
		]),
	],
	providers: [PersonalInterestService],
	exports: [PersonalInterestService],
})
export class PersonalInterestModule {}
