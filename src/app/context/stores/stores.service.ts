import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { SavedItemService } from 'app/saved-item/saved-item.service'
import { UserService } from 'app/user/user.service'
import { Model, Types } from 'mongoose'
import {
	Categories,
	Category,
	CategoryDocument,
	ReqUser,
	SavedItemDocument,
	Store,
	StoreDocument,
	SubCategory,
	SubCategoryDocument,
} from 'shared/entities'
import { buildStoreAggregateQuery } from 'shared/helpers/store.helper'
import { SavedEnum } from 'shared/types'
import { OffersService } from '../offers/offers.service'
import { ContextStore, StoresByCbContextResponse } from '../types/stores.types'

@Injectable()
export class StoresService {
	constructor(
		@InjectModel(Store.name) private readonly store: Model<StoreDocument>,
		@InjectModel(SubCategory.name)
		private readonly subCategories: Model<SubCategoryDocument>,
		@InjectModel(Category.name)
		private readonly categories: Model<CategoryDocument>,
		private readonly offerService: OffersService,
		private readonly userService: UserService,
		private readonly savedItemService: SavedItemService
	) {}
	async getTotalStoresCount() {
		return this.store.countDocuments({ active: true }).exec()
	}
	async getStoresByCategoryIds(categoryIds: Types.ObjectId[]) {
		return this.store
			.find({
				'categories.category': { $in: categoryIds },
				active: true,
			})
			.exec()
	}

	async getStoresBySubCategoryIds(subCategoryIds: number[], limit = -1) {
		// First, fetch the subcategory documents to get their _ids
		const subCategories = await this.subCategories.find({ uid: { $in: subCategoryIds } }).exec()

		// Extract the _ids
		const subCategoryObjectIds = subCategories.map(sc => sc._id.toString())

		const aggregationPipeline = buildStoreAggregateQuery({
			subCategoryIds: subCategoryObjectIds,
			limit,
			// biome-ignore lint/suspicious/noExplicitAny: required for external API
		}) as any
		const stores = await this.store.aggregate(aggregationPipeline).exec()

		return stores[0].documents
	}

	async populateSubCategories(categories: Categories[]) {
		// Iterate over categories array
		const populatedCategories = await Promise.all(
			categories.map(async category => {
				// For each category, map over subCategories array to replace ObjectIds with full documents
				const populatedSubCategories = await Promise.all(
					category.subCategories.map(async subCategoryId => {
						// Fetch the subCategory document by its _id
						const subCategoryDoc = await this.subCategories.findById(subCategoryId).lean()
						return subCategoryDoc
					})
				)

				// Return the category with the populated subCategories
				return {
					...category,
					subCategories: populatedSubCategories,
				}
			})
		)

		return populatedCategories
	}

	async getStoresByCatWithAmazonSpecialCase(): Promise<
		{ categoryId: string; categoryName: string; stores: ContextStore[] }[]
	> {
		// Find the top 6 categories based on priority
		const topCategories = await this.categories
			.find({ active: true, isTop: true })
			.sort({ priority: -1 })
			.limit(6)
			.exec()

		if (topCategories.length === 0) {
			return []
		}

		// NOTE - Amazon special condition
		const amazonId = new Types.ObjectId('66f206ec28628113b4856900') // Ensure amazonId is an ObjectId

		// Extract category IDs
		const categoryIds = topCategories.map(cat => cat._id)

		// Fetch stores for each category (minimum 15 per category)
		const storesByCategory = await Promise.all(
			categoryIds.map(async categoryId => {
				// Check if Amazon belongs to this category
				const amazonStore = await this.store.findOne({
					_id: amazonId,
					'categories.category': categoryId,
				})

				// Determine the limit for other stores
				const limit = amazonStore ? 14 : 15

				// Fetch other stores in the category
				const stores = await this.store
					.find({
						'categories.category': categoryId,
						active: true,
						$or: [{ isDeleted: false }, { isDeleted: { $exists: false } }],
						...(amazonStore ? { _id: { $ne: amazonId } } : {}), // Exclude Amazon if it's already fetched
					})
					.sort({ priority: -1 })
					.limit(limit)
					.exec()

				// Get offer counts for all stores in this category
				const storeIds = stores.map(store => store._id)
				if (amazonStore) {
					storeIds.push(amazonId)
				} // Include Amazon's ID for offer count

				const offersCount = await this.offerService.getOffersCountByStoreId(storeIds)

				// Transform stores to ContextStore format
				const contextStores = stores.map(store => {
					const offersCountData = offersCount.find(item => item.uid === store.uid)
					return {
						uid: store.uid,
						bgColor: store.bgColor || '#70367C',
						caption: store.storeOffer,
						offerCount: offersCountData?.count ?? 0,
						imageUrl: store.logo?.secureUrl,
						storeName: store.name,
					}
				})

				// If Amazon exists, add it to the second position of the array
				if (amazonStore) {
					const amazonOffersCount = offersCount.find(item => item.uid === amazonStore.uid)
					const amazonContextStore = {
						uid: amazonStore.uid,
						bgColor: amazonStore.bgColor || '#70367C',
						caption: amazonStore.storeOffer,
						offerCount: amazonOffersCount?.count ?? 0,
						imageUrl: amazonStore.logo?.secureUrl,
						storeName: amazonStore.name,
					}
					contextStores.splice(1, 0, amazonContextStore) // Add Amazon to the second position
				}

				// Find the category name from topCategories
				const category = topCategories.find(cat => cat._id.equals(categoryId))
				return {
					categoryId: categoryId.toString(),
					categoryName: category?.name || '',
					stores: contextStores,
				}
			})
		)

		return storesByCategory
	}

	async getStoresByCat(): Promise<
		{ categoryId: string; categoryName: string; stores: ContextStore[] }[]
	> {
		// Find the top 6 categories based on priority
		const topCategories = await this.categories
			.find({ active: true, isTop: true })
			.sort({ priority: -1 })
			.limit(6)
			.exec()

		if (topCategories.length === 0) {
			return []
		}

		const _amazonId = '66f206ec28628113b4856900'

		// Extract category IDs
		const categoryIds = topCategories.map(cat => cat._id)

		// Fetch stores for each category (minimum 15 per category)
		const storesByCategory = await Promise.all(
			categoryIds.map(async categoryId => {
				const stores = await this.store
					.find({
						'categories.category': categoryId,
						active: true,
						$or: [{ isDeleted: false }, { isDeleted: { $exists: false } }],
					})
					.sort({ priority: -1 })
					.limit(15)
					.exec()

				// Get offer counts for all stores in this category
				const storeIds = stores.map(store => store._id)
				const offersCount = await this.offerService.getOffersCountByStoreId(storeIds)

				// Transform stores to ContextStore format
				const contextStores = stores.map(store => {
					const offersCountData = offersCount.find(item => item.uid === store.uid)
					return {
						uid: store.uid,
						bgColor: store.bgColor || '#70367C',
						caption: store.storeOffer,
						offerCount: offersCountData?.count ?? 0,
						imageUrl: store.logo?.secureUrl,
						storeName: store.name,
					}
				})

				// Find the category name from topCategories
				const category = topCategories.find(cat => cat._id.equals(categoryId))
				return {
					categoryId: categoryId.toString(),
					categoryName: category?.name || '',
					stores: contextStores,
				}
			})
		)

		return storesByCategory
	}

	async getStoresByCbPercent(userSession: ReqUser): Promise<StoresByCbContextResponse> {
		const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null
		const usersSavedStores: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Store], user._id)
			: []

		// in the descending order of priority and only the first 15 needs to be shown
		const subCategoryNames = ['100% Cashback Stores', '50% Cashback Stores', '25% Cashback Stores']
		// Group 100%, 50%, 25% cashback stores separately in an object
		let storesByCbPercent: StoresByCbContextResponse = {
			'100pc': [],
			'50pc': [],
			'25pc': [],
		}

		const storesBeforeSavedUpdate = await this.getStoresByPercentArray(subCategoryNames)

		if (!storesBeforeSavedUpdate) {
			throw new NotFoundException('Stores not found')
		}

		const stores = storesBeforeSavedUpdate.map((store: StoreDocument) => {
			const isSaved = usersSavedStores.some(savedStore => savedStore.itemUid === store.uid)
			return { ...store, saved: isSaved }
		})

		for (const store of stores) {
			let { _id, uid, bgColor = '#70367C', storeOffer, logo, name, saved, categories } = store

			const populatedCats = await this.populateSubCategories(categories)
			categories = populatedCats as Categories[]

			if (
				categories.some(category =>
					category?.subCategories.some(subCategory => subCategory?.name === subCategoryNames[0])
				)
			) {
				storesByCbPercent['100pc'].push({
					uid,
					bgColor,
					saved,
					caption: storeOffer,
					imageUrl: logo?.secureUrl,
					storeName: name,
				})
			}
			if (
				categories.some(category =>
					category?.subCategories.some(subCategory => subCategory.name === subCategoryNames[1])
				)
			) {
				storesByCbPercent['50pc'].push({
					uid,
					bgColor,
					saved,
					caption: storeOffer,
					imageUrl: logo?.secureUrl,
					storeName: name,
				})
			}
			if (
				categories.some(category =>
					category?.subCategories.some(subCategory => subCategory.name === subCategoryNames[2])
				)
			) {
				storesByCbPercent['25pc'].push({
					uid,
					bgColor,
					saved,
					caption: storeOffer,
					imageUrl: logo?.secureUrl,
					storeName: name,
				})
			}
		}
		// Limit the count to 15
		storesByCbPercent = {
			'100pc': storesByCbPercent['100pc'].slice(0, 15),
			'50pc': storesByCbPercent['50pc'].slice(0, 15),
			'25pc': storesByCbPercent['25pc'].slice(0, 15),
		}

		return storesByCbPercent
	}

	async getStoresForSearch(subCategoryNames: string[], name: string) {
		const queryConditions = {
			name: { $regex: name, $options: 'i' },
			active: true,
		}
		const aggregationPipeline = buildStoreAggregateQuery({
			subCategoryNames,
			queryConditions,
			limit: 3,
			// biome-ignore lint/suspicious/noExplicitAny: required for external API
		}) as any
		const [stores] = await this.store.aggregate(aggregationPipeline)
		if (!stores) {
			throw new NotFoundException('Stores not found')
		}
		const storesList = stores.documents as StoreDocument[]
		const storeIds = storesList.map(store => store._id)
		const offersCount = await this.offerService.getOffersCountByStoreId(storeIds)
		const storesData = storesList.map(store => {
			const { uid, logo, name, bgColor } = store
			//Find the match of store id in offersCount and get the count
			const offersCountData = offersCount.find(item => item.uid === uid)

			return {
				uid,
				imageUrl: logo.secureUrl,
				offerCount: offersCountData?.count ?? 0,
				storeName: name,
				bgColor,
			}
		})

		return {
			stores: storesData,
			storesCount: stores.totalCount.length > 0 ? stores.totalCount[0].count : 0,
		}
	}

	async getStoresByPercentArray(subCategoryNames: string[]): Promise<StoreDocument[]> {
		const subCategories = await this.subCategories.find({ name: { $in: subCategoryNames } }).exec()

		// Extract the _ids as ObjectIds
		const subCategoryObjectIds = subCategories.map(sc => sc._id.toString())

		const sortQuery: Record<string, 1 | -1> = { priority: -1 }
		const aggregationPipeline = buildStoreAggregateQuery({
			subCategoryIds: subCategoryObjectIds,
			sortQuery,
		})

		// remove deleted items from the response
		aggregationPipeline.unshift({
			$match: {
				$or: [{ isDeleted: false }, { isDeleted: { $exists: false } }],
			},
		})

		const aggregationResult = await this.store.aggregate(aggregationPipeline).exec()

		if (
			!aggregationResult ||
			aggregationResult.length === 0 ||
			aggregationResult[0].documents.length === 0
		) {
			throw new NotFoundException('Stores not found')
		}

		const [result] = aggregationResult

		const stores = result.documents as StoreDocument[]
		return stores
	}
}
