import { Controller, Get } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { AuthOptional, OptionalUser } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { CategoryStoresResponse, StoresByCbContextResponse } from '../types/stores.types'
import { StoresService } from './stores.service'

@ApiTags('Context')
@Controller('context/stores-by-cb-percent')
export class StoresController {
	constructor(private readonly storeService: StoresService) {}

	@ApiResponse({
		type: StoresByCbContextResponse,
	})
	@AuthOptional()
	@Get()
	async getContextStoresByCb(@OptionalUser() userSession: ReqUser) {
		return this.storeService.getStoresByCbPercent(userSession)
	}

	@ApiResponse({
		type: [CategoryStoresResponse],
	})
	@Get('find-by-category')
	async getAllCategories() {
		return this.storeService.getStoresByCatWithAmazonSpecialCase()
	}
}
