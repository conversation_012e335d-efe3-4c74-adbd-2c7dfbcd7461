import { Controller, Get } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { HeroResponseItem, QuickAccessResponseItem } from '../types/quick-access.types'
import { QuickAccessService } from './quick-access.service'

@ApiTags('Context')
@Controller('context/quick-access')
export class QuickAccessController {
	constructor(private readonly quickAccessService: QuickAccessService) {}

	@ApiResponse({
		type: [QuickAccessResponseItem],
	})
	@Get()
	async getQuickAccesses() {
		return this.quickAccessService.getActiveQuickAccess()
	}

	@ApiResponse({
		type: [HeroResponseItem],
	})
	@Get('hero')
	async getHeroQuickAccesses() {
		return this.quickAccessService.getHeroQuickAccesses()
	}
}
