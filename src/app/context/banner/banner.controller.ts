import { Controller, Get } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { BannerResponse } from '../types/banner.types'
import { BannerService } from './banner.service'

@ApiTags('Context')
@Controller('context/banner')
export class BannerController {
	constructor(private readonly bannerService: BannerService) {}

	@ApiResponse({
		type: BannerResponse,
	})
	@Get('')
	async getBanners() {
		return this.bannerService.getActiveBannerUrls()
	}
}
