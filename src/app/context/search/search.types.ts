export type MeiliSearchResponse = {
	type: 'store' | 'giftCard' | 'offer'
	id: string
	uid: number
	active: boolean
	name?: string
	description: string
	detailedDescription?: string
	offerWarning?: string
	giftCard?: string
	newUserOffer?: string
	oldUserOffer?: string
	caption: string
	categories: Category[]
	subcategories: Category[]
	storecategories: Category[]
	url: string
	cashbackGiving?: number
	relatedInstantStore?: string
	title?: string
	offerType?: 'upto' | 'flat'
	couponCode?: string
	store?: string
	dateExpiry?: Date
	offerPercent?: number
	offerAmount?: number
}

export type Category = {
	name?: string
}
