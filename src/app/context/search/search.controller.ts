import { Controller, Get, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { SearchResponseItem } from '../types/search.types'
import { SearchService } from './search.service'

@ApiTags('Context')
@Controller('context/search')
export class SearchController {
	constructor(private readonly searchService: SearchService) {}

	@ApiResponse({
		type: SearchResponseItem,
	})
	@Get()
	async getSearchResults(@Query('text') searchParam: string) {
		return this.searchService.searchResults(searchParam)
	}
}
