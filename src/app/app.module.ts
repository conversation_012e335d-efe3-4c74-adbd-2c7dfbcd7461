import { HttpLoggerMiddleware } from '@nest-toolbox/http-logger-middleware'
import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common'
import { APP_GUARD } from '@nestjs/core'
import { MongooseModule } from '@nestjs/mongoose'
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler'
import { env } from 'config'
import { App<PERSON>ontroller } from './app.controller'
import { AuthModule } from './auth/auth.module'
import { BotModule } from './bot/bot.module'
import { CardModule } from './card/card.module'
import { ChatModule } from './chat/chat.module'
import { ClickModule } from './click/click.module'
import { MissingCashbackModule } from './click/missing-cashback/missing-cashback.module'
import { ContextModule } from './context/context.module'
import { GiftCardModule } from './gift-card/gift-card.module'
import { LinkModule } from './link/link.module'
import { OfferModule } from './offer/offer.module'
import { PaymentModule } from './payment/payment.module'
import { SavedItemModule } from './saved-item/saved-item.module'
import { SitemapModule } from './sitemap/sitemap.module'
import { StoreModule } from './store/store.module'
import { StoreCategoryModule } from './store-category/store-category.module'
import { UserModule } from './user/user.module'
@Module({
	imports: [
		ContextModule,
		AuthModule,
		UserModule,
		StoreModule,
		GiftCardModule,
		OfferModule,
		ClickModule,
		MissingCashbackModule,
		StoreCategoryModule,
		PaymentModule,
		SavedItemModule,
		CardModule,
		LinkModule,
		MongooseModule.forRoot(env.DB.hostUrl, { dbName: env.DB.name }),
		ThrottlerModule.forRoot([
			{
				ttl: 6000,
				limit: 350,
			},
		]),
		SitemapModule,
		BotModule,
		ChatModule,
		// SeedModule,
		// MigrationModule,
	],
	controllers: [AppController],
	providers: [
		{
			provide: APP_GUARD,
			useClass: ThrottlerGuard,
		},
	],
})
export class AppModule implements NestModule {
	configure(consumer: MiddlewareConsumer): void {
		if (env.APP.nodeEnv !== 'production') {
			consumer.apply(HttpLoggerMiddleware).forRoutes({
				path: '*',
				method: RequestMethod.ALL,
			})
		}
	}
}
