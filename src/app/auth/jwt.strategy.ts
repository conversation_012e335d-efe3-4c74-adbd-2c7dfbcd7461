import { Injectable, UnauthorizedException } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { Strategy } from 'passport-custom'
import { ReqUser } from 'shared/entities'
import { AuthService } from './auth.service'

// Importing necessary modules and dependencies
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt-session') {
	constructor(private readonly authService: AuthService) {
		super()
	}

	// biome-ignore lint/suspicious/noExplicitAny: required for external API
	async validate(request: any): Promise<ReqUser> {
		const { token } = request.query
		const user = await this.authService.verifyToken(token)
		if (!user) {
			throw new UnauthorizedException()
		}

		return { email: user.email, id: user._id }
	}
}
