import { Injectable, Logger } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { env } from 'config'
import { Request } from 'express'
import { Profile, Strategy, VerifyCallback } from 'passport-google-oauth20'
import { AuthService } from './auth.service'

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
	private readonly logger = new Logger(GoogleStrategy.name)

	constructor(private readonly authService: AuthService) {
		super({
			// biome-ignore lint/style/useNamingConvention: required for external API
			clientID: env.GOOGLE_OAUTH.clientId,
			clientSecret: env.GOOGLE_OAUTH.clientSecret,
			// biome-ignore lint/style/useNamingConvention: required for external API
			callbackURL: env.GOOGLE_OAUTH.callbackUrl,
			scope: ['email', 'profile'],
		})
	}

	async validate(
		_accessToken: string,
		_refreshToken: string,
		profile: Profile,
		done: VerifyCallback,
		_req?: Request
	) {
		try {
			const { id, name, emails, photos } = profile

			this.logger.log(`Google profile received: ${id}`)

			if (!emails || emails.length === 0) {
				this.logger.error('Google authentication failed: No email provided')
				return done(new Error('Google authentication failed: No email provided'), false)
			}

			// Safely construct the name, handling potential missing values
			const firstName = name?.givenName || ''
			const lastName = name?.familyName || ''
			const fullName = firstName + (firstName && lastName ? ' ' : '') + lastName || 'Google User'

			// Get avatar if available
			const avatar = photos && photos.length > 0 && photos[0]?.value ? photos[0].value : ''

			// Ensure we have a valid email
			if (!emails?.[0]?.value) {
				this.logger.error('Google authentication failed: No valid email provided')
				return done(new Error('Google authentication failed: No valid email provided'), false)
			}

			const user = {
				googleId: id,
				email: emails[0].value,
				name: fullName,
				avatar,
			}
			return done(null, user)
		} catch (error) {
			this.logger.error(`Error in Google strategy validation: ${error.message}`)
			return done(error, false)
		}
	}
}
