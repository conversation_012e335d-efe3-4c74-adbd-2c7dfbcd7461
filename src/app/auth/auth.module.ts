import { BotModule } from '@app/bot/bot.module'
import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { PassportModule } from '@nestjs/passport'
import { UserModule } from 'app/user/user.module'
import { CookieSession, CookieSessionSchema } from 'shared/entities/cookie-session.entity'
import { Otp, OtpSchema } from 'shared/entities/otp.entity'
import { User, UserSchema } from 'shared/entities/user.entity'
import { SharedServicesModule } from 'shared/services/shared-services.module'
import { AuthController } from './auth.controller'
import { AuthService } from './auth.service'
import { GoogleStrategy } from './google.strategy'
import { JwtStrategy } from './jwt.strategy'
import { OtpStrategy } from './otp-strategy'
import { SessionSerializer } from './session.serializer'

// NOTE - only the Offer based entity is being used in the Offers module.
// The other entities are needed to be imported in the respective modules.
@Module({
	imports: [
		BotModule,
		UserModule,
		SharedServicesModule,
		PassportModule.register({ session: true }),
		MongooseModule.forFeature([
			{
				name: User.name,
				schema: UserSchema,
			},
			{
				name: Otp.name,
				schema: OtpSchema,
			},
			{
				name: CookieSession.name,
				schema: CookieSessionSchema,
			},
		]),
	],
	controllers: [AuthController],
	providers: [AuthService, OtpStrategy, GoogleStrategy, JwtStrategy, SessionSerializer],
	exports: [AuthService],
})
export class AuthModule {}
