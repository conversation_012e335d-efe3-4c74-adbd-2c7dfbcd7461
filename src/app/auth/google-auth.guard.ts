import { ExecutionContext, Injectable, Logger } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'

@Injectable()
export class GoogleAuthGuard extends AuthGuard('google') {
	private readonly logger = new Logger(GoogleAuthGuard.name)

	async canActivate(context: ExecutionContext) {
		try {
			// For the initial /auth/google route, we just want to redirect to Google
			const request = context.switchToHttp().getRequest()
			const isCallback = request.url.includes('/google/callback')

			if (!isCallback) {
				this.logger.log('Starting Google authentication flow')
				return super.canActivate(context) as Promise<boolean>
			}

			// For the callback route, handle the authentication result
			this.logger.log('Processing Google authentication callback')
			const result = (await super.canActivate(context)) as boolean
			this.logger.log(`Authentication result: ${result}`)

			if (result) {
				// If authentication was successful, log the user in
				await super.logIn(request)
				this.logger.log('User logged in successfully')
			} else {
				this.logger.error('Authentication failed')
			}

			return result
		} catch (error) {
			this.logger.error(`Error in Google authentication: ${error.message}`)
			throw error
		}
	}
}
