import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'

export class UserResponse {
	@ApiProperty({ type: String })
	message!: string | undefined

	@ApiProperty({ type: String, required: false })
	@IsString()
	credential: string | undefined
}

export class LoginResponse {
	@ApiProperty({ type: String })
	message!: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	email?: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	mobile?: string
}
