import { Body, Controller, Delete, Get, Post, Query, Req, Res, UseGuards } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { Throttle } from '@nestjs/throttler'
import { env } from 'config'
import { Request as ExpressRequest, Response } from 'express'
import { Auth, ClientInfo, ClientInfoData, User } from 'shared/decorators'

import { ReqUser, UserDocument } from 'shared/entities'

import { AuthService } from './auth.service'

import { CreateUserDto } from './dto/create-user.dto'
import { LoginDto } from './dto/login.dto'
import { VerifyUserDto } from './dto/verify-otp.dto'
import { GoogleAuthGuard } from './google-auth.guard'
import { JwtAuthGuard } from './jwt-auth.guard'
import { OtpAuthGuard } from './otp-auth.guard'
import { LoginResponse, UserResponse } from './types/user.response'

// Extend the Express Request type to include the user property
interface Request extends ExpressRequest {
	user?: UserDocument
}

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
	constructor(private readonly authService: AuthService) {}

	@ApiResponse({
		type: UserResponse,
	})
	@Post('/register')
	@Throttle({ default: { limit: 500, ttl: 5 * 60 * 1000 } })
	async create(@Body() user: CreateUserDto) {
		return await this.authService.create(user)
	}

	@ApiResponse({
		type: LoginResponse,
	})
	@Post('/login')
	@Throttle({ default: { limit: 500, ttl: 5 * 60 * 1000 } })
	async login(@Body() loginDto: LoginDto) {
		return this.authService.login(loginDto)
	}

	@ApiResponse({
		type: Boolean,
	})
	@Post('/verify-user')
	@UseGuards(OtpAuthGuard)
	async verifyOtp(
		@Body() _verifyOtpDto: VerifyUserDto,
		@Res() response: Response,
		@ClientInfo() clientInfo: ClientInfoData,
		@User() user: ReqUser
	) {
		await this.authService.saveSession(user, clientInfo)
		return response.send(true)
	}

	@ApiResponse({
		type: LoginResponse,
	})
	@Post('/resend-otp')
	@Throttle({ default: { limit: 500, ttl: 5 * 60 * 1000 } })
	async resendOtp(@Body() loginDto: LoginDto) {
		return this.authService.resendOtp(loginDto)
	}

	@ApiResponse({ status: 200 })
	@Auth()
	@Delete('/logout')
	async logout(@User() user: ReqUser, @Req() request: Request, @Res() response: Response) {
		await this.authService.logout(user.id)
		// Passport logout to remove the user from the session
		request.logout(() => {})

		// Optional: If you're using express-session, you can destroy the session like this
		request.session.destroy((err: Error) => {
			if (err) {
			}
		})
		response.clearCookie('accessToken')

		// Redirect or inform the user they are logged out
		return response.send('Logged out successfully.')
	}

	@ApiResponse({ status: 200 })
	@Auth()
	@Get('/check')
	async check() {
		return true
	}

	@Get('/token')
	// biome-ignore lint/suspicious/noExplicitAny: required for external API
	getCsrfToken(@Req() req: any): any {
		return {
			token: req.csrfToken(),
		}
	}

	@ApiResponse({
		description: 'Initiates Google OAuth authentication flow',
	})
	@Get('/google')
	// @UseGuards(GoogleAuthGuard)
	async googleAuth(@Res() res: Response, @Query('referralCode') referralCode?: string) {
		// This route initiates the Google OAuth flow
		// If a referral code is provided, we'll store it in the state parameter
		const state = referralCode ? JSON.stringify({ referralCode }) : undefined
		// We need to manually handle the redirect to include the state parameter
		const authUrl = `${env.GOOGLE_OAUTH.authUrl}?client_id=${
			env.GOOGLE_OAUTH.clientId
		}&redirect_uri=${encodeURIComponent(
			env.GOOGLE_OAUTH.callbackUrl
		)}&response_type=code&scope=email%20profile&state=${encodeURIComponent(state || '')}`
		return res.redirect(authUrl)
	}

	@ApiResponse({
		description: 'Handles the callback from Google OAuth authentication',
	})
	@Get('/google/callback')
	@UseGuards(GoogleAuthGuard)
	async googleAuthCallback(
		@Req() req: Request,
		@Res() res: Response,
		@ClientInfo() clientInfo: ClientInfoData,
		@Query('redirect') _redirectUrl?: string,
		@Query('state') stateParam?: string
	) {
		try {
			if (!req.user) {
				throw new Error('Authentication failed: No user in request')
			}

			// Extract referral code from state parameter if available
			let referralCode: string | undefined
			if (stateParam) {
				try {
					// Try to parse the state parameter
					const state = JSON.parse(decodeURIComponent(stateParam))
					referralCode = state.referralCode
				} catch (_e) {
					// Continue even if state parsing fails
				}
			}

			const user = await this.authService.validateGoogleUser(
				req.user as unknown as {
					googleId: string
					email: string
					name: string
					avatar: string
				},
				referralCode
			)

			// If we have a referral code, update the user
			// if (referralCode) {
			// 	await this.authService.updateUserReferral(user._id, referralCode)
			// }

			// Save the session
			const token = await this.authService.saveGoogleSession(user, clientInfo)

			// Get the frontend URL for redirection
			const frontendUrl = `https://www.indiancashback.com?token=${token}`

			// Set a cookie to indicate authentication (visible to JavaScript)
			return res.redirect(frontendUrl)
		} catch (_error) {
			// Redirect to frontend with error
			// Using direct URL instead of constructing from env to avoid domain issues
			return res.redirect('https://www.indiancashback.com/')
		}
	}

	// verify token after google auth callback
	@UseGuards(JwtAuthGuard)
	@Get('/verify-token')
	async verifyToken(@Query('token') _token: string, @Res() res: Response) {
		return res.send(true)
	}
}
