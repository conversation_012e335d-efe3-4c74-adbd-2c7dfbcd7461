import { BotService } from '@app/bot/bot.service'
import { ReferralCommissionService } from '@app/user/referral-commission.service'
import {
	BadRequestException,
	ForbiddenException,
	Injectable,
	InternalServerErrorException,
	Logger,
	NotAcceptableException,
	NotFoundException,
	UnauthorizedException,
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { UserService } from 'app/user/user.service'
import { env } from 'config'
import jwt, { JwtPayload } from 'jsonwebtoken'
import { Model, Types } from 'mongoose'
import { ClientInfoData } from 'shared/decorators'
import { CookieSession, CookieSessionDocument } from 'shared/entities/cookie-session.entity'
import { Otp, OtpDocument } from 'shared/entities/otp.entity'
import { ReqUser, User, UserDocument } from 'shared/entities/user.entity'
import { OtpSmsPayload, smsOtp } from 'shared/functions'
import { createSubscriber, sendTransactionalEmail } from 'shared/functions/listmonk/base'
import { OtpVerificationPayload, sendOtpVerificationMail } from 'shared/functions/mails/brevo'
import { EmailNormalizationService } from 'shared/services/email-normalization.service'
import { Status } from '../../shared/types'
import { CreateUserDto } from './dto/create-user.dto'
import { LoginDto } from './dto/login.dto'
import { VerifyUserDto } from './dto/verify-otp.dto'
import { LoginResponse } from './types/user.response'

@Injectable()
export class AuthService {
	private readonly logger = new Logger(AuthService.name)

	constructor(
		@InjectModel(CookieSession.name)
		private cookieSession: Model<CookieSessionDocument>,

		@InjectModel(Otp.name)
		private otp: Model<OtpDocument>,

		@InjectModel(User.name)
		private user: Model<UserDocument>,

		private userService: UserService,
		private emailNormalizationService: EmailNormalizationService,
		private referralCommissionService: ReferralCommissionService,
		private botService: BotService
	) {}

	/**
	 *  login
	 * @param email
	 * @returns  { success : true }
	 */
	async login(loginDto: LoginDto): Promise<LoginResponse> {
		const loginField = await this.sendOtp(loginDto)

		return {
			message: ` ${
				loginField === 'mobile'
					? ' an sms has been sent to your mobile, please use it to verify '
					: 'a mail has been sent to your email m please use it to verify'
			}`,
			email: loginDto?.email ? loginDto.email : '',
			mobile: loginDto?.mobile ? loginDto.mobile : '',
		}
	}

	/**
	 *  create user
	 * @param user
	 * @returns  Promise<User>
	 */
	async create(user: CreateUserDto): Promise<LoginResponse> {
		// Validate email and check for conflicts (including Gmail aliases)
		const emailValidation = await this.emailNormalizationService.validateEmailForRegistration(
			user.email
		)
		if (!emailValidation.isValid) {
			throw new BadRequestException(emailValidation.error)
		}

		// Check for mobile conflicts
		if (user.mobile) {
			const existingUserWithPhone = await this.user.findOne({
				mobile: user.mobile,
			})
			if (existingUserWithPhone) {
				throw new BadRequestException('User already exists with this mobile')
			}
		}

		// Find referral user if referral code provided
		const referralUser = await this.user.findOne({
			referralCode: user.referralCode,
		})

		// Check for self-referral through Gmail aliases
		if (
			referralUser &&
			(await this.emailNormalizationService.checkSelfReferralByAlias(
				user.email,
				referralUser.email
			))
		) {
			throw new BadRequestException('You cannot refer yourself')
		}

		// Set appropriate reward based on referral campaign rules
		const isCampaignActive = this.referralCommissionService.isCampaignActive()
		// Default: Rs. 25 sign up bonus for everyone
		let signupBonus = 25
		const referralBonus = 25

		// If campaign is active and user was referred, give Rs. 50 bonus
		if (isCampaignActive && referralUser) {
			signupBonus = 50
		}

		const createdUser = new this.user({
			...user,
			email: user.email, // Keep original email for delivery
			normalizedEmail: emailValidation.normalizedEmail, // Store normalized email
			referral: referralUser,
			totalEarned: 0, // Initialize total earned
		})
		await createdUser?.save()

		// Prepare alert message with referral information if available in HTML format
		let alertMessage = `<b>🆕 New User Created</b>\n• <b>Name:</b> ${createdUser.name}\n• <b>ID:</b> ${createdUser.uid}`

		// Add referral information if user was referred
		if (referralUser) {
			alertMessage += `\n\n<b>👥 Referred by</b>\n• <b>Name:</b> ${referralUser.name}\n• <b>ID:</b> ${referralUser.uid}`
		}

		await this.botService.sendNewUserAlert(alertMessage)

		// If user was referred during campaign, add Rs. 25 to referrer's pending rewards
		await this.referralCommissionService.createJoinEarning({
			userId: referralUser?._id,
			referralEarnings: referralBonus,
			newUserId: createdUser._id,
			newUserEarnings: signupBonus,
		})

		const generatedOtp = this.generateOtp()
		const newOtp = new this.otp({
			email: user.email,
			type: 'email',
			otp: generatedOtp,
		})
		await newOtp.save()

		const emailVerifyParams: OtpVerificationPayload = {
			to: user.email,
			otp: generatedOtp,
			name: user?.name,
		}

		//send mail
		await sendOtpVerificationMail(emailVerifyParams)

		// Create subscriber in listmonk
		try {
			await createSubscriber({
				email: user.email,
				name: user.name,
				referralCode: user.referralCode || '',
			})

			// Send welcome email
			await sendTransactionalEmail({
				subscriberEmail: user.email,
				templateId: 5, // Signup Welcome template
				data: {
					name: user.name,
					amount: signupBonus,
				},
			})

			// If user was referred, send referral welcome email
			if (referralUser?.email) {
				await sendTransactionalEmail({
					subscriberEmail: referralUser.email,
					templateId: 6, // Referral Welcome template
					data: {
						referrerName: referralUser.name,
						referredName: user.name,
						amount: referralBonus,
					},
				})
			}
		} catch (emailError) {
			this.logger.error('Error sending welcome email during user creation', {
				email: user.email,
				error: emailError.message,
				stack: emailError.stack,
			})
			// Continue even if email fails
		}

		return {
			message: 'Please check your email for the OTP.',
			email: user.email,
		}
	}

	async validateUser({ email, mobile, otp }: VerifyUserDto): Promise<UserDocument> {
		const field = email ? 'email' : mobile ? 'mobile' : null
		const searchField = email ? { email } : { mobile }
		const [userData, otpData] = await Promise.all([
			searchField.email
				? this.userService.getUserByEmail(email)
				: this.userService.getUserByMobile(mobile),
			this.otp.findOne({ ...searchField, otp }).sort({ createdAt: -1 }),
		])

		if (userData?.status === Status.blocked) {
			throw new UnauthorizedException('Your account is restricted. Please contact support.')
		}

		if (!userData) {
			if (!field) {
				throw new BadRequestException('Either email or mobile must be provided')
			}
			throw new NotAcceptableException(
				`User with this ${field} isn't registered, Please try again!.`
			)
		}

		if (!otpData) {
			throw new NotAcceptableException('Invalid OTP, Please try again!.')
		}

		if (this.checkIfOtpIsExpired(otpData)) {
			throw new NotAcceptableException('OTP Expired, Please try again!.')
		}

		const updates = {
			...(userData.status === Status.inactive && { status: Status.active }),
			...(userData.mobileVerified === false && field === 'mobile' && { mobileVerified: true }),
		}

		if (Object.keys(updates).length > 0) {
			await this.userService.activateUser(userData.email, updates)
		}
		await this.otp.deleteOne({ _id: otpData._id })

		return userData
	}

	async saveSession(user: ReqUser, client: ClientInfoData): Promise<boolean> {
		const session: CookieSession = {
			browser: client.browser,
			device: client.device,
			ip: client.ip,
			os: client.os,
			status: Status.active,
			user: user.id,
			userAgent: client.userAgent,
		}
		const newSession = new this.cookieSession(session)
		await newSession.save()
		return true
	}

	checkIfOtpIsExpired(otpDoc: OtpDocument): boolean {
		// Check if OTP is expired
		const otpExpiration = 5 * 60 * 1000 // 5 minutes in milliseconds
		if (Date.now() - otpDoc.createdAt.valueOf() > otpExpiration) {
			// OTP is expired
			return true
		}
		return false
	}

	generateOtp(): number {
		return Math.floor(1000 + Math.random() * 9000)
	}

	async sendOtp(loginDto: LoginDto): Promise<string> {
		let existingUser: User | null
		let loginField: string

		// Determine which field the user has provided
		if (loginDto.email) {
			existingUser = await this.user.findOne({
				email: { $regex: loginDto.email, $options: 'i' },
			})
			loginField = 'email'
		} else if (loginDto.mobile) {
			existingUser = await this.user.findOne({ mobile: loginDto.mobile })
			loginField = 'mobile'
		} else {
			// Neither email nor mobile provided
			throw new NotAcceptableException('Either email or mobile must be provided')
		}

		if (!existingUser) {
			throw new BadRequestException('You are not registered, please Sign Up')
		}

		if (existingUser.status === Status.blocked) {
			throw new BadRequestException('Your account is restricted. Please contact support.')
		}

		const generatedOtp = this.generateOtp()

		if (loginDto?.email && loginField === 'email') {
			const emailVerifyParams: OtpVerificationPayload = {
				to: loginDto.email,
				otp: generatedOtp,
				name: existingUser?.name,
			}

			const newOtp = new this.otp({
				email: loginDto.email,
				otp: generatedOtp,
			})

			await newOtp.save()
			await sendOtpVerificationMail(emailVerifyParams)
		}
		if (loginDto?.mobile && loginField === 'mobile') {
			const phoneVerifyParams: OtpSmsPayload = {
				to: Number.parseInt(loginDto.mobile, 10),
				otp: generatedOtp,
			}
			const newOtp = new this.otp({
				mobile: loginDto.mobile,
				otp: generatedOtp,
			})

			await newOtp.save()
			await smsOtp(phoneVerifyParams)
		}
		return loginField
	}

	async resendOtp(loginDto: LoginDto): Promise<LoginResponse> {
		const loginField = await this.sendOtp(loginDto)

		return {
			message: ` ${
				loginField === 'mobile'
					? 'An sms has been sent to your mobile, please use it to verify '
					: 'A mail has been sent to your email, please use it to verify'
			}`,
			email: loginDto?.email ? loginDto.email : '',
			mobile: loginDto?.mobile ? loginDto.mobile : '',
		}
	}

	async logout(userId: Types.ObjectId): Promise<void> {
		await this.cookieSession.updateMany({ user: userId }, { status: Status.inactive })
	}

	// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex authentication logic - refactoring would break functionality
	async validateGoogleUser(
		googleUserData: {
			googleId: string
			email: string
			name: string
			avatar: string
		},
		referralCode?: string
	): Promise<UserDocument> {
		try {
			// Use email normalization service to find user
			let user = await this.emailNormalizationService.findUserByEmail(googleUserData.email)

			if (user?.status === Status.blocked) {
				throw new UnauthorizedException('Your account is restricted. Please contact support.')
			}

			let _isNewUser = false
			let referralUser = null

			// If referral code is provided, find the referrer
			if (referralCode) {
				referralUser = await this.user.findOne({
					referralCode,
				})

				// Check for self-referral through Gmail aliases
				if (
					referralUser &&
					(await this.emailNormalizationService.checkSelfReferralByAlias(
						googleUserData.email,
						referralUser.email
					))
				) {
					throw new BadRequestException('You cannot refer yourself')
				}
			}

			if (user) {
				// Update the user's Google ID if it's not set
				if (!user.googleId) {
					user.googleId = googleUserData.googleId
					// If avatar URL is provided, set it
					if (googleUserData.avatar) {
						user.avatar = {
							publicId: googleUserData.googleId, // Use googleId as publicId
							secureUrl: googleUserData.avatar,
						}
					}
					await user.save()
				}

				// If user exists but is inactive, activate them
				if (user.status === Status.inactive) {
					user.status = Status.active
					await user.save()
				}
			} else {
				_isNewUser = true

				// Validate email for new Google user (check for conflicts)
				const emailValidation = await this.emailNormalizationService.validateEmailForRegistration(
					googleUserData.email
				)
				if (!emailValidation.isValid) {
					throw new BadRequestException(emailValidation.error)
				}

				// Set appropriate reward based on referral campaign rules
				const isCampaignActive = this.referralCommissionService.isCampaignActive()
				// Default: Rs. 25 sign up bonus for everyone
				let signupBonus = 25
				const referralBonus = 25

				// If campaign is active and user was referred, give Rs. 50 bonus
				if (isCampaignActive && referralUser) {
					signupBonus = 50
				}

				// Create a new user with Google data
				const newUser = new this.user({
					email: googleUserData.email,
					normalizedEmail: emailValidation.normalizedEmail,
					name: googleUserData.name,
					googleId: googleUserData.googleId,
					status: Status.active,
					referral: referralUser?._id,
					totalEarned: 0, // Initialize total earned
				})

				// If avatar URL is provided, set it
				if (googleUserData.avatar) {
					newUser.avatar = {
						publicId: googleUserData.googleId, // Use googleId as publicId
						secureUrl: googleUserData.avatar,
					}
				}

				user = await newUser.save()

				// Prepare alert message with referral information if available in HTML format
				let alertMessage = `<b>🆕 New Google User Created</b>\n• <b>Name:</b> ${user.name}\n• <b>ID:</b> ${user.uid}`

				// Add referral information if user was referred
				if (referralUser) {
					alertMessage += `\n\n<b>👥 Referred by</b>\n• <b>Name:</b> ${referralUser.name}\n• <b>ID:</b> ${referralUser.uid}`
				}

				// Send alert to bot
				await this.botService.sendNewUserAlert(alertMessage)

				// Create subscriber in listmonk
				try {
					await createSubscriber({
						email: user.email,
						name: user.name,
						referralCode: user.referralCode || '',
					})

					// Send welcome email
					await sendTransactionalEmail({
						subscriberEmail: user.email,
						templateId: 5, // Signup Welcome template
						data: {
							name: user.name,
							amount: signupBonus,
						},
					})

					// If user was referred, send referral welcome email
					if (referralUser?.email) {
						await sendTransactionalEmail({
							subscriberEmail: referralUser.email,
							templateId: 6, // Referral Welcome template
							data: {
								referrerName: referralUser.name,
								referredName: user.name,
								amount: referralBonus,
							},
						})
					}
				} catch (emailError) {
					this.logger.error('Error sending welcome email for Google user', {
						email: user.email,
						error: emailError.message,
						stack: emailError.stack,
					})
					// Continue even if email fails
				}

				// If user was referred during campaign, create referral earnings
				await this.referralCommissionService.createJoinEarning({
					userId: referralUser?._id,
					referralEarnings: referralBonus,
					newUserId: user._id,
					newUserEarnings: signupBonus,
				})
			}

			return user
		} catch (error) {
			this.logger.error('Google user validation failed', {
				email: googleUserData.email,
				name: googleUserData.name,
				googleId: googleUserData.googleId,
				hasReferralCode: !!referralCode,
				error: error.message,
				stack: error.stack,
			})
			throw new InternalServerErrorException('Failed to authenticate with Google')
		}
	}

	async saveGoogleSession(user: UserDocument, clientInfo: ClientInfoData): Promise<string> {
		const token = jwt.sign({ email: user.email }, env.JWT.secret, {
			expiresIn: '5m',
		})

		const session: CookieSession = {
			browser: clientInfo.browser,
			device: clientInfo.device,
			ip: clientInfo.ip,
			os: clientInfo.os,
			status: Status.active,
			user: user._id,
			token,
			userAgent: clientInfo.userAgent,
		}
		const newSession = new this.cookieSession(session)
		await newSession.save()
		return token
	}

	async verifyToken(token: string): Promise<UserDocument> {
		try {
			const session = await this.cookieSession.findOne({ token })
			if (!session) {
				throw new NotFoundException('Session not found')
			}
			const decoded = jwt.verify(token, env.JWT.secret) as JwtPayload
			const user = await this.user.findOne({ email: decoded.email })
			if (!user) {
				throw new NotFoundException('User not found')
			}

			await this.cookieSession.updateOne({ _id: session._id }, { token: '' })
			return user
		} catch (_error) {
			throw new ForbiddenException('Invalid token')
		}
	}

	/**
	 * Update a user's referral information
	 * @param userId The ID of the user to update
	 * @param referralCode The referral code to use
	 * @returns True if the update was successful
	 */
	async updateUserReferral(userId: Types.ObjectId, referralCode: string): Promise<boolean> {
		try {
			// Find the referrer user by referral code
			const referrer = await this.user.findOne({ referralCode })

			if (!referrer) {
				this.logger.error('No user found with referral code', {
					referralCode,
					userId: userId.toString(),
				})
				return false
			}

			// Update the user with the referrer
			await this.user.findByIdAndUpdate(userId, { referral: referrer._id })
			return true
		} catch (error) {
			this.logger.error('Error updating user referral', {
				userId: userId.toString(),
				referralCode,
				error: error.message,
				stack: error.stack,
			})
			return false
		}
	}
}
