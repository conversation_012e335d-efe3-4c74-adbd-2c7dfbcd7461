import { Injectable, Logger } from '@nestjs/common'
import { PassportSerializer } from '@nestjs/passport'
import { Types } from 'mongoose'
import { ReqUser, UserDocument } from 'shared/entities'

@Injectable()
export class SessionSerializer extends PassportSerializer {
	private readonly logger = new Logger(SessionSerializer.name)

	serializeUser(
		user: UserDocument | ReqUser,
		// biome-ignore lint/suspicious/noExplicitAny: required for passport serialization
		done: (err: Error | null, user: Record<string, any>) => void
	): void {
		try {
			// Handle both UserDocument and ReqUser types
			if ('_id' in user) {
				// It's a UserDocument
				const sessionUser = {
					id: user._id,
					email: user.email,
				}
				this.logger.log(`Serializing user: ${user.email}`)
				done(null, sessionUser)
			} else {
				// It's already a ReqUser
				this.logger.log(`Serializing ReqUser: ${user.email}`)
				done(null, user)
			}
		} catch (error) {
			this.logger.error(`Error serializing user: ${error.message}`)
			// biome-ignore lint/suspicious/noExplicitAny: required for passport serialization
			done(error, {} as Record<string, any>)
		}
	}

	deserializeUser(
		// biome-ignore lint/suspicious/noExplicitAny: required for passport serialization
		payload: Record<string, any>,
		// biome-ignore lint/suspicious/noExplicitAny: required for passport serialization
		done: (err: Error | null, payload: Record<string, any>) => void
	): void {
		try {
			// Ensure we have a valid user object
			if (!(payload && (payload.id || payload.email))) {
				this.logger.error('Invalid user payload for deserialization')
				// biome-ignore lint/suspicious/noExplicitAny: required for passport serialization
				done(new Error('Invalid user payload'), {} as Record<string, any>)
				return
			}

			// Convert string ID to ObjectId if needed
			if (payload.id && typeof payload.id === 'string') {
				payload.id = new Types.ObjectId(payload.id)
			}

			this.logger.log(`Deserializing user: ${payload.email || 'Unknown'}`)
			done(null, payload)
		} catch (error) {
			this.logger.error(`Error deserializing user: ${error.message}`)
			// biome-ignore lint/suspicious/noExplicitAny: required for passport serialization
			done(error, {} as Record<string, any>)
		}
	}
}
