import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'

export class CreateUserDto {
	@ApiProperty({
		example: '<PERSON>',
		description: 'User Name',
		required: true,
	})
	@IsString()
	@IsNotEmpty()
	name!: string

	@ApiProperty({
		example: '<EMAIL>',
		description: 'User Email',
		required: true,
	})
	@IsEmail()
	email!: string

	@ApiProperty({ example: 1_234_567_890, description: 'Mobile Number' })
	@IsNumber()
	@IsOptional()
	mobile?: number

	@ApiProperty({ example: 'Some referral code', description: 'Referral Code' })
	@IsString()
	@IsOptional()
	referralCode?: string
}
