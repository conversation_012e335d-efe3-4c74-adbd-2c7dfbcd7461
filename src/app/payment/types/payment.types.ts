import { ApiProperty } from '@nestjs/swagger'
import { IsEnum } from 'class-validator'
import { PaginationResponseType } from 'shared/dto'
import { Payment, PaymentStatus, PaymentStatusType, PaymentTypes } from 'shared/types'

export class PaymentType {
	@ApiProperty({ type: String })
	referenceId: string

	@ApiProperty({ type: Number })
	withdrawAmount: number

	@IsEnum(Payment)
	@ApiProperty({
		enum: Payment,
	})
	paymentType: PaymentTypes

	@IsEnum(PaymentStatus)
	@ApiProperty({
		enum: PaymentStatus,
	})
	status?: PaymentStatusType

	@ApiProperty({ type: Number })
	paymentDate: number
}

export class GetPaymentListResponse {
	@ApiProperty({ type: [PaymentType] })
	payments: PaymentType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}
