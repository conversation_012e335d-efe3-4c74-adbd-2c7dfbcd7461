import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'

import { Auth, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { GetPaymentRequestDto, PaymentRequestDto } from './dto/payment.dto'
import { PaymentService } from './payment.service'
import { GetPaymentListResponse } from './types/payment.types'

@ApiTags('PaymentRequest')
@Controller('payment')
export class PaymentController {
	constructor(private readonly paymentService: PaymentService) {}

	@ApiResponse({
		type: <PERSON><PERSON><PERSON>,
	})
	@Auth()
	@Post('withdraw')
	async requestPayments(@Body() paymentData: PaymentRequestDto, @User() user: ReqUser) {
		return await this.paymentService.requestPayments(paymentData, user)
	}

	@ApiResponse({
		type: GetPaymentListResponse,
	})
	@Auth()
	@Get('')
	async getAllPaymentRequestedUser(
		@Query() queryParams: GetPaymentRequestDto,
		@User() user: ReqUser
	) {
		return await this.paymentService.getAllPaymentRequestedUser(queryParams, user)
	}
}
