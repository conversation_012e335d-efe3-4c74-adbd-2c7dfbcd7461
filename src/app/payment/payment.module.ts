//NOTE - only the Offer based entity is being used in the Offers module.

import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { BotModule } from 'app/bot/bot.module'
import { UserModule } from 'app/user/user.module'
import { PaymentRequest, PaymentRequestSchema } from 'shared/entities/payment-request.entity'
import { PaymentController } from './payment.controller'
import { PaymentService } from './payment.service'

// The other entities are needed to be imported in the respective modules.
@Module({
	imports: [
		UserModule,
		BotModule,
		MongooseModule.forFeature([
			{
				name: PaymentRequest.name,
				schema: PaymentRequestSchema,
			},
		]),
	],
	controllers: [PaymentController],
	providers: [PaymentService],
	exports: [PaymentService],
})
export class PaymentModule {}
