import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Min } from 'class-validator'
import { PaginationDto } from 'shared/dto'
import { PaymentSortTypes, PaymentTypes } from 'shared/enums'
import { PaymentStatus } from 'shared/types'

export class PaymentRequestDto {
	@IsNumber()
	@Min(200)
	@ApiProperty({
		required: true,
		minimum: 100,
		description: 'The amount to withdraw',
	})
	withdrawAmount: number

	@ApiProperty({
		example: 'bank',
		required: true,
		enum: PaymentTypes,
		enumName: 'PaymentTypes',
		description: 'The type of payment',
	})
	@IsString()
	@IsEnum(PaymentTypes)
	@IsNotEmpty()
	paymentType!: PaymentTypes
}
export class GetPaymentRequestDto extends PaginationDto {
	@IsOptional()
	@IsString()
	@ApiPropertyOptional({
		example: 'a',
		required: false,
	})
	searchParam?: string

	@IsEnum(PaymentSortTypes)
	@IsOptional()
	@ApiProperty({ enum: PaymentSortTypes, enumName: 'PaymentSortTypes' })
	sortType?: PaymentSortTypes

	@ApiProperty({
		enum: PaymentTypes,
		enumName: 'PaymentTypes',
	})
	@IsOptional()
	@IsString()
	@IsEnum(PaymentTypes)
	paymentType!: PaymentTypes

	@IsOptional()
	@Transform(({ value }) => {
		return value.split(',').map((val: string) => {
			if (val === PaymentStatus.requested) {
				return 'pending'
			}
			if (val === PaymentStatus.paid) {
				return 'approved'
			}
			return 'rejected'
		})
	})
	@IsArray()
	@IsNotEmpty()
	@ApiPropertyOptional({
		example: 'Requested,Paid,Cancelled',
		type: String,
		required: false,
	})
	status!: string

	@IsString()
	@IsOptional()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T00:00:00.000Z`
	})
	@ApiPropertyOptional({
		default: '2024-03-27T07:00:30.418Z',
	})
	startDate?: string

	@IsString()
	@IsOptional()
	@Transform(({ value }) => {
		return `${new Date(value).toISOString().split('T')[0]}T23:59:59.999Z`
	})
	@ApiPropertyOptional({
		default: '2024-03-27T07:00:31.418Z',
	})
	endDate?: string
}
