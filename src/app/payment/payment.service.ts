import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { BotService } from 'app/bot/bot.service'
import { UserService } from 'app/user/user.service'
import { Model, Types } from 'mongoose'
import { ReqUser, UserDocument } from 'shared/entities'
import { PaymentRequest, PaymentRequestDocument } from 'shared/entities/payment-request.entity'
import { buildPaymentSortQuery } from 'shared/helpers/query-builder'
import { convertLocalToUTC } from 'shared/helpers/time.helper'
import { Payment, PaymentStatus } from 'shared/types'
import { GetPaymentRequestDto, PaymentRequestDto } from './dto/payment.dto'
import { buildPaymentAggregateQuery } from './payment.helper'
import { GetPaymentListResponse } from './types/payment.types'

@Injectable()
export class PaymentService {
	private readonly logger = new Logger(PaymentService.name)

	constructor(
		@InjectModel(PaymentRequest.name)
		private paymentRequest: Model<PaymentRequestDocument>,
		private readonly userService: UserService,
		private readonly botService: BotService
	) {}

	/**
	 * Format withdrawal alert message with HTML markup
	 */
	private formatWithdrawalAlertMessage(
		user: UserDocument,
		paymentRequest: PaymentRequestDocument,
		paymentData: PaymentRequestDto
	): string {
		const paymentTypeMap = {
			bank: 'Bank Transfer',
			upi: 'UPI',
			giftVoucher: 'Gift Voucher',
			recharge: 'Mobile Recharge',
			wallet: 'Wallet',
		}

		const paymentTypeDisplay = paymentTypeMap[paymentData.paymentType] || paymentData.paymentType

		const timestamp = new Date().toLocaleString('en-US', {
			timeZone: 'Asia/Kolkata',
			year: 'numeric',
			month: 'short',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			hour12: true,
		})

		return `<b>💰 Withdrawal Request</b>

<b>👤 User Details:</b>
• <b>Name:</b> ${user.name}
• <b>User ID:</b> ${user.uid}

<b>💳 Payment Details:</b>
• <b>Amount:</b> ₹${paymentData.withdrawAmount}
• <b>Method:</b> ${paymentTypeDisplay}
• <b>Reference ID:</b> ${paymentRequest.referenceId}

<b>📅 Request Info:</b>
• <b>Timestamp:</b> ${timestamp}
• <b>Status:</b> Pending

<i>Withdrawal request submitted successfully</i>`
	}

	async requestPayments(paymentData: PaymentRequestDto, loggedUser: ReqUser) {
		const payment = await this.userService.withdrawBalance(
			loggedUser.id,
			paymentData.withdrawAmount,
			paymentData.paymentType
		)

		if (payment) {
			const paymentRequest = new this.paymentRequest({
				uid: 0,
				referenceId: 'PYMT',
				withdrawer: new Types.ObjectId(loggedUser.id),
				withdrawAmount: paymentData.withdrawAmount,
				paymentType: paymentData.paymentType,
				status: 'pending',
				requestedDate: convertLocalToUTC(),
			})
			const savedPaymentRequest = await paymentRequest.save()

			// Send withdrawal notification to Telegram
			try {
				// Get user details for the notification
				const user = await this.userService.getUserByEmail(loggedUser.email)
				if (user) {
					const alertMessage = this.formatWithdrawalAlertMessage(
						user,
						savedPaymentRequest,
						paymentData
					)

					await this.botService.sendWithdrawalAlert(alertMessage)

					this.logger.log('Withdrawal notification sent successfully', {
						userId: loggedUser.id,
						referenceId: savedPaymentRequest.referenceId,
						amount: paymentData.withdrawAmount,
						paymentType: paymentData.paymentType,
					})
				}
			} catch (error) {
				// Log error but don't fail the withdrawal request
				this.logger.error('Failed to send withdrawal notification', {
					userId: loggedUser.id,
					referenceId: savedPaymentRequest.referenceId,
					amount: paymentData.withdrawAmount,
					paymentType: paymentData.paymentType,
					error: error.message,
					stack: error.stack,
				})
			}
		}

		return true
	}
	async getAllPaymentRequestedUser(
		queryParams: GetPaymentRequestDto,
		userData: ReqUser
	): Promise<GetPaymentListResponse> {
		const page = queryParams.page ?? 1
		const skip = (page - 1) * queryParams.pageSize
		const limit = queryParams.pageSize
		const status = queryParams.status
		const paymentType = queryParams.paymentType
		const paymentStartDate = queryParams.startDate
		const paymentEndDate = queryParams.endDate
		const queryConditions = queryParams.searchParam

		// Match stage for user referral

		const loggedUser = (await this.userService.getUserByEmail(userData.email)) as UserDocument

		if (!loggedUser) {
			throw new BadRequestException('User not found')
		}

		const sortQuery = buildPaymentSortQuery(queryParams.sortType, 'requestedDate', 'withdrawAmount')

		const aggregationPipeline = buildPaymentAggregateQuery({
			loggedUser,
			status,
			paymentType,
			paymentStartDate,
			paymentEndDate,
			queryConditions,
			sortQuery,
			skip,
			limit,
		})

		const aggregationResult = await this.paymentRequest.aggregate(aggregationPipeline).exec()

		if (!aggregationResult || aggregationResult.length === 0) {
			throw new NotFoundException('Payment History list not found')
		}

		const [result] = aggregationResult
		function formatTimeStamp(date: string) {
			const utcTimestamp = Date.parse(date)
			const offset = 5.5 * 60 * 60 * 1000
			const istTimestamp = utcTimestamp + offset
			return istTimestamp
		}
		const paymentRequests = result.paginatedResults as PaymentRequestDocument[]
		const totalCount = result.totalCount.length > 0 ? result.totalCount[0].count : 0
		return {
			payments: paymentRequests.map(paymentRequest => {
				const { withdrawAmount, requestedDate, paymentType, status, referenceId } = paymentRequest

				return {
					referenceId,
					withdrawAmount,
					paymentType:
						paymentType === 'bank'
							? Payment.bank
							: paymentType === 'upi'
								? Payment.upi
								: paymentType === 'giftVoucher'
									? Payment.giftVoucher
									: paymentType === 'recharge'
										? Payment.recharge
										: Payment.wallet,
					status:
						status === 'pending'
							? PaymentStatus.requested
							: status === 'approved'
								? PaymentStatus.paid
								: PaymentStatus.cancelled,
					paymentDate: formatTimeStamp(requestedDate as unknown as string),
				}
			}),
			pagination: {
				page,
				pageSize: paymentRequests.length,
				total: totalCount,
			},
		}
	}
}
