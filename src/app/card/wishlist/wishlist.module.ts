import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { CardWishList, CardWishListSchema } from './schema/wishlist.schema'
import { WishListController } from './wishlist.controller'
import { WishListService } from './wishlist.service'

@Module({
	imports: [
		MongooseModule.forFeature([
			{
				name: CardWishList.name,
				schema: CardWishListSchema,
			},
		]),
	],
	controllers: [WishListController],
	providers: [WishListService],
})
export class WishListModule {}
