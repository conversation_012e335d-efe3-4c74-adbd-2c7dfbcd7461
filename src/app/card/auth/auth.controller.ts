import { Body, Controller, Post } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { CardLoginDto, CardLoginResponseDto } from './dto/card.dto'

@ApiTags('Card Auth')
@Controller('card/auth')
export class CardAuthController {
	@ApiResponse({
		type: CardLoginResponseDto,
	})
	@Post('login')
	async cardLogin(@Body() data: CardLoginDto) {
		if (!data.mobile) {
			return { message: 'Mobile number is required' }
		}
		if (data.mobile.length !== 10) {
			return { message: 'Invalid mobile number' }
		}
		if (Number.isNaN(Number(data.mobile))) {
			return { message: 'Invalid mobile number' }
		}
		return { message: 'User not found' }
	}
}
