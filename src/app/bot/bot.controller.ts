import { BadRequestException, Body, Controller, HttpStatus, Post } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { BotService } from './bot.service'
import { SendMessageDto, SendMessageResponseDto } from './dto/send-message.dto'

@ApiTags('Bot')
@Controller('bot')
export class BotController {
	constructor(private readonly botService: BotService) {}

	@ApiResponse({
		status: HttpStatus.OK,
		type: SendMessageResponseDto,
		description: 'Message sent successfully',
	})
	@ApiResponse({
		status: HttpStatus.BAD_REQUEST,
		description: 'Invalid request parameters',
	})
	@Post('send-message')
	async sendMessage(@Body() sendMessageDto: SendMessageDto): Promise<SendMessageResponseDto> {
		try {
			await this.botService.sendMessage(sendMessageDto.threadId, sendMessageDto.msg)

			return {
				message: 'Message sent successfully',
				threadId: sendMessageDto.threadId,
			}
		} catch (error) {
			if (error instanceof Error) {
				throw new BadRequestException(error.message)
			}
			throw new BadRequestException('Failed to send message')
		}
	}
}
