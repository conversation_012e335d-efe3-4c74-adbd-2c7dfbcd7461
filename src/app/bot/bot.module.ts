import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { ScheduleModule } from '@nestjs/schedule'
import { Click, ClickSchema, User, UserSchema } from '@shared/entities'
import { BotController } from './bot.controller'
import { BotService } from './bot.service'
import { BotStatsService } from './bot-status.service'

@Module({
	imports: [
		ScheduleModule.forRoot(),
		MongooseModule.forFeature([
			{
				name: Click.name,
				schema: ClickSchema,
			},
			{
				name: User.name,
				schema: UserSchema,
			},
		]),
	],
	controllers: [BotController],
	providers: [BotService, BotStatsService],
	exports: [BotService],
})
export class BotModule {}
