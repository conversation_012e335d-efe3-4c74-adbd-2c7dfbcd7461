import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class SendMessageDto {
	@ApiProperty({
		example: '1262',
		description: 'The unique identifier for the conversation thread',
		required: true,
	})
	@IsString()
	@IsNotEmpty()
	threadId!: string

	@ApiProperty({
		example: 'Hello, this is a test message from the API',
		description: 'The message content to send to the bot',
		required: true,
	})
	@IsString()
	@IsNotEmpty()
	msg!: string
}

export class SendMessageResponseDto {
	@ApiProperty({
		example: 'Message sent successfully',
		description: 'Response message indicating success',
	})
	message!: string

	@ApiProperty({
		example: '1262',
		description: 'The thread ID where the message was sent',
	})
	threadId!: string
}
