import { env } from '@config'
import { run } from '@grammyjs/runner'
import { Injectable, OnModuleInit } from '@nestjs/common'
import { Bot } from 'grammy'

@Injectable()
export class BotService implements OnModuleInit {
	private bot: Bot
	private chatId = -1_002_559_017_139
	private newUserAlertThreadId = 1262
	private withdrawalAlertThreadId = 1963
	private missingCashbackThreadId = 1966

	onModuleInit() {
		this.bot = new Bot(env.TELEGRAM.botToken)
		run(this.bot)
	}

	async sendNewUserAlert(message: string) {
		await this.bot.api.sendMessage(this.chatId, message, {
			// biome-ignore lint/style/useNamingConvention: required for external API
			message_thread_id: this.newUserAlertThreadId,
			// biome-ignore lint/style/useNamingConvention: required for external API
			parse_mode: 'HTML',
		})
	}

	async sendWithdrawalAlert(message: string) {
		await this.bot.api.sendMessage(this.chatId, message, {
			// biome-ignore lint/style/useNamingConvention: required for external API
			message_thread_id: this.withdrawalAlertThreadId,
			// biome-ignore lint/style/useNamingConvention: required for external API
			parse_mode: 'HTML',
		})
	}

	async sendMissingCashbackAlert(message: string) {
		await this.bot.api.sendMessage(this.chatId, message, {
			// biome-ignore lint/style/useNamingConvention: required for external API
			message_thread_id: this.missingCashbackThreadId,
			// biome-ignore lint/style/useNamingConvention: required for external API
			parse_mode: 'HTML',
		})
	}

	async sendMessage(threadId: string, message: string) {
		const threadIdNumber = Number.parseInt(threadId, 10)

		if (Number.isNaN(threadIdNumber)) {
			throw new Error('Invalid thread ID: must be a valid number')
		}

		await this.bot.api.sendMessage(this.chatId, message, {
			// biome-ignore lint/style/useNamingConvention: required for external API
			message_thread_id: threadIdNumber,
			// biome-ignore lint/style/useNamingConvention: required for external API
			parse_mode: 'HTML',
		})
	}

	getBot() {
		return this.bot
	}
}
