import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Cron } from '@nestjs/schedule'
import { Click, ClickDocument, User, UserDocument } from '@shared/entities'
import { Status } from '@shared/types'
import { Model } from 'mongoose'
import { BotService } from './bot.service'

@Injectable()
export class BotStatsService implements OnModuleInit {
	private readonly logger = new Logger(BotStatsService.name)
	private readonly statsThreadId = 1262 // Daily stats thread ID

	constructor(
		private readonly botService: BotService,
		@InjectModel(Click.name)
		private click: Model<ClickDocument>,
		@InjectModel(User.name)
		private user: Model<UserDocument>
	) {}

	onModuleInit() {
		this.getBotStats()
	}

	/**
	 * Calculate total active users count
	 */
	private async getTotalActiveUsers(): Promise<number> {
		try {
			this.logger.debug('Fetching total active users count')
			const count = await this.user.countDocuments({ status: Status.active })
			this.logger.debug(`Total active users count: ${count}`)
			return count
		} catch (error) {
			this.logger.error('Failed to get total active users count', {
				error: error.message,
				stack: error.stack,
			})
			throw error
		}
	}

	/**
	 * Calculate newly registered users in a given date range
	 */
	private async getNewlyRegisteredUsers(startDate: Date, endDate: Date): Promise<number> {
		try {
			this.logger.debug('Fetching newly registered users count', {
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
			})
			const count = await this.user.countDocuments({
				dateRegistered: {
					$gte: startDate,
					$lte: endDate,
				},
			})
			this.logger.debug(`Newly registered users count: ${count}`, {
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
			})
			return count
		} catch (error) {
			this.logger.error('Failed to get newly registered users count', {
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
				error: error.message,
				stack: error.stack,
			})
			throw error
		}
	}

	/**
	 * Calculate click counts with specific status in a given date range
	 */
	private async getClickCountsByStatus(
		status: string,
		startDate: Date,
		endDate: Date
	): Promise<number> {
		try {
			this.logger.debug('Fetching click counts by status', {
				status,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
			})
			const count = await this.click.countDocuments({
				status,
				createdAt: {
					$gte: startDate,
					$lte: endDate,
				},
			})
			this.logger.debug(`Click counts for status '${status}': ${count}`, {
				status,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
			})
			return count
		} catch (error) {
			this.logger.error('Failed to get click counts by status', {
				status,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
				error: error.message,
				stack: error.stack,
			})
			throw error
		}
	}

	/**
	 * Format the stats into a readable message
	 */
	private formatStatsMessage(stats: {
		totalActiveUsers: number
		newUsersLast7Days: number
		newUsersLastMonth: number
		clickedToday: number
		clickedLast7Days: number
		trackedLast7Days: number
		trackedLast30Days: number
		confirmedLast6Months: number
	}): string {
		return `<b>📊 ICashback Stats 📊</b>

<b>👥 User Statistics:</b>
• Total Verified Users: <b>${stats.totalActiveUsers}</b>
• New Users (Last 7 Days): <b>${stats.newUsersLast7Days}</b>
• New Users (Last Month): <b>${stats.newUsersLastMonth}</b>

<b>🖱️ Click Statistics:</b>
• Clicked Today: <b>${stats.clickedToday}</b>
• Clicked (Last 7 Days): <b>${stats.clickedLast7Days}</b>

<b>📈 Tracking Statistics:</b>
• Tracked (Last 7 Days): <b>${stats.trackedLast7Days}</b>
• Tracked (Last 30 Days): <b>${stats.trackedLast30Days}</b>

<b>✅ Confirmation Statistics:</b>
• Confirmed (Last 6 Months): <b>${stats.confirmedLast6Months}</b>

<i>Last Updated: ${new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' })}</i>`
	}

	/**
	 * Calculate and return stats data
	 */
	private async calculateStats(): Promise<{
		totalActiveUsers: number
		newUsersLast7Days: number
		newUsersLastMonth: number
		clickedToday: number
		clickedLast7Days: number
		trackedLast7Days: number
		trackedLast30Days: number
		confirmedLast6Months: number
	}> {
		// Get current date
		const now = new Date()

		// Calculate date ranges
		const today = new Date(now)
		today.setHours(0, 0, 0, 0)

		const endOfToday = new Date(now)
		endOfToday.setHours(23, 59, 59, 999)

		const last7Days = new Date(now)
		last7Days.setDate(now.getDate() - 7)
		last7Days.setHours(0, 0, 0, 0)

		const lastMonth = new Date(now)
		lastMonth.setDate(1) // Set to first day of current month
		lastMonth.setMonth(now.getMonth() - 1) // Go back one month
		lastMonth.setHours(0, 0, 0, 0)

		const last30Days = new Date(now)
		last30Days.setDate(now.getDate() - 30)
		last30Days.setHours(0, 0, 0, 0)

		const last6Months = new Date(now)
		last6Months.setDate(1) // Set to first day of current month
		last6Months.setMonth(now.getMonth() - 6) // Go back 6 months
		last6Months.setHours(0, 0, 0, 0)

		// Calculate all stats in parallel
		const [
			totalActiveUsers,
			newUsersLast7Days,
			newUsersLastMonth,
			clickedToday,
			clickedLast7Days,
			trackedLast7Days,
			trackedLast30Days,
			confirmedLast6Months,
		] = await Promise.all([
			this.getTotalActiveUsers(),
			this.getNewlyRegisteredUsers(last7Days, now),
			this.getNewlyRegisteredUsers(lastMonth, now),
			this.getClickCountsByStatus('clicked', today, endOfToday),
			this.getClickCountsByStatus('clicked', last7Days, now),
			this.getClickCountsByStatus('tracked', last7Days, now),
			this.getClickCountsByStatus('tracked', last30Days, now),
			this.getClickCountsByStatus('confirmed', last6Months, now),
		])

		return {
			totalActiveUsers,
			newUsersLast7Days,
			newUsersLastMonth,
			clickedToday,
			clickedLast7Days,
			trackedLast7Days,
			trackedLast30Days,
			confirmedLast6Months,
		}
	}

	/**
	 * Send daily stats notification - runs every day at midnight (IST)
	 */
	@Cron('0 0 * * *', {
		timeZone: 'Asia/Kolkata',
	})
	async sendDailyStatsNotification(): Promise<void> {
		try {
			this.logger.log('Starting daily stats notification job')

			const stats = await this.calculateStats()
			const statsMessage = this.formatStatsMessage(stats)

			// Add daily notification header
			const dailyStatsMessage = `🌅 <b>Daily Stats Report</b> 🌅\n\n${statsMessage}`

			await this.botService.sendMessage(this.statsThreadId.toString(), dailyStatsMessage)

			this.logger.log('Daily stats notification sent successfully', {
				threadId: this.statsThreadId,
				stats,
			})
		} catch (error) {
			this.logger.error('Failed to send daily stats notification', {
				error: error.message,
				stack: error.stack,
			})
		}
	}

	getBotStats() {
		const bot = this.botService.getBot()

		bot.command('stats', async ctx => {
			const loadingMessage = await ctx.reply('Fetching stats...', {
				// biome-ignore lint/style/useNamingConvention: Telegram Bot API uses snake_case
				parse_mode: 'HTML',
				// biome-ignore lint/style/useNamingConvention: Telegram Bot API uses snake_case
				disable_notification: true,
			})

			try {
				this.logger.log('Starting bot stats calculation', {
					chatId: ctx.chat?.id,
					messageThreadId: ctx.message?.message_thread_id,
				})

				const stats = await this.calculateStats()

				this.logger.log('Bot stats calculated successfully', {
					chatId: ctx.chat?.id,
					messageThreadId: ctx.message?.message_thread_id,
					stats,
				})

				// Format the stats message
				const statsMessage = this.formatStatsMessage(stats)

				// Delete the loading message and send the stats
				await ctx.api.deleteMessage(ctx.chat.id, loadingMessage.message_id)
				await ctx.reply(statsMessage, {
					// biome-ignore lint/style/useNamingConvention: Telegram Bot API uses snake_case
					parse_mode: 'HTML',
					// biome-ignore lint/style/useNamingConvention: Telegram Bot API uses snake_case
					message_thread_id: ctx.message?.message_thread_id,
					// biome-ignore lint/style/useNamingConvention: Telegram Bot API uses snake_case
					disable_notification: true,
				})
			} catch (error) {
				this.logger.error('Error fetching bot stats', {
					chatId: ctx.chat?.id,
					messageThreadId: ctx.message?.message_thread_id,
					error: error.message,
					stack: error.stack,
				})
				await ctx.reply('Error fetching stats. Please try again later.', {
					// biome-ignore lint/style/useNamingConvention: Telegram Bot API uses snake_case
					parse_mode: 'HTML',
				})
			}
		})
	}
}
