import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { RemoveOfferDto, SaveOfferDto } from 'app/offer/dto/offer.dto'
import { SaveItemResponse } from 'app/saved-item/types/saved-item.types'
import { Auth, AuthOptional, OptionalUser, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { GetAllStoresDto } from './dto/get-all-stores.dto'
import { StoreService } from './store.service'
import {
	GetAllStoresResponse,
	GetCashbackRatesByStoreResponse,
	GetStoreDetailsResponse,
} from './types/get-all-stores.types'

@ApiTags('Stores')
@Controller('/stores')
export class StoreController {
	constructor(private readonly storeService: StoreService) {}

	@ApiResponse({
		type: GetAllStoresResponse,
	})
	@Get()
	@AuthOptional()
	async getAllStores(
		@Query() queryParams: GetAllStoresDto,
		@OptionalUser() userSession: ReqUser
	): Promise<GetAllStoresResponse> {
		const res = await this.storeService.getAllStores(queryParams, userSession)
		return res
	}

	@ApiResponse({
		type: GetStoreDetailsResponse,
	})
	@Get('store-details:name')
	@AuthOptional()
	async getStoreDetailsByName(
		@Param('name') name: string,
		@User() user: ReqUser
	): Promise<GetStoreDetailsResponse> {
		return await this.storeService.getStoreDetailsByName(name, user)
	}

	@ApiResponse({
		type: GetCashbackRatesByStoreResponse,
	})
	@Get('cashback-rates-by-store:id')
	async getCashbackRatesByStoreId(
		@Param('id') id: string
	): Promise<GetCashbackRatesByStoreResponse> {
		return await this.storeService.getCashbackRatesByStoreId(id)
	}

	@ApiResponse({
		type: SaveItemResponse,
	})
	@Auth()
	@Post('/save')
	async saveItem(@Body() createSaveItem: SaveOfferDto, @User() user: ReqUser) {
		return await this.storeService.saveStore(createSaveItem, user)
	}

	@ApiResponse({
		type: SaveItemResponse,
	})
	@Auth()
	@Post('/remove')
	async removeSavedItem(@Body() removeSavedItem: RemoveOfferDto, @User() user: ReqUser) {
		return await this.storeService.removeStore(user, removeSavedItem)
	}
}
