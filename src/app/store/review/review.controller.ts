import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { Auth, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { CreateReviewDto, GetAllReviewsDto } from './dto/review.dto'
import { ReviewService } from './review.service'
import { CreateReviewResponse, GetAllReviewsResponse } from './types/review.types'

@ApiTags('Review')
@Controller('/stores/review')
export class ReviewController {
	constructor(private readonly reviewService: ReviewService) {}

	@Auth()
	@ApiResponse({ type: CreateReviewResponse })
	@Post('/add-review')
	async create(@Body() review: CreateReviewDto, @User() user: ReqUser) {
		return await this.reviewService.create(review, user)
	}

	@ApiResponse({
		type: GetAllReviewsResponse,
	})
	@Get()
	async getAllGiftCards(@Query() queryParams: GetAllReviewsDto) {
		return this.reviewService.getAllReviewsOfStore(queryParams)
	}
}
