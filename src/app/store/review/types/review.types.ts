import { ApiProperty } from '@nestjs/swagger'
import { PaginationResponseType } from 'shared/dto'

export class CreateReviewResponse {
	@ApiProperty({ type: String })
	message!: string
}
export class GetAllReviewsType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	avatar: string

	@ApiProperty({ type: String })
	review: string

	@ApiProperty({ type: Number })
	rating: number

	@ApiProperty({ type: String })
	createdDate: string
}

export class GetAllReviewsResponse {
	@ApiProperty({ type: [GetAllReviewsType] })
	reviews: GetAllReviewsType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}
