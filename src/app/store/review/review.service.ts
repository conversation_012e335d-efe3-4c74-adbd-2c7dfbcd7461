import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { UserService } from 'app/user/user.service'
import { Model, Types } from 'mongoose'
import { ReqUser, StoreDocument, UserDocument } from 'shared/entities'
import { Review, ReviewDocument } from 'shared/entities/review.entity'
import { buildStoreReviewSortQuery } from 'shared/helpers/query-builder'
import { StoreService } from '../store.service'
import { CreateReviewDto, GetAllReviewsDto } from './dto/review.dto'
import { CreateReviewResponse } from './types/review.types'

@Injectable()
export class ReviewService {
	constructor(
		@InjectModel(Review.name)
		private review: Model<ReviewDocument>,
		private readonly userService: UserService,
		@Inject(forwardRef(() => StoreService))
		private storeService: StoreService
	) {}

	/**
	 * Create a review using the provided review data and user information.
	 *
	 * @param {CreateReviewDto} reviewData - the data for creating the review
	 * @param {ReqUser} reqUser - the user making the request
	 * @return {Promise<CreateReviewResponse>} a promise that resolves to the created review response
	 */
	async create(reviewData: CreateReviewDto, reqUser: ReqUser): Promise<CreateReviewResponse> {
		const user = (await this.userService.getUserByEmail(reqUser.email)) as UserDocument
		const store = (await this.storeService.getStoreByUid(reviewData.storeUid)) as StoreDocument

		if (!store) {
			throw new BadRequestException('Store not found')
		}

		const review = new this.review({
			...reviewData,
			reviewer: user,
			uid: 1,
			store,
		})

		store.ratesTotal += reviewData.rating
		store.ratesCount += 1

		await store.save()

		await review.save()

		return {
			message: 'Review created successfully',
		}
	}
	async getAllReviewsOfStore(queryParams: GetAllReviewsDto) {
		const page = queryParams.page ?? 1
		const _pageSize = queryParams.pageSize ?? 1
		const skip = (page - 1) * queryParams.pageSize
		const _limit = queryParams.pageSize // Ensure pageSize is parsed correctly
		const sortQuery = buildStoreReviewSortQuery(queryParams.sortType, 'createdAt', 'rating')

		// const store = await this.store.findOne({ _id: queryParams.storeId })
		// const store = (await this.storeService.getStoreById(
		// 	queryParams.storeId,
		// )) as StoreDocument

		const reviews = await this.review
			.find({ store: new Types.ObjectId(queryParams.storeId), active: true })
			.populate('reviewer')
			.sort(sortQuery)
			.skip(skip)
			.limit(queryParams.pageSize)
			.exec()

		const totalCount = await this.review
			.countDocuments({
				store: new Types.ObjectId(queryParams.storeId),
				active: true,
			})
			.exec()

		return {
			reviews: reviews.map(item => {
				const { _id, uid, reviewer, review, rating, createdAt } = item
				return {
					uid,
					name: reviewer.name, // Assuming name is a property of the reviewer
					avatar: reviewer.avatar?.secureUrl, // Assuming avatar is a property of the reviewer with a secureUrl property
					review,
					rating,
					createdDate: createdAt,
				}
			}),
			pagination: {
				page,
				pageSize: reviews.length,
				total: totalCount,
			},
		}
	}
	// async getAllReviewsOfStore(queryParams: GetAllReviewsDto) {
	// 	const page = queryParams.page ?? 1
	// 	const skip = (page - 1) * queryParams.pageSize
	// 	const limit = queryParams.pageSize // Ensure pageSize is parsed correctly
	// 	const sortQuery = buildStoreReviewSortQuery(
	// 		queryParams.sortType,
	// 		'createdAt',
	// 		'rating',
	// 	)

	// 	const store = await this.store.find({ _id: queryParams.storeId })

	// 	const aggregationPipeline = buildReviewAggregateQuery({
	// 		sortQuery,
	// 		skip,
	// 		limit,
	// 	})

	// 	const aggregationResult = await this.review
	// 		.aggregate(aggregationPipeline)
	// 		.exec()

	// 	if (
	// 		!aggregationResult ||
	// 		aggregationResult.length === 0 ||
	// 		aggregationResult[0].paginatedResults.length === 0
	// 	) {
	// 		throw new NotFoundException('Reviews  not found')
	// 	}

	// 	const [result] = aggregationResult
	// 	const reviewData = result.paginatedResults as ReviewDocument[]
	// 	const totalCount =
	// 		result.totalCount.length > 0 ? result.totalCount[0].count : 0

	// 	return {
	// 		reviews: reviewData?.map(item => {
	// 			const { uid, rating, review, status, reviewer, createdAt } = item
	// 			return {
	// 				uid,
	// 				rating,
	// 				review,
	// 				status,
	// 				// TODO: Use the reviewer's avatar and name from the user object
	// 				avatar: faker.image.url({ height: 100, width: 100 }) as string,
	// 				name: faker.person.fullName() as string,
	// 				createdDate: createdAt,
	// 			}
	// 		}),
	// 		pagination: {
	// 			page,
	// 			pageSize: reviewData?.length,
	// 			total: totalCount,
	// 		},
	// 	}
	// }
}
