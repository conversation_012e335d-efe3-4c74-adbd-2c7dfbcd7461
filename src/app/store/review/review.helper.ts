export function buildReviewAggregateQuery({
	sortQuery,
	skip,
	limit,
}: {
	sortQuery?: Record<string, 1 | -1>
	skip?: number
	limit?: number
}) {
	const aggregationPipeline = []

	// Filter pipeline for facet stage
	const filterPipeline = []

	if (sortQuery) {
		filterPipeline.push({ $sort: sortQuery })
	}
	// Skip stage
	if (skip) {
		filterPipeline.push({ $skip: skip })
	}
	// Limit stage
	if (limit) {
		filterPipeline.push({ $limit: limit })
	}
	// Project stage
	filterPipeline.push({
		$project: {
			uid: 1,
			rating: 1,
			review: 1,
			createdAt: 1,
			status: 1,
		},
	})

	// Facet stage
	aggregationPipeline.push({
		$facet: {
			paginatedResults: filterPipeline,
			totalCount: [{ $count: 'count' }],
		},
	})

	return aggregationPipeline
}
