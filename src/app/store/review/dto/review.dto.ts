import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { PaginationDto } from 'shared/dto'
import { ReviewTypes } from 'shared/enums'

export class CreateReviewDto {
	@ApiProperty({
		example: 32,
		required: true,
	})
	@IsNumber()
	@IsNotEmpty()
	storeUid!: number

	@ApiProperty({
		example: 'This is a review',
		required: true,
	})
	@IsString()
	@IsNotEmpty()
	review!: string

	@ApiProperty({
		example: 3,
		required: true,
		enum: [1, 2, 3, 4, 5],
	})
	@IsNumber()
	@IsEnum([1, 2, 3, 4, 5])
	@IsNotEmpty()
	rating!: 1 | 2 | 3 | 4 | 5
}

export class GetAllReviewsDto extends PaginationDto {
	@ApiProperty({
		example: '65eef6a14d2e4417e86b9ce5',
		required: true,
	})
	@IsString()
	@IsNotEmpty()
	storeId!: string

	@IsEnum(ReviewTypes)
	@IsOptional()
	@ApiProperty({ enum: ReviewTypes, enumName: 'ReviewTypes' })
	sortType!: ReviewTypes
}
