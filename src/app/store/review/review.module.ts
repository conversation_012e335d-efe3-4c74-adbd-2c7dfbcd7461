import { forwardRef, Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { UserModule } from 'app/user/user.module'
import { Review, ReviewSchema } from 'shared/entities/review.entity'
import { StoreModule } from '../store.module'
import { ReviewController } from './review.controller'
import { ReviewService } from './review.service'

@Module({
	imports: [
		UserModule,
		forwardRef(() => StoreModule),
		MongooseModule.forFeature([
			{
				name: Review.name,
				schema: ReviewSchema,
			},
		]),
	],
	controllers: [ReviewController],
	providers: [ReviewService],
	exports: [ReviewService],
})
export class ReviewModule {}
