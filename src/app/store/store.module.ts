import { forwardRef, Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { OfferModule } from 'app/offer/offer.module'
import { SavedItemModule } from 'app/saved-item/saved-item.module'
import { StoreCategoryModule } from 'app/store-category/store-category.module'
import { UserModule } from 'app/user/user.module'
import {
	Affiliation,
	AffiliationSchema,
	Categories,
	CategoriesSchema,
	Store,
	StoreSchema,
	SubCategory,
	SubCategorySchema,
} from 'shared/entities'
import { ReviewModule } from './review/review.module'
import { StoreController } from './store.controller'
import { StoreService } from './store.service'

@Module({
	imports: [
		OfferModule,
		StoreCategoryModule,
		forwardRef(() => ReviewModule),
		UserModule,
		SavedItemModule,
		MongooseModule.forFeature([
			{
				name: Affiliation.name,
				schema: AffiliationSchema,
			},
			{
				name: Store.name,
				schema: StoreSchema,
			},
			{
				name: SubCategory.name,
				schema: SubCategorySchema,
			},
			{
				name: Categories.name,
				schema: CategoriesSchema,
			},
		]),
	],

	controllers: [StoreController],
	providers: [StoreService],
	exports: [StoreService],
})
export class StoreModule {}
