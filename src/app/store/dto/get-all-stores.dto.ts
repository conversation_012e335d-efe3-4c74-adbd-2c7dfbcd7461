import { ApiProperty } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsEnum, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator'
import { PaginationDto } from 'shared/dto'
import { SortTypes } from 'shared/enums'
import { boolean } from 'zod'

export class GetAllStoresDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'and',
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsOptional()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes' })
	sortType!: SortTypes

	@Transform(({ value }) => Number.parseInt(value, 10))
	@IsNumber()
	@IsOptional()
	@Min(0)
	@Max(100)
	@ApiProperty({ example: '10' })
	minPercent!: number

	@Transform(({ value }) => Number.parseInt(value, 10))
	@IsNumber()
	@IsOptional()
	@Min(0)
	@Max(100)
	@ApiProperty({ example: '60' })
	maxPercent!: number

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	subCategories?: string

	@IsOptional()
	@ApiProperty({
		type: boolean,
	})
	saved!: boolean
}
