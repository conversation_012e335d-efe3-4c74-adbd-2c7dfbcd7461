import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { ContextStore } from 'app/context/types/stores.types'
import { RemoveOfferDto, SaveOfferDto } from 'app/offer/dto/offer.dto'
import { SavedItemService } from 'app/saved-item/saved-item.service'
import { StoreCategoryService } from 'app/store-category/store-category.service'
import { UserService } from 'app/user/user.service'
import { Model } from 'mongoose'
import {
	ReqUser,
	SavedItemDocument,
	Store,
	StoreDocument,
	SubCategory,
	SubCategoryDocument,
	UserDocument,
} from 'shared/entities'
import { buildSortQuery } from 'shared/helpers/query-builder'
import { MeiliSearchService } from 'shared/modules/meilisearch'
import { SavedEnum } from 'shared/types'
import {
	buildStoreAggregateQuery,
	// buildSortQuery,
	buildStoreQuery,
} from '../../shared/helpers/store.helper'
import { GetAllStoresDto } from './dto/get-all-stores.dto'
import {
	GetAllStoresResponse,
	GetStoreDetails,
	GetStoreDetailsResponse,
} from './types/get-all-stores.types'

@Injectable()
export class StoreService {
	constructor(
		@InjectModel(Store.name) private readonly store: Model<StoreDocument>,
		@InjectModel(SubCategory.name)
		private readonly subCategories: Model<SubCategoryDocument>,

		private readonly savedItemService: SavedItemService,
		private readonly userService: UserService,
		private readonly storeCategoryService: StoreCategoryService
	) {}

	async getAllStores(
		queryParams: GetAllStoresDto,
		userSession: ReqUser
	): Promise<GetAllStoresResponse> {
		const page = queryParams.page ?? 1
		const skip = (page - 1) * queryParams.pageSize
		const limit = queryParams.pageSize // Ensure pageSize is parsed correctly
		const queryConditions = buildStoreQuery(queryParams)

		// If search parameter is provided, use MeiliSearch
		if (queryParams.searchParam && queryParams.searchParam.trim() !== '') {
			const client = new MeiliSearchService()
			const { hits: response } = await client.searchDocuments(queryParams.searchParam)

			// Filter results to only include active stores
			const storeData = response.filter(item => item.type === 'store' && item.active)

			// If no stores found, return empty result
			if (storeData.length === 0) {
				return {
					stores: [],
					pagination: {
						page,
						pageSize: 0,
						total: 0,
					},
				}
			}

			// Apply pagination to the filtered results
			const paginatedStores = storeData.slice(skip, skip + limit)

			// Get user's saved stores for checking if stores are saved
			const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null
			const usersSavedStores: SavedItemDocument[] = user
				? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Store], user._id)
				: []

			// Fetch the actual store documents from MongoDB using the IDs from MeiliSearch
			const storeIds = paginatedStores.map((item: { id: string }) => item.id)
			const storesFromDb = await this.store.find({ _id: { $in: storeIds } }).exec()

			// If no stores found in MongoDB, return empty result
			if (storesFromDb.length === 0) {
				return {
					stores: [],
					pagination: {
						page,
						pageSize: 0,
						total: 0,
					},
				}
			}

			// Sort stores in the same order as storeIds
			const orderedStores = storeIds
				.map(id => storesFromDb.find(store => store._id.toString() === id))
				.filter(Boolean) as StoreDocument[]

			// Map the stores to the expected format
			const storesRes = {
				stores: orderedStores.map(store => {
					const isSaved = usersSavedStores.some(savedStore => savedStore.itemUid === store.uid)
					const { uid, bgColor = '#70367C', storeOffer, logo, name } = store
					return {
						uid,
						bgColor,
						caption: storeOffer,
						imageUrl: logo.secureUrl,
						storeName: name,
						saved: isSaved,
					}
				}),
				pagination: {
					page,
					pageSize: orderedStores.length,
					total: storeData.length,
				},
			}

			return storesRes
		}

		// If no search parameter, use the original MongoDB aggregation

		const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null
		const usersSavedStores: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Store], user._id)
			: []

		const sortQuery = buildSortQuery(
			queryParams.sortType,
			'createdAt',
			'name',
			queryParams?.sortType === 'highestCbAmount' ? 'cashbackAmount' : 'cashbackPercent'
		)

		const subCategoryIds =
			queryParams?.subCategories && queryParams.subCategories.length > 0
				? queryParams.subCategories.split(',').map(Number)
				: []

		// First, fetch the subcategory documents to get their _ids
		const subCategories = await this.subCategories.find({ uid: { $in: subCategoryIds } }).exec()

		// Extract the _ids
		const subCategoryObjectIds = subCategories.map(sc => sc._id.toString())

		const aggregationPipeline = buildStoreAggregateQuery({
			subCategoryIds: subCategoryObjectIds,
			sortQuery,
			skip,
			limit,
			queryConditions,
			// biome-ignore lint/suspicious/noExplicitAny: required for external API
		}) as any

		aggregationPipeline.unshift({
			$match: {
				$or: [{ isDeleted: false }, { isDeleted: { $exists: false } }],
			},
		})

		const aggregationResult = await this.store.aggregate(aggregationPipeline).exec()

		// if (
		// 	!aggregationResult ||
		// 	aggregationResult.length === 0 ||
		// 	aggregationResult[0].documents.length === 0
		// ) {
		// 	throw new NotFoundException('Stores not found')
		// }

		const [result] = aggregationResult
		const stores = result.documents as StoreDocument[]
		const totalCount = result.totalCount.length > 0 ? result.totalCount[0].count : 0

		const storesRes = {
			stores: stores.map(store => {
				const isSaved = usersSavedStores.some(savedStore => savedStore.itemUid === store.uid)
				const { uid, bgColor = '#70367C', storeOffer, logo, name } = store
				return {
					uid,
					bgColor,
					caption: storeOffer,
					imageUrl: logo.secureUrl,
					storeName: name,
					saved: isSaved,
				}
			}),
			pagination: {
				page,
				pageSize: stores.length,
				total: totalCount,
			},
		}

		return storesRes
	}

	async getPopulatedStoreByUid(uid: number) {
		return this.store.findOne({ uid }).populate('affiliation').exec()
	}

	async getStoreByUid(uid: number) {
		return this.store.findOne({ uid }).exec()
	}
	async getStoreById(_id: string) {
		return this.store.findOne({ _id }).exec()
	}

	async getStoreDetailsByName(
		name: string,
		userSession: ReqUser
	): Promise<GetStoreDetailsResponse> {
		const store = await this.store
			.findOne({
				$expr: {
					$eq: [
						{
							$toLower: {
								$trim: {
									input: {
										$replaceAll: { input: '$name', find: ' ', replacement: '' },
									},
								},
							},
						}, // Remove spaces, trim, and convert the field `name` from the database to lowercase
						name
							.trim()
							.toLowerCase()
							.replace(/\s+/g, ''), // Remove spaces and convert the input `name` to lowercase
					],
				},
			})
			.populate('giftCard')
			.populate('relatedStores')
			.exec()

		if (!store) {
			throw new NotFoundException('Store not found')
		}

		const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null

		const usersSavedStores: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Store], user._id)
			: []

		const relatedStoreDetails = await this.store.find({
			_id: { $in: store?.relatedStores },
			active: true,
		})

		const similarStores: ContextStore[] = relatedStoreDetails.map((relatedStore: Store) => {
			const { uid, name, bgColor, logo, storeOffer } = relatedStore
			const isSaved = usersSavedStores.some(savedStore => uid === savedStore.uid)
			return {
				uid,
				bgColor,
				storeName: name,
				imageUrl: logo?.secureUrl,
				caption: storeOffer,
				saved: isSaved,
			}
		})

		//Calculate the rating
		const rating = Number((store.ratesTotal / store.ratesCount).toFixed(1))
		const storeDetails: GetStoreDetails = {
			id: store.id,
			uid: store.uid,
			name: store.name,
			logo: store.logo.secureUrl,
			description: store.description,
			offerWarning: store.offerWarning,
			missingAccepted: store.missingAccepted,
			trackingTime: store.trackingTime,
			confirmationTime: store.confirmationTime,
			cashbackAmount: store.cashbackAmount,
			cashbackPercent: store.cashbackPercent,
			cashbackType: store?.cashbackType ?? 'cashback',
			offerType: store.offerType,
			importantPoints: store.importantPoints,
			minimumAmount: store.minimumAmount,
			ratingAverage: rating,
			ratingsCount: store.ratesCount,
			active: store?.active,
			storeWarning: store?.storeTopWarning || '',
			storePopUpWarning: store?.storeWarning || '',
			isAppSaleTrackable: store?.noAppSaleCategory,
			giftCard: {
				uid: store?.giftCard?.uid,
				name: store?.giftCard?.name,
			},
		}
		return {
			store: storeDetails,
			similarStores,
		}
	}

	async saveStore(saveParams: SaveOfferDto, userSession: ReqUser) {
		const user = (await this.userService.getUserByEmail(userSession.email)) as UserDocument

		if (!user) {
			throw new NotFoundException('User not found')
		}

		const store = await this.getStoreByUid(saveParams.itemUid)

		if (!store) {
			throw new NotFoundException('Store not found')
		}

		await this.savedItemService.saveItem(user, SavedEnum.Store, store)
		return {
			message: 'Store saved successfully',
		}
	}

	async removeStore(userSession: ReqUser, params: RemoveOfferDto) {
		const user = await this.userService.getUserByEmail(userSession.email)

		if (!user) {
			throw new NotFoundException('User not found')
		}

		// Remove the saved item
		await this.savedItemService.removeItem(user, SavedEnum.Store, params.itemUid)

		return {
			message: 'Store removed successfully.',
		}
	}
	async getCashbackRatesByStoreId(id: string) {
		return await this.storeCategoryService.getCashbackRatesByStoreId(id)
	}
}
