import { ApiProperty } from '@nestjs/swagger'
import { ContextStore } from 'app/context/types/stores.types'
import { IsOptional } from 'class-validator'
import { PaginationResponseType } from 'shared/dto'
import { OfferCouponsType } from '../../offer/types/offer.types'

export class StoreCard {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	bgColor: string

	@ApiProperty({ type: String })
	caption: string

	@ApiProperty({ type: String })
	imageUrl: string

	@ApiProperty({ type: String })
	storeName: string

	@IsOptional()
	@ApiProperty({ type: Boolean })
	saved: boolean
}
export class GetAllStoresResponse {
	@ApiProperty({ type: [StoreCard] })
	stores: StoreCard[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}

export class GetStoreDataResponse {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	bgColor: string

	@ApiProperty({ type: String })
	caption: string

	@ApiProperty({ type: String })
	storeLogo: string

	@ApiProperty({ type: String })
	storeName: string

	// @ApiProperty({ type: String })
	// rating: string

	@ApiProperty({ type: String })
	description: string

	@ApiProperty({ type: Number })
	cashbackAmount: number

	@ApiProperty({ type: Boolean })
	missingAccepted: boolean

	@ApiProperty({ type: String })
	trackingTime: string

	@ApiProperty({ type: String })
	confirmationTime: string

	@ApiProperty({ type: String })
	giftCard: string

	@ApiProperty({ type: [OfferCouponsType] })
	all: OfferCouponsType[]

	// rating: number

	// description: string
	// cashbackAmount: number //amount or percent
	// missingAccepted: string
	// trackingTime: string
	// confirmationTime: string
	// giftCard: {
	// 	name: string
	// 	uid: number
	// }
	// all: OfferCouponsType[] // For All Offers - Sort - newest, rating
	// deals: OfferCouponsType[] // For Deals - Sort - newest, rating
	// coupons: OfferCouponsType[] // For Coupons - Sort - newest, rating
	// cashbackRates: OfferCouponsType[] // For Cashback Rates - Sort - newest, rating

	// For Customer Rating - Sort - newest, rating
}

export class StoreGiftCard {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string
}

export class GetStoreDetails {
	@ApiProperty({ type: String })
	id: string

	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	logo: string

	@ApiProperty({ type: String })
	description: string

	@ApiProperty({ type: String })
	offerWarning: string

	@ApiProperty({ type: Boolean })
	missingAccepted: boolean

	@ApiProperty({ type: String })
	trackingTime: string

	@ApiProperty({ type: String })
	confirmationTime: string

	@ApiProperty({ type: Number })
	minimumAmount: number

	@ApiProperty({ type: Number })
	@IsOptional()
	cashbackAmount?: number

	@ApiProperty({ type: Number })
	@IsOptional()
	cashbackPercent?: number

	@ApiProperty({ type: String, enum: ['cashback', 'reward'] })
	cashbackType: 'cashback' | 'reward'

	@ApiProperty({ type: String, enum: ['flat', 'upto'] })
	offerType: 'flat' | 'upto'

	@ApiProperty({ type: String })
	@IsOptional()
	importantPoints?: string

	@ApiProperty({ type: Number })
	ratingAverage: number

	@ApiProperty({ type: Number })
	ratingsCount: number

	@ApiProperty({ type: StoreGiftCard })
	@IsOptional()
	giftCard?: StoreGiftCard

	@IsOptional()
	@ApiProperty({ type: Boolean })
	active: boolean

	@IsOptional()
	@ApiProperty({ type: String })
	storePopUpWarning?: string

	@IsOptional()
	@ApiProperty({ type: String })
	storeWarning?: string

	@IsOptional()
	@ApiProperty({ type: Boolean })
	isAppSaleTrackable?: boolean
}
export class GetStoreDetailsResponse {
	@ApiProperty({ type: GetStoreDetails })
	store: GetStoreDetails

	@ApiProperty({ type: [ContextStore] })
	similarStores: ContextStore[]
}

export class CashbackRateType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	description: string

	@ApiProperty({ type: Number })
	oldUserRate: number

	@ApiProperty({ type: Number })
	newUserRate: number

	@ApiProperty({ type: String, enum: ['percent', 'amount'] })
	type: 'percent' | 'amount'
}

export class GetCashbackRatesByStoreResponse {
	@ApiProperty({ type: [CashbackRateType] })
	cashbackRates: CashbackRateType[]
}
