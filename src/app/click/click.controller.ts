import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { Auth, ClientInfo, ClientInfoData, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { ClickService } from './click.service'
import { ClicksByStoreResponse, ClicksResponse } from './dto/click-response.dto'
import { ClickCreateResponse, CreateClickDto } from './dto/create-click.dto'
import { GetClicksByStoreDto, GetClicksDto } from './dto/get-clicks.dto'

@ApiTags('Click')
@Controller('click')
export class ClickController {
	constructor(private readonly clickService: ClickService) {}

	@ApiResponse({
		type: ClickCreateResponse,
	})
	@Auth()
	@Post()
	async create(
		@Body() createClickDto: CreateClickDto,
		@ClientInfo() clientInfo: ClientInfoData,
		@User() user: ReqUser
	) {
		return this.clickService.create(createClickDto, clientInfo, user)
	}

	@ApiResponse({
		type: ClickCreateResponse,
	})
	@Post('no-auth')
	async createNoAuth(
		@Body() createClickDto: CreateClickDto,
		@ClientInfo() clientInfo: ClientInfoData
	) {
		return this.clickService.create(createClickDto, clientInfo)
	}

	@ApiResponse({
		type: ClicksResponse,
	})
	@Auth()
	@Get()
	async getClicks(@User() user: ReqUser, @Query() clickQuery: GetClicksDto) {
		return await this.clickService.getClicks(user, clickQuery)
	}

	@ApiResponse({
		type: ClicksByStoreResponse,
	})
	@Auth()
	@Get('stores')
	async getClicksByStores(@User() user: ReqUser, @Query() clickQuery: GetClicksByStoreDto) {
		return await this.clickService.getClicksByStores(user, clickQuery)
	}

	// @ApiResponse({
	// 	type: ClicksByStoreResponse,
	// })
	// // @Auth()
	// @Get('stores-test')
	// async getClicksByStoresTest(
	// 	@User() user: ReqUser,
	// 	@Query() clickQuery: GetClicksByStoreDto,
	// ) {
	// 	return await this.clickService.getClicksByStores({
	// 		email: "<EMAIL>"
	// 	}, clickQuery)
	// }

	@Auth()
	@Get('clicked_stores')
	async getClickedStores(@User() user: ReqUser) {
		return await this.clickService.getClickedStores(user.email)
	}
}
