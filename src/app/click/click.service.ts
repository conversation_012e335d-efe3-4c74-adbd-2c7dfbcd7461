import { BadRequestException, Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { OfferService } from 'app/offer/offer.service'
import { StoreService } from 'app/store/store.service'
import { StoreCategoryService } from 'app/store-category/store-category.service'
import { UserService } from 'app/user/user.service'
import { Model, Types } from 'mongoose'
import { ClientInfoData } from 'shared/decorators'
import { Click, ClickDocument, ReqUser, User } from 'shared/entities'
import { SortTypes } from 'shared/enums'
import { generateOfferCaption } from 'shared/helpers/offer.helper'
import { CreateClickDto } from './dto/create-click.dto'
import { GetClicksByStoreDto, GetClicksDto } from './dto/get-clicks.dto'
import { operations } from './tags'

@Injectable()
export class ClickService {
	constructor(
		@InjectModel(Click.name) private readonly click: Model<ClickDocument>,
		private readonly userService: UserService,
		private readonly offerService: OfferService,
		private readonly storeService: StoreService,
		private readonly storeCategoryService: StoreCategoryService
	) {}

	async getClickById(id: string) {
		return await this.click.findById(id).populate('store offer').exec()
	}
	async create(createClickDto: CreateClickDto, clientInfo: ClientInfoData, userDta?: ReqUser) {
		const user = userDta && ((await this.userService.getUserByEmail(userDta.email)) as User)
		const click: Partial<Click> = {
			user,
			userCity: clientInfo.geo?.city,
			userIp: clientInfo.ip,
			device: clientInfo.device,
			type: createClickDto.type,
		}
		let redirectUrl: string | undefined = ''
		let offerText: string | undefined = ''

		const processUrl = (url: string, operations: string[][]): string => {
			let currentUrl = url
			for (const [substring, paramToAppend] of operations) {
				if (substring && currentUrl.includes(substring)) {
					const separator = currentUrl.includes('?') ? '&' : '?'
					currentUrl = `${currentUrl}${separator}${paramToAppend}`
				}
			}
			return currentUrl
		}

		switch (createClickDto.type) {
			case 'offer': {
				const offer = await this.offerService.getPopulatedOfferByUid(createClickDto.uid)
				if (!offer) {
					throw new BadRequestException('Offer not found')
				}

				click.offer = offer
				click.title = offer.title
				click.store = offer.store
				click.url = offer.store.logo.secureUrl
				click.affiliation = offer.affiliation
				redirectUrl = offer.link
				offerText = generateOfferCaption({
					offerType: offer?.offerType,
					offerAmount: offer?.offerAmount,
					offerPercent: offer?.offerPercent,
					cashbackType: offer?.store?.cashbackType,
				})
				break
			}
			case 'express': {
				const store = await this.storeService.getPopulatedStoreByUid(createClickDto.uid)
				if (!store) {
					throw new BadRequestException('Store not found')
				}
				click.store = store
				click.title = store.name
				click.url = store.logo.secureUrl
				click.affiliation = store.affiliation
				redirectUrl = store.affiliateLink
				offerText = store.storeOffer
				break
			}
			case 'rates': {
				const storeCategory = await this.storeCategoryService.getPopulatedStoreCategoryByUid(
					createClickDto.uid
				)
				if (!storeCategory) {
					throw new BadRequestException('Store category not found')
				}
				click.storeCategory = storeCategory
				click.title = storeCategory.store.name
				click.url = storeCategory.store.logo.secureUrl
				click.affiliation = storeCategory.affiliation
				click.store = storeCategory.store
				redirectUrl = storeCategory.sectionLink
				break
			}
			default: {
				// Handle unknown click types
				throw new BadRequestException(`Unsupported click type: ${createClickDto.type}`)
			}
		}
		const newClick = new this.click({ ...click, uid: 1, referenceId: '1' })
		await newClick.save()
		const params = [
			`UID=${newClick.referenceId}`,
			`s1=${newClick.referenceId}`,
			`subid1=${newClick.referenceId}`,
			`subid=${newClick.referenceId}`,
			`sid=${newClick.referenceId}`,
			`aff_sub=${newClick.referenceId}`,
			`subTrackId=${newClick.referenceId}`,
			`p1=${newClick.referenceId}`,
			`source=${newClick.referenceId}`,
		].join('&')

		const processedUrl = processUrl(redirectUrl || '', operations(newClick.referenceId))

		newClick.offerUrl =
			processedUrl === redirectUrl
				? `${redirectUrl}${redirectUrl.includes('?') ? '&' : '?'}${params}`
				: processedUrl

		await newClick.save()
		return {
			referenceId: newClick.referenceId,
			url:
				processedUrl === redirectUrl
					? `${redirectUrl}${redirectUrl.includes('?') ? '&' : '?'}${params}`
					: processedUrl,
			logo: newClick.url,
			offer: offerText,
			isActive: click.store.active,
		}
	}

	async getClicks(userDta: ReqUser, query: GetClicksDto) {
		const user = await this.userService.getUserByEmail(userDta.email)
		if (!user) {
			throw new BadRequestException('User not found')
		}
		const buildSortQuery = (sortType: SortTypes): Record<string, 1 | -1> => {
			switch (sortType) {
				case SortTypes.Newest:
					return { createdAt: -1 }
				case SortTypes.Discount:
					return { cashbackAmount: -1 }
				default:
					return { createdAt: -1 } // Default sort
			}
		}
		const matchCriteria: Record<string, object> = {}

		matchCriteria.user = new Types.ObjectId(user._id)

		if (query.searchParam) {
			matchCriteria.title = { $regex: query.searchParam, $options: 'i' }
		}

		// Dynamically adjust store filtering based on the type of click
		if (query.stores && query.stores.length > 0) {
			matchCriteria['store.uid'] = { $in: query.stores }
		}

		if (query?.startDate && query.endDate) {
			matchCriteria.createdAt = {
				$gte: new Date(query.startDate),
				$lte: new Date(query.endDate),
			}
		}

		const sortQuery = buildSortQuery(query.sortType)
		const skip = (query.page - 1) * query.pageSize
		const limit = query.pageSize

		const clicks = await this.click
			.aggregate([
				{
					$lookup: {
						from: 'stores',
						localField: 'store',
						foreignField: '_id',
						as: 'store',
					},
				},
				{
					$unwind: '$store',
				},
				{ $match: matchCriteria },
				{
					$addFields: {
						canReport: {
							$gt: [
								new Date(),
								{ $add: ['$createdAt', 3 * 24 * 60 * 60 * 1000] }, // Add 3 days to createdAt
							],
						},

						// fromPreviousMonth
						fromPreviousMonth: {
							$or: [
								// If the `createdAt` is from the previous year
								{
									$lt: [
										{ $year: '$createdAt' }, // Year of createdAt
										{ $year: new Date() }, // Current year
									],
								},
								// If the `createdAt` is from the previous month in the same year
								{
									$and: [
										{ $gte: [{ $dayOfMonth: new Date() }, 5] }, // Current date is the 5th or later
										{ $lt: [{ $month: '$createdAt' }, { $month: new Date() }] }, // Previous month
										{ $eq: [{ $year: '$createdAt' }, { $year: new Date() }] }, // Same year
									],
								},
							],
						},
					},
				},
				{
					$addFields: {
						status: {
							$switch: {
								branches: [
									{
										case: {
											$and: [{ $eq: ['$status', 'clicked'] }, { $not: ['$canReport'] }],
										},
										then: 'Pending',
									},
									{
										case: {
											$and: [{ $eq: ['$status', 'clicked'] }, '$canReport'],
										},
										then: 'Report Missing CB',
									},
									{ case: { $eq: ['$status', 'tracked'] }, then: 'Tracked' },
									{
										case: { $eq: ['$status', 'confirmed'] },
										then: 'Confirmed',
									},
								],
								default: 'Cancelled',
							},
						},
					},
				},

				// Filter based on the dynamically computed status
				...(query.status && query.status.length > 0
					? [
							{
								$match: {
									status: { $in: query.status },
								},
							},
						]
					: []),
				{
					$facet: {
						clicks: [
							{ $sort: sortQuery },
							{ $skip: skip },
							{ $limit: limit },
							{
								$project: {
									id: '$_id',
									uid: 1,
									referenceId: 1,
									status: 1,
									fromPreviousMonth: 1,
									canReport: 1,
									logo: '$url',
									type: 1,
									createdAt: 1,
									name: '$title',
									date: {
										$dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
									},
									time: {
										$dateToString: { format: '%H:%M', date: '$createdAt' },
									},
								},
							},
						],
						totalCount: [{ $count: 'count' }],
					},
				},
			])
			.exec()

		return {
			clicks: clicks[0].clicks,
			pagination: {
				page: query.page,
				pageSize: clicks[0].clicks.length,
				total: clicks[0].totalCount.length > 0 ? clicks[0].totalCount[0].count : 0,
			},
		}
	}

	async getClicksByStores(userDta: ReqUser, query: GetClicksByStoreDto) {
		const user = await this.userService.getUserByEmail(userDta.email)
		if (!user) {
			throw new BadRequestException('User not found')
		}
		const matchCriteria: Record<string, object> = {}
		const sortCriteria: Record<string, number> = {}

		matchCriteria.user = new Types.ObjectId(user._id)

		// Dynamically adjust store filtering based on the type of click
		if (query.stores && query.stores.length > 0) {
			matchCriteria['store.uid'] = { $in: query.stores }
		}

		if (query?.date) {
			const startOfDay = new Date(query.date)
			startOfDay.setHours(0, 0, 0, 0)
			const endOfDay = new Date(query.date)
			endOfDay.setHours(23, 59, 59, 999)

			matchCriteria.createdAt = {
				$gte: startOfDay,
				$lt: endOfDay,
			}
		}

		switch (query.sortType) {
			case SortTypes.Newest: {
				sortCriteria.createdAt = -1
				break
			}
			case SortTypes.Discount: {
				sortCriteria.cashbackAmount = -1
				break
			}
			case SortTypes.Alphabetical: {
				sortCriteria.name = 1
				break
			}
			default: {
				sortCriteria.createdAt = -1
				break
			}
		}
		const skip = (query.page - 1) * query.pageSize
		const limit = query.pageSize

		// fetch all clicks and grouped by stores
		const clicksGroupedByStore = await this.click
			.aggregate([
				{
					$lookup: {
						from: 'stores',
						localField: 'store',
						foreignField: '_id',
						as: 'store',
					},
				},
				{
					$unwind: '$store',
				},
				{ $match: matchCriteria },
				{
					$addFields: {
						canReport: {
							$gt: [
								new Date(),
								{ $add: ['$createdAt', 3 * 24 * 60 * 60 * 1000] }, // Add 3 days to createdAt
							],
						},

						// fromPreviousMonth
						fromPreviousMonth: {
							$or: [
								// If the `createdAt` is from the previous year
								{
									$lt: [
										{ $year: '$createdAt' }, // Year of createdAt
										{ $year: new Date() }, // Current year
									],
								},
								// If the `createdAt` is from the previous month in the same year
								{
									$and: [
										{ $gte: [{ $dayOfMonth: new Date() }, 5] }, // Current date is the 5th or later
										{ $lt: [{ $month: '$createdAt' }, { $month: new Date() }] }, // Previous month
										{ $eq: [{ $year: '$createdAt' }, { $year: new Date() }] }, // Same year
									],
								},
							],
						},
					},
				},

				{
					$addFields: {
						status: {
							$switch: {
								branches: [
									{
										case: {
											$and: [{ $eq: ['$status', 'clicked'] }, { $not: ['$canReport'] }],
										},
										then: 'Pending',
									},
									{
										case: {
											$and: [{ $eq: ['$status', 'clicked'] }, '$canReport'],
										},
										then: 'Report Missing CB',
									},
									{ case: { $eq: ['$status', 'tracked'] }, then: 'Tracked' },
									{
										case: { $eq: ['$status', 'confirmed'] },
										then: 'Confirmed',
									},
								],
								default: 'Cancelled',
							},
						},
					},
				},

				// Filter based on the dynamically computed status
				...(query.status && query.status.length > 0
					? [
							{
								$match: {
									status: { $in: query.status },
								},
							},
						]
					: []),
				// Sort all clicks by createdAt in descending order before grouping
				{ $sort: { createdAt: -1 } },
				{
					$project: {
						_id: 1,
						'store.uid': 1, // Include Store ID
						'store.name': 1, // Include Store Name
						'store.logo.secureUrl': 1, // Include Store Location
						click: '$$ROOT', // Keep the entire click document for now
					},
				},
				{
					$group: {
						_id: '$store.uid',
						store: {
							$first: {
								uid: '$store.uid',
								name: '$store.name',
								logo: '$store.logo.secureUrl',
							},
						},
						count: { $sum: 1 },
						latestClickDate: { $max: '$click.createdAt' }, // Track the latest click date for sorting
						clicks: {
							$push: {
								id: '$click._id',
								uid: '$click.uid',
								referenceId: '$click.referenceId',
								status: '$click.status',
								name: '$click.title',
								type: '$click.type',
								canReport: '$click.canReport',
								fromPreviousMonth: '$click.fromPreviousMonth',
								date: {
									$dateToString: {
										format: '%Y-%m-%d',
										date: '$click.createdAt',
									},
								},
								time: {
									$dateToString: { format: '%H:%M', date: '$click.createdAt' },
								},
							},
						},
					},
				},
				{
					$project: {
						_id: 0,
						store: {
							$mergeObjects: [
								'$store',
								{
									clicks: '$clicks',
									count: '$count',
								},
							],
						},
						latestClickDate: 1, // Keep the latest click date for sorting
					},
				},
				{ $sort: { latestClickDate: -1 } }, // Sort by the latest click date in descending order
				{ $skip: skip },
				{ $limit: limit },
			])
			.exec()
		return {
			stores: clicksGroupedByStore,
			pagination: {
				page: query.page,
				pageSize: clicksGroupedByStore.length,
				total: clicksGroupedByStore.length,
			},
		}
	}

	async getClickedStores(userEmail: string) {
		const user = await this.userService.getUserByEmail(userEmail)

		if (!user) {
			throw new Error('User not found')
		}

		// Ensure the correct match condition
		const matchStage = {
			$match: {
				user: { $exists: true, $eq: new Types.ObjectId(user._id) },
			},
		}

		type Store = {
			uid: number
			name: string
			logo: string
			clicks: number
		}

		// aggregate the clicked store names by user
		const clickedStores = (await this.click
			.aggregate([
				matchStage,
				{
					$group: {
						_id: '$store',
						count: { $sum: 1 },
					},
				},
				{
					$lookup: {
						from: 'stores',
						localField: '_id',
						foreignField: '_id',
						as: 'store',
					},
				},
				{
					$unwind: '$store',
				},
				{
					$project: {
						_id: 0,
						uid: '$store.uid',
						name: '$store.name',
						logo: '$store.logo.secureUrl',
						clicks: '$count',
					},
				},
				{ $sort: { name: 1 } },
			])
			.exec()) as unknown as Store[]

		return clickedStores
	}
}
