import { ApiProperty } from '@nestjs/swagger'
import { PaginationResponseType } from 'shared/dto'

export class ClickType {
	@ApiProperty({ type: String })
	id: string

	@ApiProperty({ type: Number })
	uid: string

	@ApiProperty({ type: String })
	referenceId: string

	@ApiProperty({
		type: String,
		enum: ['Pending', 'Confirmed', 'Cancelled', 'Tracked', 'Report Missing CB'],
	})
	status: 'Pending' | 'Confirmed' | 'Cancelled' | 'Tracked' | 'Report Missing CB'

	@ApiProperty({ type: String, enum: ['offer', 'express', 'rates'] })
	type!: 'offer' | 'express' | 'rates'

	@ApiProperty({ type: Boolean })
	canReport: boolean

	@ApiProperty({ type: Boolean })
	fromPreviousMonth: boolean

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	date: string

	@ApiProperty({ type: String })
	time!: string

	@ApiProperty({ type: String })
	logo: string

	@ApiProperty({ type: String })
	createdAt: string
}

export class ClicksResponse {
	@ApiProperty({ type: [ClickType] })
	clicks: ClickType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}

export class ClickByStoreType {
	@ApiProperty({ type: String })
	id: string

	@ApiProperty({ type: Number })
	uid: string

	@ApiProperty({ type: String })
	referenceId: string

	@ApiProperty({
		type: String,
		enum: ['Pending', 'Confirmed', 'Cancelled', 'Tracked', 'Report Missing CB'],
	})
	status: 'Pending' | 'Confirmed' | 'Cancelled' | 'Tracked' | 'Report Missing CB'

	@ApiProperty({ type: String, enum: ['offer', 'express', 'rates'] })
	type!: 'offer' | 'express' | 'rates'

	@ApiProperty({ type: Boolean })
	canReport: boolean

	@ApiProperty({ type: Boolean })
	fromPreviousMonth: boolean

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	date: string

	@ApiProperty({ type: String })
	time!: string
}

export class StoreType {
	@ApiProperty({ type: Number })
	uid: string

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	logo: string

	@ApiProperty({ type: [ClickByStoreType] })
	clicks: ClickByStoreType[]

	@ApiProperty({ type: Number })
	count: number
}

export class ClicksByStoreResponse {
	@ApiProperty({ type: [StoreType] })
	stores: StoreType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}
