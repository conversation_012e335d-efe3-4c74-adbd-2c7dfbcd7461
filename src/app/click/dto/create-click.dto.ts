import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsEnum, IsNumber } from 'class-validator'

export class CreateClickDto {
	@ApiProperty({ enum: ['offer', 'express', 'rates'] })
	@IsEnum(['offer', 'express', 'rates'])
	type: 'offer' | 'express' | 'rates'

	@ApiProperty({ type: Number })
	@IsNumber()
	uid: number
}

export class ClickCreateResponse {
	@ApiProperty({ type: String })
	referenceId: string

	@ApiProperty({ type: String })
	url: string

	@ApiProperty({ type: String })
	logo: string

	@ApiProperty({ type: String, required: false })
	offer: string | undefined

	@ApiPropertyOptional({ type: String, required: false })
	isActive?: boolean
}
