// export type MissingCashbacks = {
// 	missingCashbacks: MissingCashback[];
// 	pagination:       Pagination;
// }

import { ApiProperty } from '@nestjs/swagger'
import { PaginationResponseType } from 'shared/dto'

// export type MissingCashback = {
// 	uid:         number;
// 	storeLogo:   string;
// 	clickedTime: Date;
// 	amount:      number;
// 	title:       string;
// 	status:      Status;
// 	createdAt:   Date;
// }

export class MissingCashbackType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	storeLogo: string

	@ApiProperty({ type: String })
	storeBgColor: string

	@ApiProperty({ type: Date })
	clickedTime: Date

	@ApiProperty({ type: Number })
	amount: number

	@ApiProperty({ type: String })
	title: string

	@ApiProperty({ type: String })
	referenceId: string

	@ApiProperty({ type: String })
	status: string

	@ApiProperty({ type: Date })
	createdAt: Date
}

export class MissingCashbackResponse {
	@ApiProperty({ type: [MissingCashbackType] })
	missingCashbacks: MissingCashbackType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}
