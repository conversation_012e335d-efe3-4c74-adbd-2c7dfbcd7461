import { Modu<PERSON> } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { BotModule } from 'app/bot/bot.module'
import { UserModule } from 'app/user/user.module'
import { MissingCashback, MissingCashbackSchema } from 'shared/entities/missing-cashback.entity'
import { ClickModule } from '../click.module'
import { MissingCashbackController } from './missing-cashback.controller'
import { MissingCashbackService } from './missing-cashback.service'

@Module({
	imports: [
		ClickModule,
		BotModule,
		UserModule,
		MongooseModule.forFeature([
			{
				name: MissingCashback.name,
				schema: MissingCashbackSchema,
			},
		]),
	],
	controllers: [MissingCashbackController],
	providers: [MissingCashbackService],
})
export class MissingCashbackModule {}
