import { HttpException, Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { BotService } from 'app/bot/bot.service'
import { UserService } from 'app/user/user.service'
import mongoose, { Model, Types } from 'mongoose'
import { uploadFileToAwsBucket } from 'shared/decorators'
import { ClickDocument, ReqUser, UserDocument } from 'shared/entities'
import { MissingCashback, MissingCashbackDocument } from 'shared/entities/missing-cashback.entity'
import { ClickService } from '../click.service'
import { GetReportsDto } from './dto/get-reports'
import { ReportMissingCashbackType } from './dto/report.dto'

@Injectable()
export class MissingCashbackService {
	private readonly logger = new Logger(MissingCashbackService.name)

	constructor(
		@InjectModel(MissingCashback.name)
		private missingCashback: Model<MissingCashbackDocument>,
		private clickService: ClickService,
		private readonly botService: BotService,
		private readonly userService: UserService
	) {}

	/**
	 * Format missing cashback alert message with HTML markup
	 */
	private formatMissingCashbackAlertMessage(
		user: UserDocument,
		missingCashback: MissingCashbackDocument,
		dto: ReportMissingCashbackType,
		click: ClickDocument
	): string {
		const userTypeDisplay = dto.userType === 'new' ? 'New User' : 'Existing User'
		const platformDisplay = dto.platform === 'web' ? 'Website' : 'Mobile'
		const couponUsed = dto.coupon ? 'Yes' : 'No'

		const timestamp = new Date().toLocaleString('en-US', {
			timeZone: 'Asia/Kolkata',
			year: 'numeric',
			month: 'short',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			hour12: true,
		})

		const orderDate = click?.createdAt
			? new Date(click.createdAt).toLocaleString('en-US', {
					timeZone: 'Asia/Kolkata',
					year: 'numeric',
					month: 'short',
					day: '2-digit',
				})
			: 'N/A'

		return `<b>🚨 Missing Cashback Report</b>

<b>👤 User Details:</b>
• <b>Name:</b> ${user.name}
• <b>User ID:</b> ${user.uid}
• <b>User Type:</b> ${userTypeDisplay}

<b>🏪 Store & Order Details:</b>
• <b>Store:</b> ${click?.store?.name || 'N/A'}
• <b>Order ID:</b> ${dto.orderId}
• <b>Order Date:</b> ${orderDate}
• <b>Order Amount:</b> ₹${dto.paidAmount}
• <b>Coupon Used:</b> ${couponUsed}

<b>📱 Platform & Context:</b>
• <b>Platform:</b> ${platformDisplay}
• <b>Click Reference:</b> ${click?.referenceId || 'N/A'}
• <b>Complaint ID:</b> ${missingCashback.complaintId}

<b>💬 User Message:</b>
<i>"${dto.message}"</i>

<b>📅 Report Info:</b>
• <b>Reported At:</b> ${timestamp}
• <b>Status:</b> New

<i>Missing cashback report submitted for review</i>`
	}

	async reportMissingCashback(
		dto: ReportMissingCashbackType,
		user: ReqUser,
		imageFile: Express.Multer.File
	): Promise<string> {
		if (!imageFile) {
			throw new HttpException('Invoice is required', 400)
		}
		const invoice = await uploadFileToAwsBucket(imageFile)
		if (!invoice) {
			throw new HttpException('Failed to upload invoice', 500)
		}

		const click = await this.clickService.getClickById(dto.click)

		if (!click) {
			throw new HttpException('Invalid click', 400)
		}

		const userId = new mongoose.Types.ObjectId(user.id)

		const { click: clickIdString, ...dtoWithoutClick } = dto

		const clickId = new mongoose.Types.ObjectId(clickIdString)

		const payload = {
			...dtoWithoutClick,
			user: userId,
			click: clickId,
			title: click.title,
			invoice,
			store: click.store,
			orderDate: click.createdAt,
			affiliation: click.affiliation,
		}
		const result = await this.missingCashback.create(payload)

		// Send missing cashback notification to Telegram
		try {
			// Get user details for the notification
			const userDetails = await this.userService.getUserByEmail(user.email)
			if (userDetails) {
				const alertMessage = this.formatMissingCashbackAlertMessage(userDetails, result, dto, click)

				await this.botService.sendMissingCashbackAlert(alertMessage)

				this.logger.log('Missing cashback notification sent successfully', {
					userId: user.id,
					complaintId: result.complaintId,
					clickId: dto.click,
					orderId: dto.orderId,
					amount: dto.paidAmount,
					store: click.store?.name,
				})
			}
		} catch (error) {
			// Log error but don't fail the missing cashback report
			this.logger.error('Failed to send missing cashback notification', {
				userId: user.id,
				complaintId: result.complaintId,
				clickId: dto.click,
				orderId: dto.orderId,
				amount: dto.paidAmount,
				store: click.store?.name,
				error: error.message,
				stack: error.stack,
			})
		}

		return result.complaintId
	}

	async listMissingCashback(userData: ReqUser, query: GetReportsDto) {
		// const user = new Types.ObjectId(userData.id)
		const user = userData.id
		const filters: Record<string, object> = {}
		filters.user = new Types.ObjectId(user)
		if (query.stores && query.stores.length > 0) {
			filters.store = {
				$in: query.stores.map(storeId => new Types.ObjectId(storeId)),
			}
		}
		if (query.searchParam) {
			filters.$text = { $search: query.searchParam }
		}
		if (query.status && query.status.length > 0) {
			filters.status = { $in: query.status }
		}
		if (query.startDate && query.endDate) {
			filters.createdAt = {
				$gte: new Date(query.startDate),
				$lte: new Date(query.endDate),
			}
		}

		const pageSize = query.pageSize || 10
		const page = query.page || 1

		const missingCashbacks = await this.missingCashback
			.find(filters)
			.populate('store click')
			.sort({
				createdAt: query.sortType === 'oldest' ? 1 : -1,
			})
			.skip((page - 1) * pageSize)
			.limit(pageSize)
			.exec()
		const list = missingCashbacks.map(missingCashback => {
			const { uid, store, status, paidAmount, complaintId, createdAt, click } = missingCashback

			return {
				uid,
				storeLogo: store?.logo?.secureUrl,
				storeBgColor: store?.bgColor,
				clickedTime: click?.createdAt,
				referenceId: complaintId,
				amount: paidAmount,
				title: click?.title,
				status,
				createdAt,
			}
		})
		const totalCount = await this.missingCashback.countDocuments(filters)

		return {
			missingCashbacks: list,
			pagination: {
				page: query.page,
				pageSize: list.length,
				total: totalCount,
			},
		}
	}
}
