import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import {
	ArrayMaxSize,
	ArrayMinSize,
	IsArray,
	IsEmail,
	IsEnum,
	IsMongoId,
	IsNotEmpty,
	IsNumber,
	IsOptional,
	IsString,
	Max,
	Min,
	ValidateNested,
} from 'class-validator'
import { Types } from 'mongoose'
import { PaginationDto } from 'shared/dto'
import { Payment, PaymentType, RedeemHistoryTypes } from 'shared/enums'

class Card {
	@ApiProperty({
		type: Number,
		example: 1000,
	})
	@IsNumber()
	@IsNotEmpty()
	amount: number

	@ApiProperty({
		type: Number,
		example: 1,
	})
	@IsNumber()
	@IsNotEmpty()
	@Min(1)
	@Max(5)
	quantity: number
}

export class OrderGiftCardDto {
	@ApiProperty({
		enum: Payment,
		isArray: true,
		description: 'The types of payment',
		example: ['balance', 'razorpay'],
	})
	@IsArray()
	@IsEnum(Payment, { each: true })
	paymentMethods: PaymentType[]

	@ApiProperty({
		type: String,
		example: '60f5f7e8d3c9e8d5e4e3f3b1',
	})
	@IsNotEmpty()
	@IsMongoId()
	giftcardId: Types.ObjectId

	@ApiProperty({ type: String, example: 'John Doe' })
	@IsNotEmpty()
	name: string

	@ApiProperty({
		type: String,
		example: '<EMAIL>',
	})
	@IsEmail()
	@IsNotEmpty()
	email: string

	@ApiProperty({ type: Number, example: 9_876_543_210, required: false })
	@IsNumber()
	@IsOptional()
	mobile?: number

	@ApiProperty({ required: false })
	@IsOptional()
	msg?: string

	@ApiProperty({
		type: [Card],
		example: [
			{ amount: 1000, quantity: 1 },
			{ amount: 100, quantity: 2 },
			{ amount: 4000, quantity: 3 },
		],
	})
	@IsArray()
	@ArrayMinSize(1)
	@ArrayMaxSize(5)
	@ValidateNested({ each: true })
	@Type(() => Card)
	cards: Card[]
}

export class PaymentVerifyDto {
	@ApiProperty({ description: 'RazorPay payment ID' })
	@IsNotEmpty()
	@IsString()
	paymentId: string

	@ApiProperty({ description: 'RazorPay order ID' })
	@IsNotEmpty()
	@IsString()
	orderId: string

	@ApiProperty({ description: 'RazorPay payment signature' })
	@IsNotEmpty()
	@IsString()
	signature: string
}

export class RedeemIcbGiftCardDto {
	@ApiProperty()
	@IsNotEmpty()
	@IsNumber()
	giftCardNumber: number

	@ApiProperty()
	@IsNotEmpty()
	@IsNumber()
	giftCardPin: number
}
export class GetRedeemGiftCardsDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'ICB123456',
	})
	searchParam!: string

	@IsEnum(RedeemHistoryTypes)
	@IsOptional()
	@ApiProperty({ enum: RedeemHistoryTypes, enumName: 'RedeemHistoryTypes' })
	sortType?: RedeemHistoryTypes

	@IsString()
	@IsOptional()
	@ApiProperty({
		default: '2024-02-25T18:19:26.808Z',
	})
	date!: string
}
