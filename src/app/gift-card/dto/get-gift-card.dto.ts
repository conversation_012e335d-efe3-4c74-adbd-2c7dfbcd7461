import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsOptional, IsString } from 'class-validator'
import { PaginationDto } from 'shared/dto'
import { SortTypes } from 'shared/enums'

export class GetAllGiftCardsDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'a',
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsOptional()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes' })
	sortType?: SortTypes

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	subCategories?: string

	get subCategoriesArray(): number[] | undefined {
		if (this.subCategories) {
			return (
				this.subCategories
					.split(',')
					.map(Number)
					.filter(num => !Number.isNaN(num)) ?? undefined
			)
		}
		return undefined
	}
}
