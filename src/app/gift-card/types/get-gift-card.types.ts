import { ApiProperty } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'
import { Types } from 'mongoose'
import { PaginationResponseType } from 'shared/dto'

export class GetGiftCardType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	id: Types.ObjectId

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	imageUrl: string

	@ApiProperty({ type: String })
	caption: string

	@IsOptional()
	@ApiProperty({ type: Boolean })
	saved?: boolean
}

export class GiftCardBannerType {
	@ApiProperty({ type: String })
	imageUrl: string

	@ApiProperty({ type: String })
	redirectUrl: string

	@ApiProperty({ type: String })
	termsContent: string

	@ApiProperty({ type: String })
	termsTitle: string
}
export class IcbCardTypeResponse {
	@ApiProperty({ type: String })
	uid: string

	@ApiProperty({ type: String })
	name: string
}

export class GiftCardBannersResponse {
	@ApiProperty({ type: [GiftCardBannerType] })
	desktopBanners!: GiftCardBannerType[]

	@ApiProperty({ type: [GiftCardBannerType] })
	mobileBanners!: GiftCardBannerType[]
}

export class GetGiftCardListResponse {
	@ApiProperty({ type: [GetGiftCardType] })
	giftCards: GetGiftCardType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}

export class GiftCardDetails {
	@ApiProperty({ type: String })
	id: Types.ObjectId

	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: Number })
	storeUid: number

	@ApiProperty({ type: String })
	storeLogo: string

	@ApiProperty({ type: String })
	storeBgColor: string

	@ApiProperty({ type: String })
	imageUrl: string

	@ApiProperty({ type: String })
	description: string

	@ApiProperty({ type: Number })
	discountGetting?: number

	@ApiProperty({ type: Boolean })
	showCustomAmount: boolean

	@ApiProperty({ type: Number })
	cashbackGiving: number

	@ApiProperty({ type: [Number] })
	denominations: number[]

	@ApiProperty({ type: String })
	terms: string

	@ApiProperty({ type: [String] })
	howToUse: string[]
}

export class GetGiftCardResponse {
	@ApiProperty({ type: GiftCardDetails })
	giftCard: GiftCardDetails

	@ApiProperty({ type: [GetGiftCardType] })
	similarGiftCards: GetGiftCardType[]
}

export class RedeemGiftCard {
	@ApiProperty({ type: String })
	giftCardId: string

	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	amount: number

	@ApiProperty({ type: String })
	date: string

	@ApiProperty({ type: String })
	time: string

	@ApiProperty({ type: Boolean })
	paid: boolean
}

export class GetRedeemGiftCardListResponse {
	@ApiProperty({ type: [RedeemGiftCard] })
	redeemGiftCards: RedeemGiftCard[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}
