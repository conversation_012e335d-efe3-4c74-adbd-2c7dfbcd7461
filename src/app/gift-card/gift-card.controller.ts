import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common'
import { <PERSON>ron, CronExpression } from '@nestjs/schedule'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { RemoveOfferDto, SaveOfferDto } from 'app/offer/dto/offer.dto'
import { OrderResponse, SaveItemResponse } from 'app/saved-item/types/saved-item.types'
import { Auth, AuthOptional, OptionalUser, User } from 'shared/decorators'
import { ReqUser } from 'shared/entities'
import { GetAllGiftCardsDto } from './dto/get-gift-card.dto'
import {
	GetRedeemGiftCardsDto,
	OrderGiftCardDto,
	PaymentVerifyDto,
	RedeemIcbGiftCardDto,
} from './dto/order-giftcard.dto'
import { GiftCardService } from './gift-card.service'
import {
	GetGiftCardListResponse,
	GetGiftCardResponse,
	GetRedeemGiftCardListResponse,
	GiftCardBannersResponse,
	IcbCardTypeResponse,
} from './types/get-gift-card.types'

@ApiTags('GiftCards')
@Controller('/gift-cards')
export class GiftCardController {
	constructor(private readonly giftCardService: GiftCardService) {}

	@ApiResponse({
		type: GetGiftCardListResponse,
	})
	@AuthOptional()
	@Get()
	async getAllGiftCards(
		@Query() queryParams: GetAllGiftCardsDto,
		@OptionalUser() userSession: ReqUser
	) {
		return this.giftCardService.getAllGiftCards(queryParams, userSession)
	}

	@ApiResponse({
		type: GiftCardBannersResponse,
	})
	@Get('/banners')
	async getAllGiftCardBanners() {
		return this.giftCardService.getAllGiftCardBanners()
	}

	@ApiResponse({
		type: GetGiftCardResponse,
	})
	@AuthOptional()
	@Get('gift-card:id')
	async getGiftCardDetails(@Param('id') id: string, @User() user: ReqUser) {
		return await this.giftCardService.getGiftCardDetails(id, user)
	}

	@ApiResponse({
		type: SaveItemResponse,
	})
	@Auth()
	@Post('/save')
	async saveItem(@Body() createSaveItem: SaveOfferDto, @User() user: ReqUser) {
		return await this.giftCardService.saveGiftCard(createSaveItem, user)
	}

	@ApiResponse({
		type: SaveItemResponse,
	})
	@Auth()
	@Post('/remove')
	async removeSavedItem(@Body() removeSavedItem: RemoveOfferDto, @User() user: ReqUser) {
		return await this.giftCardService.removeGiftCard(user, removeSavedItem)
	}

	@Cron(CronExpression.EVERY_10_SECONDS)
	async handleExpiredSessions() {
		await this.giftCardService.cancelExpiredSessions()
	}

	@ApiResponse({
		type: OrderResponse,
	})
	@Auth()
	@Post('/create-order')
	async orderGiftCard(
		@Body() order: OrderGiftCardDto,
		@User() user: ReqUser
	): Promise<OrderResponse> {
		return await this.giftCardService.orderGiftCard(order, user)
	}

	@Auth()
	@Put('/verify-payment')
	async verifyPayment(@Body() order: PaymentVerifyDto, @User() user: ReqUser) {
		return await this.giftCardService.verifyPayment(order, user)
	}

	@ApiResponse({
		type: SaveItemResponse,
	})
	@Auth()
	@Post('/redeem')
	async redeemIcbGiftCard(@Body() redeemData: RedeemIcbGiftCardDto, @User() user: ReqUser) {
		return await this.giftCardService.redeemIcbGiftCard(redeemData, user)
	}

	@ApiResponse({
		type: GetRedeemGiftCardListResponse,
	})
	@Auth()
	@Get('/redeem')
	async redeemGiftCardHistory(@Query() queryParams: GetRedeemGiftCardsDto, @User() user: ReqUser) {
		return await this.giftCardService.getRedeemGiftCardHistory(queryParams, user)
	}

	@ApiResponse({
		type: IcbCardTypeResponse,
	})
	@Get('/icb-card')
	async getIcbCard() {
		return this.giftCardService.getIcbCard()
	}
}
