import { Types } from 'mongoose'

export function buildGiftCardAggregateQuery({
	subCategoryIds,
	sortQuery,
	skip,
	limit,
	queryConditions,
}: {
	subCategoryIds?: number[]
	sortQuery?: Record<string, 1 | -1>
	skip?: number
	limit?: number
	queryConditions?: object
}) {
	const aggregationPipeline = []

	if (queryConditions) {
		aggregationPipeline.push({
			$match: queryConditions,
		})
	}

	//checking subCategoryIds is present or not
	if (subCategoryIds && subCategoryIds.length > 0) {
		aggregationPipeline.push({
			$match: {
				categories: {
					$elemMatch: {
						subCategories: {
							$elemMatch: {
								uid: { $in: subCategoryIds },
							},
						},
					},
				},
			},
		})
	}

	// Filter pipeline for facet stage
	const filterPipeline = []

	if (sortQuery) {
		filterPipeline.push({ $sort: sortQuery })
	}
	// Skip stage
	if (skip) {
		filterPipeline.push({ $skip: skip })
	}
	// Limit stage
	if (limit) {
		filterPipeline.push({ $limit: limit })
	}
	// Project stage
	filterPipeline.push({
		$project: {
			name: 1,
			_id: 1,
			uid: 1,
			image: 1,
			cashbackGiving: 1,
		},
	})

	// Facet stage
	aggregationPipeline.push({
		$facet: {
			paginatedResults: filterPipeline,
			totalCount: [{ $count: 'count' }],
		},
	})

	return aggregationPipeline
}

export function buildGiftCardRedeemHistoryAggregateQuery({
	loggedUser,
	sortQuery,
	skip,
	limit,
	queryConditions,
}: {
	loggedUser: Types.ObjectId
	subCategoryIds?: number[]
	sortQuery?: Record<string, 1 | -1>
	skip?: number
	limit?: number
	queryConditions?: object
}) {
	const aggregationPipeline = []

	//
	if (loggedUser) {
		aggregationPipeline.push({
			$match: {
				receiver: loggedUser,
			},
		})
	}

	if (queryConditions) {
		aggregationPipeline.push({
			$match: queryConditions,
		})
	}

	// Filter pipeline for facet stage
	const filterPipeline = []

	if (sortQuery) {
		filterPipeline.push({ $sort: sortQuery })
	}
	// Skip stage
	if (skip) {
		filterPipeline.push({ $skip: skip })
	}
	// Limit stage
	if (limit) {
		filterPipeline.push({ $limit: limit })
	}
	// Project stage
	filterPipeline.push({
		$project: {
			// giftCardId:1,
			uid: 1,
			amount: 1,
			redeemDate: 1,
			isRedeemed: 1,
		},
	})

	// Facet stage
	aggregationPipeline.push({
		$facet: {
			paginatedResults: filterPipeline,
			totalCount: [{ $count: 'count' }],
		},
	})

	return aggregationPipeline
}
