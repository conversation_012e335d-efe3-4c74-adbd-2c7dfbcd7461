import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator'
import { PaginationDto } from 'shared/dto'
import { OfferTypes, SortTypes, UserTypes } from 'shared/enums'

export class CouponsAndDealsDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'and',
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsOptional()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes' })
	sortType?: SortTypes

	@IsEnum(UserTypes)
	@IsOptional()
	@ApiProperty({ enum: UserTypes, enumName: 'UserTypes' })
	userType?: UserTypes

	@IsEnum(OfferTypes)
	@IsOptional()
	@ApiProperty({ enum: OfferTypes, enumName: 'OfferTypes' })
	offerType?: OfferTypes

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	subCategories?: string

	get subCategoriesArray(): number[] | undefined {
		if (this.subCategories) {
			return (
				this.subCategories
					.split(',')
					.map(Number)
					.filter(num => !Number.isNaN(num)) ?? undefined
			)
		}
		return
	}

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2' })
	storeId?: string
}

export class OngoingSalesDto extends PaginationDto {
	@IsString()
	@IsOptional()
	@ApiProperty({
		example: 'and',
	})
	searchParam!: string

	@IsEnum(SortTypes)
	@IsOptional()
	@ApiProperty({ enum: SortTypes, enumName: 'SortTypes' })
	sortType?: SortTypes

	@IsEnum(OfferTypes)
	@IsOptional()
	@ApiProperty({ enum: OfferTypes, enumName: 'OfferTypes' })
	offerType?: OfferTypes

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	subCategories?: string

	get subCategoriesArray(): number[] | undefined {
		if (this.subCategories) {
			return (
				this.subCategories
					.split(',')
					.map(Number)
					.filter(num => !Number.isNaN(num)) ?? undefined
			)
		}
		return
	}

	@IsString()
	@IsOptional()
	@ApiProperty({ example: '2,4,6,7' })
	sales?: string

	get saleIdArray(): number[] | undefined {
		if (this.sales) {
			return (
				this.sales
					.split(',')
					.map(Number)
					.filter(num => !Number.isNaN(num)) ?? undefined
			)
		}
		return
	}
}
export class SaveOfferDto {
	@IsNumber()
	@ApiProperty({
		example: 23,
	})
	itemUid: number
}

export class RemoveOfferDto {
	@IsNumber()
	@ApiProperty({
		example: 23,
	})
	itemUid: number
}
