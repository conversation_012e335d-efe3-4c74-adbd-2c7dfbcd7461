import { ApiProperty } from '@nestjs/swagger'
import { IsOptional } from 'class-validator'
import { Types } from 'mongoose'
import { PaginationResponseType } from 'shared/dto'

export class OfferCouponsType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	productImage: string

	@ApiProperty({ type: String })
	storeLogoUrl: string

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: String })
	endDate: string

	@ApiProperty({ type: String })
	offerTitle: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	offerCaption!: string

	@ApiProperty({ type: Number })
	salePrice: number

	@ApiProperty({ type: String })
	couponCode!: string

	@ApiProperty({ type: String })
	repeatBy!: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	offerUrl?: string

	@ApiProperty({ type: Boolean, required: false })
	@IsOptional()
	saved: boolean

	@ApiProperty({ type: Boolean, required: false })
	@IsOptional()
	hideCbTag?: boolean

	@ApiProperty({ type: Boolean, required: false })
	@IsOptional()
	isAutoGenerated?: boolean

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	cashbackType?: 'reward' | 'cashback'

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	offerType?: 'upto' | 'flat'

	@ApiProperty({ type: Number, required: false })
	@IsOptional()
	offerAmount?: number

	@ApiProperty({ type: Number, required: false })
	@IsOptional()
	offerPercent?: number
}

export class OngoingOfferType {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	productImage: string

	@ApiProperty({ type: String })
	storeName: string

	@ApiProperty({ type: String })
	storeLogoUrl: string

	@ApiProperty({ type: String })
	endDate: Date

	@ApiProperty({ type: String })
	offerTitle: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	@IsOptional()
	offerCaption?: string

	@ApiProperty({ type: Number, required: false, nullable: true })
	@IsOptional()
	salePrice?: number | null

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	saleLogoUrl?: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	saleCaption?: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	offerUrl?: string

	@ApiProperty({ type: Boolean, required: false })
	@IsOptional()
	saved?: boolean

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	repeatBy?: string

	@ApiProperty({ type: Boolean, required: false })
	@IsOptional()
	hideCbTag?: boolean
}

export class GetOfferType extends OngoingOfferType {
	@ApiProperty({ type: String })
	storeBgColor: string

	@ApiProperty({ type: Number, required: false })
	@IsOptional()
	newUserRate?: number

	@ApiProperty({ type: Number, required: false })
	@IsOptional()
	oldUserRate?: number

	@ApiProperty({ type: Number, required: false })
	@IsOptional()
	itemPrice?: number

	@ApiProperty({ type: Number, required: false })
	@IsOptional()
	offerPercent?: number

	@ApiProperty({ type: Number })
	offerAmount: number

	@ApiProperty({ type: Number })
	minimumAmount: number

	@ApiProperty({ type: String })
	confirmationTime: string

	@ApiProperty({ type: Boolean, required: false })
	@IsOptional()
	missingAccepted?: boolean

	@ApiProperty({ type: String })
	trackingTime: string

	@ApiProperty({ type: String })
	termsAndConditions?: string

	@ApiProperty({ type: String })
	offerDescription: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	offerWarning?: string

	@ApiProperty({ type: String, enum: ['cashback', 'reward'] })
	cashbackType: 'cashback' | 'reward'

	@ApiProperty({ type: Types.ObjectId })
	storeId: Types.ObjectId

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	keySpecs?: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	importantUpdate?: string

	@ApiProperty({ type: String, enum: ['upto', 'flat'] })
	offerType: string

	@ApiProperty({ type: String, required: false })
	@IsOptional()
	couponCode?: string

	@ApiProperty({ type: Boolean, required: false })
	@IsOptional()
	isExpired?: boolean

	@ApiProperty({ type: Boolean, required: false })
	@IsOptional()
	isAutoGenerated?: boolean

	// @ApiProperty({ type: Boolean, required: false })
	// hideCbTag?: boolean
}

export class AppliedFiltersType {
	@ApiProperty({
		type: [Number],
		description: 'All subcategory IDs applied (explicit + keyword-based)',
	})
	subCategoryIds: number[]

	@ApiProperty({
		type: [Number],
		description: 'Subcategory IDs detected from search keywords',
	})
	keywordBasedSubcategoryIds: number[]
}

export class DealAndCouponsResponse {
	@ApiProperty({ type: [OfferCouponsType] })
	offers: OfferCouponsType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType

	@ApiProperty({ type: AppliedFiltersType, required: false })
	@IsOptional()
	appliedFilters?: AppliedFiltersType
}

export class OngoingOffersResponse {
	@ApiProperty({ type: [OngoingOfferType] })
	offers: OngoingOfferType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}

export class GetOfferByIdResponse {
	@ApiProperty({ type: GetOfferType })
	offer: GetOfferType

	@ApiProperty({ type: [OfferCouponsType] })
	similarOffers: OfferCouponsType[]
}

export class GetSimilarOffers {
	@ApiProperty({ type: [OfferCouponsType] })
	similarOffers: OfferCouponsType[]
}
