import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { <PERSON>ron } from '@nestjs/schedule'
import { ContextOfferDealsType } from 'app/context/types/offers.types'
import { SavedItemService } from 'app/saved-item/saved-item.service'
import { UserService } from 'app/user/user.service'
import mongoose, { Model } from 'mongoose'
import { isEmpty } from 'radash'
import {
	Offer,
	OfferDocument,
	ReqUser,
	SavedItemDocument,
	Store,
	StoreDocument,
	SubCategory,
	SubCategoryDocument,
	UserDocument,
} from 'shared/entities'
import {
	OngoingSaleOffers,
	OngoingSaleOffersDocument,
} from 'shared/entities/ongoing-sale-offers.entity'
import {
	buildOfferAggregateQuery,
	buildOngoingOfferAggregateQuery,
	buildSingleOfferAndSimilarOffers,
	generateOfferCaption,
} from 'shared/helpers/offer.helper'
import {
	convertLocalToUTC,
	convertLocalToUTCDate,
	getFinalDateToShow,
} from 'shared/helpers/time.helper'
import { MeiliSearchService } from 'shared/modules/meilisearch'
import { SavedEnum } from 'shared/types'
import { CouponsAndDealsDto, OngoingSalesDto, RemoveOfferDto, SaveOfferDto } from './dto/offer.dto'
import {
	DealAndCouponsResponse,
	OfferCouponsType,
	OngoingOffersResponse,
	OngoingOfferType,
} from './types/offer.types'

@Injectable()
export class OfferService {
	constructor(
		@InjectModel(Offer.name)
		private offers: Model<OfferDocument>,

		@InjectModel(Store.name)
		private store: Model<StoreDocument>,

		@InjectModel(OngoingSaleOffers.name)
		private ongoingSales: Model<OngoingSaleOffersDocument>,

		@InjectModel(SubCategory.name)
		private readonly subCategories: Model<SubCategoryDocument>,

		private readonly userService: UserService,

		private savedItemService: SavedItemService
	) {}

	/**
	 * Maps search keywords to subcategory names for automatic filtering
	 * This helps users find relevant offers when they search for general terms
	 */
	private getSubcategoryKeywordMapping(): Record<string, string[]> {
		return {
			// Electronics & Technology
			mobile: ['Mobile', 'Smartphones', 'Phone', 'Android', 'iPhone', 'Cellphone', 'Smartphone'],
			laptop: ['Laptop', 'Computer', 'PC', 'MacBook', 'Notebook', 'Ultrabook'],
			electronics: ['Electronics', 'Gadgets', 'Device', 'Tech', 'Technology'],
			tablet: ['Tablet', 'iPad', 'Tab'],
			headphones: ['Headphones', 'Earphones', 'Earbuds', 'Audio'],
			camera: ['Camera', 'Photography', 'DSLR', 'Lens'],

			// Fashion & Lifestyle
			fashion: ['Fashion', 'Clothing', 'Apparel', 'Dress', 'Shirt', 'Jeans', 'Wear'],
			shoes: ['Shoes', 'Footwear', 'Sneakers', 'Boots', 'Sandals', 'Heels'],
			watches: ['Watch', 'Watches', 'Timepiece', 'Smartwatch'],
			bags: ['Bag', 'Bags', 'Backpack', 'Handbag', 'Purse', 'Wallet'],
			sunglasses: ['Sunglasses', 'Glasses', 'Eyewear'],

			// Beauty & Personal Care
			beauty: ['Beauty', 'Cosmetics', 'Makeup', 'Skincare', 'Skincare'],
			perfume: ['Perfume', 'Fragrance', 'Cologne'],

			// Home & Living
			home: ['Home', 'Furniture', 'Decor', 'Kitchen', 'Appliances'],
			furniture: ['Furniture', 'Chair', 'Table', 'Sofa', 'Bed'],
			kitchen: ['Kitchen', 'Cookware', 'Appliance'],

			// Travel & Services
			travel: ['Travel', 'Flight', 'Hotel', 'Trip', 'Vacation', 'Tourism'],
			hotel: ['Hotel', 'Accommodation', 'Resort', 'Booking'],
			flight: ['Flight', 'Airline', 'Air', 'Ticket'],

			// Food & Dining
			food: ['Food', 'Restaurant', 'Dining', 'Grocery', 'Meal'],
			restaurant: ['Restaurant', 'Dining', 'Cafe', 'Eatery'],

			// Automotive
			automotive: ['Car', 'Automotive', 'Vehicle', 'Bike', 'Auto'],

			// Sports & Fitness
			sports: ['Sports', 'Fitness', 'Gym', 'Exercise', 'Workout'],

			// Health & Wellness
			health: ['Health', 'Medical', 'Healthcare', 'Wellness', 'Medicine'],

			// Books & Education
			books: ['Books', 'Reading', 'Literature', 'Novel', 'Education'],

			// Entertainment
			movies: ['Movies', 'Cinema', 'Film', 'Entertainment'],
			games: ['Games', 'Gaming', 'Console', 'PlayStation', 'Xbox'],
			music: ['Music', 'Audio', 'Concert', 'Streaming'],

			// Kids & Baby
			kids: ['Kids', 'Children', 'Baby', 'Toys'],
			toys: ['Toys', 'Games', 'Plaything'],

			// Jewelry & Accessories
			jewelry: ['Jewelry', 'Jewellery', 'Ring', 'Necklace', 'Earring'],

			// Grocery & Essentials
			grocery: ['Grocery', 'Food', 'Essentials', 'Supermarket'],
		}
	}

	/**
	 * Finds subcategory UIDs based on search keywords
	 * Uses fuzzy matching to find relevant subcategories
	 */
	private async getSubcategoriesFromKeywords(searchParam: string): Promise<number[]> {
		const keywordMapping = this.getSubcategoryKeywordMapping()
		const searchWords = searchParam.toLowerCase().split(/\s+/)
		const matchedSubcategoryNames: string[] = []

		// Check each search word against keyword mappings
		for (const word of searchWords) {
			for (const [_category, keywords] of Object.entries(keywordMapping)) {
				const isMatched = keywords.some(keyword => {
					const keywordLower = keyword.toLowerCase()
					// Exact match or partial match (both ways)
					return (
						keywordLower === word ||
						keywordLower.includes(word) ||
						word.includes(keywordLower) ||
						// Handle plurals and similar variations
						this.isSimilarWord(word, keywordLower)
					)
				})

				if (isMatched) {
					matchedSubcategoryNames.push(...keywords)
				}
			}
		}

		if (matchedSubcategoryNames.length === 0) {
			return []
		}

		// Remove duplicates
		const uniqueSubcategoryNames = [...new Set(matchedSubcategoryNames)]

		// Find subcategories that match the keywords (case-insensitive)
		const subcategories = await this.subCategories
			.find({
				$or: uniqueSubcategoryNames.map(name => ({
					name: { $regex: new RegExp(name, 'i') },
				})),
				active: true,
			})
			.exec()

		return subcategories.map(sc => sc.uid)
	}

	/**
	 * Helper method to check if two words are similar (handles plurals, etc.)
	 */
	private isSimilarWord(word1: string, word2: string): boolean {
		// Handle common plural/singular variations
		const singularWord1 = word1.endsWith('s') ? word1.slice(0, -1) : word1
		const singularWord2 = word2.endsWith('s') ? word2.slice(0, -1) : word2

		return singularWord1 === singularWord2 || singularWord1 === word2 || word1 === singularWord2
	}

	async getAllOffers(
		queryParams: CouponsAndDealsDto,
		userSession: ReqUser
	): Promise<DealAndCouponsResponse> {
		const skipAvailabilityCheck = false

		if (!skipAvailabilityCheck) {
			await this.storeOfferAvailabilityCheck(queryParams)
		}

		const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Offer], user._id)
			: []

		// If search parameter is provided, use MeiliSearch
		if (queryParams.searchParam && queryParams.searchParam.trim() !== '') {
			const client = new MeiliSearchService()
			const { hits: response } = await client.searchDocuments(queryParams.searchParam)

			const today = new Date()

			// Filter results to only include active offers
			let offerData = response.filter(
				item =>
					item.type === 'offer' &&
					item.active &&
					item.dateExpiry &&
					new Date(item.dateExpiry) >= today
			)

			// Apply offer type filtering (coupons vs deals)
			if (queryParams.offerType) {
				switch (queryParams.offerType) {
					case 'coupons':
						offerData = offerData.filter(item => !isEmpty(item.couponCode))
						break
					case 'deals':
						offerData = offerData.filter(item => isEmpty(item.couponCode))
						break
					case 'trending':
						// For trending, we might need additional logic or keep all
						break
					default:
						// 'both' or undefined - keep all offers
						break
				}
			}

			// SMART SUBCATEGORY FILTERING:
			// Get subcategory IDs from both explicit params and intelligent keyword detection
			// The applied filters will be returned to the frontend for transparency
			let subCategoryIds: number[] = []

			// If explicit subcategories are provided
			if (queryParams.subCategories) {
				subCategoryIds = queryParams.subCategories.split(',').map(Number).filter(Boolean)
			}

			// Add subcategories based on search keywords
			const keywordBasedSubcategoryIds = await this.getSubcategoriesFromKeywords(
				queryParams.searchParam
			)

			// Log keyword-based filtering for debugging
			if (keywordBasedSubcategoryIds.length > 0) {
			}

			// Combine both explicit and keyword-based subcategory IDs
			subCategoryIds = [...new Set([...subCategoryIds, ...keywordBasedSubcategoryIds])]

			// Apply subcategory filtering if we have any subcategory IDs
			if (subCategoryIds.length > 0) {
				// Get subcategory names from UIDs for filtering
				const subCategories = await this.subCategories.find({ uid: { $in: subCategoryIds } }).exec()

				const subCategoryNames = subCategories.map(sc => sc.name)

				if (subCategoryNames.length > 0) {
					offerData = offerData.filter(item =>
						item.subcategories?.some(
							subcat => subcat.name && subCategoryNames.includes(subcat.name)
						)
					)
				}
			}

			// If no offers found, return empty result
			if (offerData.length === 0) {
				return {
					offers: [],
					pagination: {
						page: queryParams.page || 1,
						pageSize: 0,
						total: 0,
					},
					appliedFilters: {
						subCategoryIds,
						keywordBasedSubcategoryIds,
					},
				}
			}

			// Apply pagination to the filtered results
			const page = queryParams.page || 1
			const pageSize = queryParams.pageSize || 10
			const skip = (page - 1) * pageSize
			const paginatedOffers = offerData.slice(skip, skip + pageSize)

			// Get offer IDs from MeiliSearch results
			const offerIds = paginatedOffers.map((item: { id: string }) => item.id)

			// Fetch the actual offer documents from MongoDB using the IDs from MeiliSearch
			const offersFromDb = await this.offers
				.find({ _id: { $in: offerIds } })
				.populate('store', 'name logo bgColor cashbackType')
				.exec()

			// If no offers found in MongoDB, return empty result
			if (offersFromDb.length === 0) {
				return {
					offers: [],
					pagination: {
						page,
						pageSize: 0,
						total: 0,
					},
					appliedFilters: {
						subCategoryIds,
						keywordBasedSubcategoryIds,
					},
				}
			}

			// Sort offers in the same order as offerIds from MeiliSearch
			const orderedOffers = offerIds
				.map(id => offersFromDb.find(offer => offer._id.toString() === id))
				.filter(Boolean) as OfferDocument[]

			// Apply MongoDB-level sorting if specified
			if (queryParams.sortType) {
				switch (queryParams.sortType) {
					case 'newest':
						orderedOffers.sort((a, b) => {
							const dateA = a.dateExpiry ? new Date(a.dateExpiry).getTime() : 0
							const dateB = b.dateExpiry ? new Date(b.dateExpiry).getTime() : 0
							return dateB - dateA
						})
						break
					case 'alphabetical':
						orderedOffers.sort((a, b) => a.title.localeCompare(b.title))
						break
					case 'highestCbAmount':
						orderedOffers.sort((a, b) => (b.offerAmount || 0) - (a.offerAmount || 0))
						break
					case 'highestCbPercent':
						orderedOffers.sort((a, b) => (b.offerPercent || 0) - (a.offerPercent || 0))
						break
					default:
						// Keep MeiliSearch order for relevance
						break
				}
			}

			// Map the offers to the expected format and add saved state
			const offersWithSavedState = orderedOffers.map(offer => {
				const isSaved = usersSavedOffers.some(savedOffer => savedOffer.itemUid === offer.uid)

				return {
					uid: offer.uid,
					productImage: offer.productImage?.secureUrl || '',
					storeLogoUrl: offer.store?.logo?.secureUrl || '',
					storeName: offer.store?.name || '',
					endDate:
						offer?.repeatBy === 'true'
							? getFinalDateToShow(offer.dateExpiry as unknown as string)
							: (offer?.dateExpiry as unknown as string),
					offerTitle: offer.title,
					offerCaption: offer.offer || '',
					salePrice: offer.itemPrice || 0,
					couponCode: offer.couponCode || '',
					repeatBy: offer.repeatBy || '',
					offerUrl: offer.url || '',
					saved: isSaved,
					hideCbTag: offer.hideCbTag,
					isAutoGenerated: offer.isAutoGenerated,
					cashbackType: offer.store?.cashbackType,
					offerType: offer.offerType,
					offerAmount: offer.offerAmount,
					offerPercent: offer.offerPercent,
				}
			})

			return {
				offers: offersWithSavedState,
				pagination: {
					page,
					pageSize: orderedOffers.length,
					total: offerData.length,
				},
				appliedFilters: {
					subCategoryIds,
					keywordBasedSubcategoryIds,
				},
			}
		}

		// Fallback to existing aggregation pipeline for non-search queries
		const subCategoryIds =
			queryParams?.subCategories && queryParams.subCategories.length > 0
				? (queryParams?.subCategories?.split(',').map(Number) ?? [])
				: []

		// First, fetch the subcategory documents to get their _ids
		const subCategories = await this.subCategories.find({ uid: { $in: subCategoryIds } }).exec()

		// Extract the _ids
		const subCategoryObjectIds = subCategories.map(sc => sc._id.toString())

		const aggregationPipeline = await buildOfferAggregateQuery(queryParams, subCategoryObjectIds)
		const filteredOffers = await this.offers.aggregate(aggregationPipeline).exec()

		const offersWithSavedState = filteredOffers[0].documents.map((offer: OngoingOfferType) => {
			const isSaved = usersSavedOffers.some(savedOffer => savedOffer.itemUid === offer.uid)

			return {
				...offer,
				saved: isSaved,
				endDate:
					offer?.repeatBy === 'true'
						? getFinalDateToShow(offer.endDate as unknown as string)
						: (offer?.endDate as unknown as string),
			}
		})
		return {
			offers: offersWithSavedState,
			pagination: {
				page: queryParams.page,
				pageSize: filteredOffers[0].documents?.length,
				total: filteredOffers[0]?.totalCount[0]?.total ?? 0,
			},
			appliedFilters: {
				subCategoryIds,
				keywordBasedSubcategoryIds: [], // No keyword-based filtering in aggregation pipeline
			},
		}
	}

	async getOngoingOffers(
		queryParams: OngoingSalesDto,
		userSession?: ReqUser
	): Promise<OngoingOffersResponse> {
		const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Offer], user._id)
			: []

		// If search parameter is provided, use MeiliSearch
		if (queryParams.searchParam && queryParams.searchParam.trim() !== '') {
			const client = new MeiliSearchService()
			const { hits: response } = await client.searchDocuments(queryParams.searchParam)

			const today = new Date()

			// Filter results to only include active offers
			let offerData = response.filter(
				item =>
					item.type === 'offer' &&
					item.active &&
					item.dateExpiry &&
					new Date(item.dateExpiry) >= today
			)

			// Apply offer type filtering if specified
			if (queryParams.offerType) {
				switch (queryParams.offerType) {
					case 'coupons':
						offerData = offerData.filter(item => !isEmpty(item.couponCode))
						break
					case 'deals':
						offerData = offerData.filter(item => isEmpty(item.couponCode))
						break
					case 'trending':
						// For trending, we might need additional logic or keep all
						break
					default:
						// 'both' or undefined - keep all offers
						break
				}
			}

			// SMART SUBCATEGORY FILTERING:
			// Get subcategory IDs from both explicit params and intelligent keyword detection
			let subCategoryIds: number[] = []

			// If explicit subcategories are provided
			if (queryParams.subCategories) {
				subCategoryIds = queryParams.subCategories.split(',').map(Number).filter(Boolean)
			}

			// Add subcategories based on search keywords
			const keywordBasedSubcategoryIds = await this.getSubcategoriesFromKeywords(
				queryParams.searchParam
			)

			// Combine both explicit and keyword-based subcategory IDs
			subCategoryIds = [...new Set([...subCategoryIds, ...keywordBasedSubcategoryIds])]

			// Apply subcategory filtering if we have any subcategory IDs
			if (subCategoryIds.length > 0) {
				// Get subcategory names from UIDs for filtering
				const subCategories = await this.subCategories.find({ uid: { $in: subCategoryIds } }).exec()
				const subCategoryNames = subCategories.map(sc => sc.name)

				if (subCategoryNames.length > 0) {
					offerData = offerData.filter(item =>
						item.subcategories?.some(
							subcat => subcat.name && subCategoryNames.includes(subcat.name)
						)
					)
				}
			}

			// If no offers found, return empty result
			if (offerData.length === 0) {
				return {
					offers: [],
					pagination: {
						page: queryParams.page || 1,
						pageSize: 0,
						total: 0,
					},
				}
			}

			// Get offer IDs from MeiliSearch results
			const offerIds = offerData.map((item: { id: string }) => item.id)
			// Convert string IDs to ObjectIds for MongoDB query
			const offerObjectIds = offerIds.map(id => new mongoose.Types.ObjectId(id))

			// Now find ongoing sales that contain these offers
			const ongoingSalesWithOffers = await this.ongoingSales
				.find({
					offers: { $in: offerObjectIds },
					saleEndDate: { $gt: new Date() },
					active: true,
				})
				.exec()

			// Manually populate offers for each ongoing sale
			for (const ongoingSale of ongoingSalesWithOffers) {
				const populatedOffers = await this.offers
					.find({
						_id: {
							$in: ongoingSale.offers.filter(id =>
								offerObjectIds.some(searchId => searchId.toString() === id.toString())
							),
						},
					})
					.populate('store', 'name logo bgColor cashbackType')
					.exec()

				ongoingSale.offers = populatedOffers as any
			}

			// If no ongoing sales found, return empty result
			if (ongoingSalesWithOffers.length === 0) {
				return {
					offers: [],
					pagination: {
						page: queryParams.page || 1,
						pageSize: 0,
						total: 0,
					},
				}
			}

			// Apply sorting if specified
			if (queryParams.sortType) {
				switch (queryParams.sortType) {
					case 'newest':
						ongoingSalesWithOffers.sort((a, b) => {
							const dateA = a.saleEndDate ? new Date(a.saleEndDate).getTime() : 0
							const dateB = b.saleEndDate ? new Date(b.saleEndDate).getTime() : 0
							return dateB - dateA
						})
						break
					case 'alphabetical':
						ongoingSalesWithOffers.sort((a, b) =>
							(a.saleName || '').localeCompare(b.saleName || '')
						)
						break
					default:
						// Keep original order for relevance
						break
				}
			}

			// Transform the ongoing sales to the expected format
			const transformedOffers: OngoingOfferType[] = []

			for (const ongoingSale of ongoingSalesWithOffers) {
				// Check if offers is an array and has items
				if (Array.isArray(ongoingSale.offers) && ongoingSale.offers.length > 0) {
					// Since we already filtered during manual populate, use all offers
					const filteredOffers = ongoingSale.offers

					for (const offer of filteredOffers) {
						// Add null check for offer
						if (!offer) {
							continue
						}

						const isSaved = usersSavedOffers.some(savedOffer => savedOffer.itemUid === offer.uid)

						transformedOffers.push({
							uid: offer.uid,
							productImage: offer.productImage?.secureUrl || '',
							storeName: offer.store?.name || '',
							storeLogoUrl: offer.store?.logo?.secureUrl || '',
							endDate: (offer?.repeatBy === 'true'
								? getFinalDateToShow(offer.dateExpiry as unknown as string)
								: (offer?.dateExpiry as unknown as string)) as unknown as Date,
							offerTitle: offer.title,
							offerCaption: offer.offer || '',
							salePrice: offer.itemPrice ?? null,
							saleLogoUrl: ongoingSale.saleLogo?.secureUrl || '',
							saleCaption: ongoingSale.saleName || '',
							offerUrl: offer.url || '',
							saved: isSaved,
							repeatBy: offer.repeatBy || '',
							hideCbTag: offer.hideCbTag,
						})
					}
				} else {
				}
			}

			// Remove duplicates based on uid
			const uniqueOffers = transformedOffers.filter(
				(offer, index, self) => index === self.findIndex(o => o.uid === offer.uid)
			)

			// Apply pagination
			const page = queryParams.page || 1
			const pageSize = queryParams.pageSize || 10
			const skip = (page - 1) * pageSize
			const paginatedOffers = uniqueOffers.slice(skip, skip + pageSize)

			return {
				offers: paginatedOffers,
				pagination: {
					page,
					pageSize: paginatedOffers.length,
					total: uniqueOffers.length,
				},
			}
		}

		// Fallback to existing aggregation pipeline for non-search queries
		const subCategoryIds =
			queryParams?.subCategories && queryParams.subCategories.length > 0
				? (queryParams?.subCategories?.split(',').map(Number) ?? [])
				: []

		// First, fetch the subcategory documents to get their _ids
		const subCategories = await this.subCategories.find({ uid: { $in: subCategoryIds } }).exec()

		// Extract the _ids
		const subCategoryObjectIds = subCategories.map(sc => sc._id.toString())

		const aggregationPipeline = buildOngoingOfferAggregateQuery(queryParams, subCategoryObjectIds)

		const filteredOffers = await this.ongoingSales.aggregate(aggregationPipeline).exec()

		const offersWithSavedState = filteredOffers[0].documents
			.filter(
				(offer: OngoingOfferType, index: number, self: OngoingOfferType[]) =>
					index === self.findIndex((o: OngoingOfferType) => o.uid === offer.uid)
			)
			.map((offer: OngoingOfferType) => ({
				...offer,
				saved: usersSavedOffers.some(savedOffer => savedOffer.itemUid === offer.uid),
				endDate:
					offer?.repeatBy === 'true'
						? getFinalDateToShow(offer.endDate as unknown as string)
						: (offer?.endDate as unknown as string),
			}))

		return {
			offers: offersWithSavedState,
			pagination: {
				page: queryParams.page,
				pageSize: filteredOffers[0].documents?.length,
				total: filteredOffers[0]?.totalCount[0]?.total ?? 0,
			},
		}
	}

	async getPopulatedOfferByUid(uid: number) {
		return await this.offers.findOne({ uid }).populate('store affiliation').exec()
	}

	async getOfferByUid(uid: number) {
		return await this.offers.findOne({ uid }).exec()
	}

	async saveOffer(saveParams: SaveOfferDto, userSession: ReqUser) {
		const user = (await this.userService.getUserByEmail(userSession.email)) as UserDocument

		if (!user) {
			throw new NotFoundException('User not found')
		}

		const offer = await this.getOfferByUid(saveParams.itemUid)

		if (!offer) {
			throw new NotFoundException('Offer not found')
		}

		await this.savedItemService.saveItem(user, SavedEnum.Offer, offer)
		return {
			message: 'Offer saved successfully',
		}
	}

	async removeSavedOffer(userSession: ReqUser, params: RemoveOfferDto) {
		const user = await this.userService.getUserByEmail(userSession.email)

		if (!user) {
			throw new NotFoundException('User not found')
		}

		// Remove the saved item
		await this.savedItemService.removeItem(user, SavedEnum.Offer, params.itemUid)

		return {
			message: 'Offer removed successfully.',
		}
	}
	async getOfferById(id: string) {
		const offerDetails = (await this.offers
			.findById(id)
			.populate(
				'store',
				'name affiliation minimumAmount maximumAmount missingAccepted confirmationTime logo bgColor '
			)
			.populate('storeCategory', 'gettingNewUserRate gettingOldUserRate')
			.exec()) as Offer

		if (!offerDetails) {
			throw new NotFoundException('Offer  not found')
		}

		const result = {
			uid: offerDetails.uid,
			productImage: offerDetails.productImage.secureUrl,
			offerTitle: offerDetails.title,
			offerCaption: offerDetails.offer,
			salePrice: offerDetails.itemPrice,
			discount: offerDetails.discount,
			offerPercentage: offerDetails.offerPercent,
			storeLogoUrl: offerDetails.store.logo.secureUrl,
			description: offerDetails.description,
			terms: offerDetails.terms,
			storeName: offerDetails.store.name,
			minimumAmount: offerDetails.store.minimumAmount,
			trackingTime: offerDetails.store.trackingTime,
			missingAccepted: offerDetails.store.missingAccepted,
			confirmationTime: offerDetails.store.confirmationTime,
			endDate: offerDetails.dateExpiry as Date,
			newUserRate: offerDetails?.storeCategory?.gettingNewUserRate,
			oldUserRate: offerDetails?.storeCategory?.gettingOldUserRate,
		}

		return { offer: result }
	}

	async getOfferAndSimilarOffers(uid: number, userSession?: ReqUser) {
		const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Offer], user._id)
			: []
		const offerDetails = (await this.offers
			.findOne({ uid })
			.populate(
				'store',
				'name minimumAmount missingAccepted repeatBy confirmationTime logo trackingTime offerWarning bgColor isAutoGenerated hideCbTag'
			)
			.populate({
				path: 'storeCategory',
				select: 'gettingNewUserRate gettingOldUserRate',
				options: { strictPopulate: false }, // Prevents errors if storeCategory is missing
			})
			.exec()) as OfferDocument

		if (!offerDetails) {
			throw new NotFoundException('Offer  not found')
		}
		const aggregatePipeline = buildSingleOfferAndSimilarOffers(uid, offerDetails.store._id)
		const similarOffers = await this.offers.aggregate(aggregatePipeline).exec()

		const similarOffersWithSavedState = similarOffers
			.filter(
				(offer: OfferCouponsType, index: number, self: OfferCouponsType[]) =>
					index === self.findIndex((o: OfferCouponsType) => o.uid === offer.uid)
			)
			.map((offer: OfferCouponsType) => ({
				...offer,
				saved: usersSavedOffers.some(savedOffer => savedOffer.itemUid === offer.uid),
				endDate:
					offer?.repeatBy === 'true'
						? getFinalDateToShow(offer.endDate as unknown as string)
						: (offer?.endDate as unknown as string),
			}))

		const result = {
			uid: offerDetails.uid, //
			productImage: offerDetails.productImage.secureUrl, //
			offerTitle: offerDetails.title, //
			offerCaption: generateOfferCaption({
				offerType: offerDetails?.offerType,
				offerAmount: offerDetails?.offerAmount,
				offerPercent: offerDetails?.offerPercent,
				cashbackType: offerDetails?.store?.cashbackType,
			}),
			cashbackType: offerDetails?.store?.cashbackType,
			offerAmount: offerDetails.offerAmount, //
			itemPrice: offerDetails.itemPrice,
			offerPercent: offerDetails.offerPercent,
			storeLogoUrl: offerDetails.store.logo.secureUrl, //
			storeBgColor: offerDetails?.store?.bgColor,
			offerDescription: offerDetails.description, //
			termsAndConditions: offerDetails.terms, //
			storeName: offerDetails?.store?.name, //
			storeId: offerDetails?.store?._id,
			minimumAmount: offerDetails?.store?.minimumAmount, //
			trackingTime: offerDetails?.store?.trackingTime, //
			missingAccepted: offerDetails?.store?.missingAccepted, //
			confirmationTime: offerDetails?.store?.confirmationTime, //
			endDate:
				offerDetails?.repeatBy === 'true'
					? getFinalDateToShow(offerDetails.dateExpiry as unknown as string)
					: (offerDetails?.dateExpiry as unknown as string),
			newUserRate: offerDetails?.storeCategory?.gettingNewUserRate, //
			oldUserRate: offerDetails?.storeCategory?.gettingOldUserRate, //
			offerWarning: offerDetails?.store?.offerWarning, //
			keySpecs: offerDetails?.keySpecs, //
			importantUpdate: offerDetails?.importantUpdate,
			offerType: offerDetails?.offerType,
			couponCode: offerDetails?.couponCode,
			isExpired:
				offerDetails?.active === false ||
				new Date(offerDetails?.dateExpiry as Date) < new Date(convertLocalToUTC()),
			isAutoGenerated: offerDetails?.isAutoGenerated,
			hideCbTag: offerDetails?.hideCbTag,
		}

		return { offer: result, similarOffers: similarOffersWithSavedState }
	}

	async getOffersByTitleAndSimilarOffers(title: string, userSession?: ReqUser) {
		const user = userSession ? await this.userService.getUserByEmail(userSession?.email) : null
		const usersSavedOffers: SavedItemDocument[] = user
			? await this.savedItemService.findSavedItemsByUserAndType([SavedEnum.Offer], user._id)
			: []

		// Extract the first part of the title before the hyphen and convert to lowercase
		const sanitizedTitle = (title?.split('-')[0] ?? '').toLowerCase()

		// Find the store by name
		const store = await this.store
			.findOne({
				name: { $regex: new RegExp(sanitizedTitle, 'i') },
			})
			.exec()

		if (!store) {
			// Fetch trending offers with a limit of 15
			const trendingDeals: Offer[] = await this.offers
				.find({
					trending: true,
					dateExpiry: { $gt: convertLocalToUTC() },
					active: true,
				}) // Filter expired offers
				.limit(15) // Limit to 15 results
				.populate('store')
				.sort({ trendingPriority: -1, dateExpiry: -1 })
				.exec()

			const formattedTrendingDeal: ContextOfferDealsType[] = trendingDeals.map(offer => {
				// Check if the offer is saved by the user based on uid
				const saved = usersSavedOffers.some(savedItem => savedItem.itemUid === offer.uid)
				return {
					uid: offer?.uid,
					offerCaption: offer?.offer,
					productImage: offer?.productImage?.secureUrl,
					storeLogoUrl: offer?.store?.logo?.secureUrl,
					storeBgColor: offer?.store?.bgColor,
					storeName: offer?.store?.name,
					offerPercentage: offer?.offerPercent,
					repeatedBy: offer?.repeatBy,
					endDate:
						offer?.repeatBy === 'true'
							? getFinalDateToShow(offer.dateExpiry as unknown as string)
							: (offer?.dateExpiry as unknown as string),
					offerTitle: offer?.title,
					...(offer?.offer && offer?.offer.trim() !== '' ? { offerCaption: offer?.offer } : {}),
					salePrice: offer.itemPrice ? offer?.itemPrice : 0,
					...(offer.migrated ? { offerUrl: offer?.url } : {}),
					saved,
				}
			})

			return { similarOffers: formattedTrendingDeal }
		}

		const aggregatePipeline = buildSingleOfferAndSimilarOffers(0, store._id)
		const similarOffers = await this.offers.aggregate(aggregatePipeline).exec()

		const similarOffersWithSavedState = similarOffers
			.filter(
				(offer: OfferCouponsType, index: number, self: OfferCouponsType[]) =>
					index === self.findIndex((o: OfferCouponsType) => o.uid === offer.uid)
			)
			.map((offer: OfferCouponsType) => ({
				...offer,
				saved: usersSavedOffers.some(savedOffer => savedOffer.itemUid === offer.uid),
				endDate:
					offer?.repeatBy === 'true'
						? getFinalDateToShow(offer.endDate as unknown as string)
						: (offer?.endDate as unknown as string),
			}))

		return { similarOffers: similarOffersWithSavedState }
	}

	// @Cron('*/20 * * * * *')

	@Cron('0 0 * * *') // Runs every day at midnight
	async autoGenerateOffersCron() {
		const stores = await this.store.find({ active: true })
		let _count = 0
		const _newStoreUid = []

		for (const storeItem of stores) {
			const offer = await this.offers.findOne({
				store: new mongoose.Types.ObjectId(storeItem?.id),
				active: true,
				dateExpiry: { $gt: convertLocalToUTCDate() },
			})

			if (!offer) {
				_count += 1
				await this.checkAndUpdateOffersForStore(storeItem.id)
				// const newOffer = await this.generateOfferFromStoreId(storeItem.id)
				// if (newOffer) {
				// 	newStoreUid.push(newOffer.uid)
				// 	console.log(
				// 		`[CRON] New offer created for store: ${storeItem.name} (ID: ${storeItem.id}) | Offer UID: ${newOffer.uid}`,
				// 	)
				// }
			}
		}
	}

	@Cron('*/10 * * * *') // Runs every 10 minutes
	async deactivateOffersForInactiveStoresCron() {
		try {
			// Fetch all active offers populated with store information
			const activeOffers = await this.offers
				.find({ active: true })
				.populate('store', 'active name')
				.exec()

			let deactivatedCount = 0
			const offersToDeactivate = []

			// Check each offer's store status
			for (const offer of activeOffers) {
				// If store is not active, mark offer for deactivation
				if (!offer.store?.active) {
					offersToDeactivate.push(offer._id)
				}
			}

			// Bulk update offers to inactive status
			if (offersToDeactivate.length > 0) {
				const updateResult = await this.offers.updateMany(
					{ _id: { $in: offersToDeactivate } },
					{ $set: { active: false } }
				)

				deactivatedCount = updateResult.modifiedCount
			} else {
			}

			return {
				success: true,
				totalOffersChecked: activeOffers.length,
				offersDeactivated: deactivatedCount,
				message: `Processed ${activeOffers.length} active offers, deactivated ${deactivatedCount} offers for inactive stores`,
			}
		} catch (error) {
			return {
				success: false,
				error: error.message,
				message: 'Failed to deactivate offers for inactive stores',
			}
		}
	}

	async storeOfferAvailabilityCheck(queryParams: CouponsAndDealsDto) {
		if (queryParams.storeId) {
			await this.checkAndUpdateOffersForStore(queryParams.storeId)
		} else if (queryParams.subCategories) {
			const storesList = await this.getStoresBySubCategories(queryParams.subCategories)

			for (const store of storesList) {
				await this.checkAndUpdateOffersForStore(store._id.toString())
			}
		}
	}

	private async checkAndUpdateOffersForStore(storeId: string) {
		const storeObjectId = new mongoose.Types.ObjectId(storeId)
		const baseQuery = {
			store: storeObjectId,
			active: true,
			dateExpiry: { $gt: convertLocalToUTCDate() },
		}

		const totalOfferCount = await this.offers.countDocuments(baseQuery)

		if (totalOfferCount === 0) {
			const inactiveAutomatedOfferCount = await this.offers.countDocuments({
				...baseQuery,
				isAutoGenerated: true,
				active: false,
			})

			if (inactiveAutomatedOfferCount === 0) {
				await this.generateOfferFromStoreId(storeId)

				return
			}
			await this.offers.updateMany(
				{ ...baseQuery, isAutoGenerated: true, active: false },
				{ $set: { active: true } }
			)

			return
		}

		const automatedOfferCount = await this.offers.countDocuments({
			...baseQuery,
			isAutoGenerated: true,
		})

		if (totalOfferCount > 1 && automatedOfferCount >= 1) {
			await this.offers.updateMany(
				{ ...baseQuery, isAutoGenerated: true },
				{ $set: { active: false } }
			)
		}
	}

	private async getStoresBySubCategories(subCategoriesParam: string): Promise<StoreDocument[]> {
		const subCategoryIds = subCategoriesParam.split(',').map(Number).filter(Boolean)

		if (subCategoryIds.length === 0) {
			return []
		}

		const subCategories = await this.subCategories.find({ uid: { $in: subCategoryIds } }).exec()

		const objectIdSubcategoryIds = subCategories.map(sc => new mongoose.Types.ObjectId(sc._id))

		return this.store
			.find({
				'categories.subCategories': {
					$elemMatch: { $in: objectIdSubcategoryIds },
				},
			})
			.populate('categories.category')
			.populate('categories.subCategories')
			.exec()
	}

	async generateOfferFromStoreId(storeId: string) {
		const currentStore: Store = (await this.store.findOne({
			_id: storeId,
			active: true,
		})) as Store

		if (!currentStore) {
			return
		}

		const updatedStoreOffer = await this.updateStoreOffer(currentStore.storeOffer)

		// Update the title of the automated offer
		const updatedTitle = `Get ${currentStore.offerType === 'upto' ? 'Up to' : ''} ${updatedStoreOffer} from us on top of all other offers from ${currentStore.name}`

		const newOfferParams = {
			isAutoGenerated: true,
			hideCbTag: true,
			store: new mongoose.Types.ObjectId(storeId),
			offerAmount: currentStore?.cashbackAmount ? currentStore?.cashbackAmount : '',
			offerPercent: currentStore?.cashbackPercent ? currentStore?.cashbackPercent : '',
			offerType: currentStore.offerType,
			affiliation: currentStore.affiliation,
			title: updatedTitle,
			discount: '',
			offer: currentStore?.storeOffer,
			itemPrice: '',
			link: currentStore?.affiliateLink ?? '',
			isCoupon: false,
			couponCode: '',
			description: `<ul>
  <li>ICB cashback can be redeemed as real cash, 1 ICB cashback = INR 1</li>
  <li>ICB cashback is on top of all other offers available at ${currentStore?.name}</li>
  <li>Check Terms & Conditions for more details. <a href="https://indiancashback.com/terms-and-conditions/" style="color: #7366d9;" target="_blank" rel="noopener noreferrer">Click here</a> </li>
  <li>If you have any doubts you can connect with us <a href="https://tawk.to/indiancashback" style="color: #7366d9;" target="_blank" rel="noopener noreferrer">here</a>.</li>
</ul>`,
			keySpecs: '',
			dateStart: convertLocalToUTCDate(),
			dateExpiry: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
			categories: currentStore?.categories ?? [],
			stockEnds: false,
			repeatBy: 'true',
			terms: `<p>&nbsp;</p><blockquote><p><strong>Terms and conditions to get this offer:</strong></p><ul><li>Offer valid only till stock ends.&nbsp;</li><li>You will get cashback only if you purchased from the automatically opened merchant’s website by indiancashback.com&nbsp;</li><li>Restrictions may apply in some cases.</li><li>Cashback is not applicable if the sale is cancelled or if the goods are returned.</li><li>Cashback is often paid excluding VAT, delivery and other administrative charges.</li><li>The usage of coupon will reflect in cashback amount at the time of pending cashback amount raises to approved state.</li><li>You can raise a missing cashback ticket for your transaction within 30 days of the purchase date. Any missing cashback ticket after 30 days will not be accepted by the merchant site.</li><li>Cashback may not paid on purchases made using store credits &amp; Gift vouchers.&nbsp;</li><li>Using a Coupon found outside&nbsp;<a href="https://indiancashback.com/">indiancashback com</a>&nbsp; may void your Cashback.&nbsp;</li><li>Any fraudulent behavior may result cancellation of your Cashback and you may get blacklisted by the merchant.&nbsp;</li><li>Cashback is not applicable if the sale is made using store credits.<br>&nbsp;</li></ul><p><br>&nbsp;</p></blockquote>`,
			productImage: currentStore?.logo,
			importantUpdate: '',
			userType: 'both',
			createdBy: new mongoose.Types.ObjectId('66f5900c0cd63ffff0ce2cd6'), // auto bot user id
		}

		const newOffer = await new this.offers(newOfferParams).save()

		return newOffer
	}

	async updateAutomatedOffers() {
		const stores = await this.store.find({
			active: true,
			//  _id: new mongoose.Types.ObjectId('66f206ec28628113b4856f42')
		})
		// let count = 0;
		const updatedOffers = []

		for (const storeItem of stores) {
			// Check for active automated offers
			const automatedOffers = await this.offers.find({
				store: new mongoose.Types.ObjectId(storeItem?.id),
				isAutoGenerated: true,
			})

			if (automatedOffers.length > 0) {
				for (const offer of automatedOffers) {
					// const updatedStoreOffer = await this.updateStoreOffer(
					// 	storeItem.storeOffer,
					// )

					// Update the title of the automated offer
					// const updatedTitle = `Get ${updatedStoreOffer} from us on top of all other offers from ${storeItem.name}`

					const updatedDescription = `<ul>
  <li>ICB cashback can be redeemed as real cash, 1 ICB cashback = INR 1</li>
  <li>ICB cashback is on top of all other offers available at ${storeItem?.name}</li>
  <li>Check Terms & Conditions for more details. <a href="https://indiancashback.com/terms-and-conditions/" style="color: #7366d9;" target="_blank" rel="noopener noreferrer">Click here</a> </li>
  <li>If you have any doubts you can connect with us
    <a href="https://tawk.to/indiancashback" style="color: #7366d9;" target="_blank" rel="noopener noreferrer">here.</a>
  </li>
</ul>`
					// const updatedTitle = `Grab ${storeItem.storeOffer} from ${storeItem.name}`;
					await this.offers.updateOne(
						{ _id: offer._id },
						{ $set: { hideCbTag: true, description: updatedDescription } }
					)
					updatedOffers.push(offer._id)
				}
			}
		}
	}

	// Function to prepend "ICB" to "cashback" or "reward"
	async updateStoreOffer(storeOffer: string): Promise<string> {
		// Use a regular expression to match "cashback" or "reward" (case-insensitive)
		return storeOffer.replace(/(cashback|reward)/gi, 'ICB $1')
	}

	async logAutomatedOffers() {
		//populate store
		const automatedOffers = await this.offers
			.find({
				isAutoGenerated: true,
			})
			.populate('store')

		for (const _offer of automatedOffers) {
		}
	}
}

// {
//   totalExecutionTime: '158.83 ms',
//   availabilityCheckTime: 'Skipped',
//   mainLogicTime: '158.83 ms',
//   availabilityCheckPercentage: 0
// }

// {
// 	totalExecutionTime: '241.82 ms',
// 		availabilityCheckTime: '0.36 ms',
// 			mainLogicTime: '241.45 ms',
// 				availabilityCheckPercentage: '0.15%'
// }
