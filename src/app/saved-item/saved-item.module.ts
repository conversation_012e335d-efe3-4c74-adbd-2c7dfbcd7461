import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { UserModule } from 'app/user/user.module'
import { SavedItem, SavedItemSchema } from 'shared/entities/saved-item.entity'
import { SavedItemController } from './saved-item.controller'
import { SavedItemService } from './saved-item.service'

@Module({
	imports: [
		UserModule,
		MongooseModule.forFeature([
			{
				name: SavedItem.name,
				schema: SavedItemSchema,
			},
		]),
	],
	controllers: [SavedItemController],
	providers: [SavedItemService],
	exports: [SavedItemService],
})
export class SavedItemModule {}
