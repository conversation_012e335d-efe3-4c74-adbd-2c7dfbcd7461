import { ApiProperty } from '@nestjs/swagger'
import { ContextOfferCouponsType, ContextOfferDealsType } from 'app/context/types/offers.types'
import { PaginationResponseType } from 'shared/dto'
import { GiftCard, Offer, SavedItemDocument, Store } from 'shared/entities'
import { Payment, PaymentType } from 'shared/enums'

export class SaveItemResponse {
	@ApiProperty({ type: String })
	message: string
}

export type SavedItemWithAssociatedOffer = SavedItemDocument & {
	associatedItem: [Offer] // Assuming Offer is the type of associatedItem
}

export type SavedItemWithAssociatedStore = SavedItemDocument & {
	associatedItem: [Store] // Assuming Offer is the type of associatedItem
}

export type SavedItemWithAssociatedGiftCard = SavedItemDocument & {
	associatedItem: [GiftCard] // Assuming Offer is the type of associatedItem
}

export class SavedOfferUidsResponse {
	@ApiProperty({ type: [Number] })
	uids: number[]
}

export class SavedDealsResponse {
	@ApiProperty({ type: [ContextOfferDealsType] })
	deals: ContextOfferDealsType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}

export class SavedCouponsResponse {
	@ApiProperty({ type: [ContextOfferCouponsType] })
	coupons: ContextOfferCouponsType[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}

export class OrderResponse {
	@ApiProperty()
	orderId: string

	@ApiProperty()
	amountToPay: number

	@ApiProperty({
		enum: Payment,
	})
	paymentMethod: PaymentType

	@ApiProperty()
	currency: string

	@ApiProperty()
	fullPaymentDone: boolean
}
