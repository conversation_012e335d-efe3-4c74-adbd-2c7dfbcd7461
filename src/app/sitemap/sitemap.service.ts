import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Store, StoreDocument } from 'shared/entities'
import { formatStoreName } from './helper'

export interface SitemapResponseType {
	stores: { slug: string; updatedAt: string }[]
	offers: { id: string }[]
}

@Injectable()
export class SitemapService {
	constructor(@InjectModel(Store.name) private readonly store: Model<StoreDocument>) {}

	async generateSitemapData(): Promise<SitemapResponseType> {
		const stores = await this.generateSitemapDataForStores()
		const offers = await this.generateSitemapDataForOffers()

		return {
			stores,
			offers,
		}
	}

	async generateSitemapDataForStores(): Promise<{ slug: string; updatedAt: string }[]> {
		const stores = await this.store
			.find({
				active: true,
				missingAccepted: true,
			})
			.select('name updatedAt')
			.exec()

		const formattedStores = stores.map(store => ({
			slug: formatStoreName(store.name),
			updatedAt: store.updatedAt.toISOString(),
		}))

		return formattedStores
	}

	async generateSitemapDataForOffers(): Promise<{ id: string; updatedAt: string }[]> {
		return []
	}
}
