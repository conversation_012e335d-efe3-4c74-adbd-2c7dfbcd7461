import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { Store, StoreSchema } from 'shared/entities'
import { SitemapController } from './sitemap.controller'
import { SitemapService } from './sitemap.service'
@Module({
	controllers: [SitemapController],
	providers: [SitemapService],
	imports: [
		MongooseModule.forFeature([
			{
				name: Store.name,
				schema: StoreSchema,
			},
		]),
	],
})
export class SitemapModule {}
