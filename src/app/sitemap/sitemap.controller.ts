import { Controller, Get } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import { SitemapResponseType, SitemapService } from './sitemap.service'

@Controller('sitemap')
@ApiTags('Sitemap')
export class SitemapController {
	constructor(private readonly sitemapService: SitemapService) {}

	@Get()
	generateSitemap(): Promise<SitemapResponseType> {
		return this.sitemapService.generateSitemapData()
	}
}
