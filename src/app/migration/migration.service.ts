/* cSpell:disable */
// biome-ignore lint/performance/noNamespaceImport: importing for file operations
import * as fs from 'node:fs'
import path from 'node:path'
import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { env } from 'config'
import defu from 'defu'
import destr from 'destr'
import { Model } from 'mongoose'
import { hash } from 'ohash'
import {
	Admin,
	AdminDocument,
	Affiliation,
	AffiliationDocument,
	Category,
	CategoryDocument,
	Store,
	StoreDocument,
	SubCategory,
	SubCategoryDocument,
	User,
	UserDocument,
} from 'shared/entities'
import { Earning, EarningDocument } from 'shared/entities/earning.entity'
import { MissingCashback, MissingCashbackDocument } from 'shared/entities/missing-cashback.entity'
import { PaymentRequest, PaymentRequestDocument } from 'shared/entities/payment-request.entity'
import { UnverifiedUser, UnverifiedUserDocument } from 'shared/entities/unverified-user.entity'
import { MeiliSearchService } from 'shared/modules/meilisearch'
import { CamelCased } from 'shared/types'
import { cloneDatabase } from './db-clone'
import { OldCategory, OldCategoryTitle } from './old-types/category'
import { OldUserEarnings } from './old-types/earnings'
import { OldMissingCashBacks } from './old-types/missing-cashback'
import { OldPartner } from './old-types/partner'
import { OldPaymentRequests } from './old-types/payment'
import { OldReferralCommission } from './old-types/referral-commissions'
import { OldReferrals } from './old-types/referrals'
import { OldStore } from './old-types/store'
import { OldUser } from './old-types/user'
import { ProgressBar } from './utils/progress-bar'
import { convertKeysToCamelCase, UserBalance } from './utils/utils'

@Injectable()
export class MigrationService {
	private meiliSearchService = new MeiliSearchService()
	constructor(
		@InjectModel(Admin.name) private admin: Model<AdminDocument>,
		@InjectModel(User.name) private user: Model<UserDocument>,
		@InjectModel(UnverifiedUser.name)
		private unverifiedUser: Model<UnverifiedUserDocument>,
		@InjectModel(Category.name) private category: Model<CategoryDocument>,
		@InjectModel(SubCategory.name)
		private subCategory: Model<SubCategoryDocument>,
		@InjectModel(Store.name) private store: Model<StoreDocument>,
		@InjectModel(Affiliation.name)
		private affiliation: Model<AffiliationDocument>,
		@InjectModel(Earning.name) private earning: Model<EarningDocument>,
		@InjectModel(MissingCashback.name)
		private missingCashback: Model<MissingCashbackDocument>,
		@InjectModel(PaymentRequest.name)
		private paymentRequest: Model<PaymentRequestDocument>
	) {}

	async rollback() {
		await Promise.all([
			this.user.deleteMany({}),
			this.category.deleteMany({}),
			this.subCategory.deleteMany({}),
			this.affiliation.deleteMany({}),
			this.store.deleteMany({}),
			this.earning.deleteMany({}),
			this.missingCashback.deleteMany({}),
			this.unverifiedUser.deleteMany({}),
			this.admin.deleteMany({}),
		])
	}

	async migrate() {
		// await this.meiliSearchService.deleteAllIndexes()
		// await this.setAdmin()
		// await this.seedAffiliation()
		// await this.migrateUsers()
		// await this.migrateCategories()
		// await this.migrateSubCategories()
		// await this.migrateAffiliations()
		// await this.migrateStores()
		// await this.migrateReferrals()
		// await this.migrateUserEarnings()
		// await this.migrateReferralEarnings()
		// await this.migrateUserBalance()
		// await this.migrateMissingCashback()
		await this.migratePaymentRequest()
	}

	async seedAffiliation() {
		const admin = await this.admin.findOne({ email: '<EMAIL>' }).exec()
		const affiliations: Affiliation = {
			name: 'Old Database',
			apiName: 'Old Database',
			apiKey: 'Old Database',
			accessToken: 'Old Database',
			active: true,
			oldId: 0,
			uid: 0,
			createdBy: admin as AdminDocument,
			updatedBy: admin as AdminDocument,
		}
		await this.affiliation.create(affiliations)
	}

	async setAdmin() {
		await this.admin.deleteMany({}).exec()
		await this.admin.create({
			uid: 1,
			email: '<EMAIL>',
			role: 'super admin',
			block: false,
			name: 'Dennis',
			password: '$2a$10$MYHZRD1vWeMvPpUtTtpfj.3o9pP0OLtbiZYP8scgvBWJt0wSAYon6', // 12345678
		})
	}

	async migrateUsers() {
		const jsonData = destr<OldUser[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/users.json')}`, 'utf8')
		)
		const progressBar = new ProgressBar('User', jsonData.length)
		const bulkOps = []
		// Array to store uids to skip
		const uidsToSkip = [6052] // Replace with actual uids to skip
		const mailToSkip = ['<EMAIL>']
		for (const user of jsonData) {
			const camelCasedUser = convertKeysToCamelCase(user) as CamelCased<OldUser>
			// Skip if uid is in the uidsToSkip array
			if (uidsToSkip.includes(camelCasedUser.uid)) {
				progressBar.update()
				continue
			}
			if (mailToSkip.includes(camelCasedUser.email)) {
				progressBar.update()
				continue
			}
			const processedUser = this.setUndefinedProperties(camelCasedUser)
			processedUser.status = this.setUserStatus(processedUser)
			if (camelCasedUser.isVerified === 0) {
				;(await this.unverifiedUser.create(processedUser)).save()
				progressBar.update()
				continue
			}
			bulkOps.push({
				insertOne: {
					document: defu(new User(), {
						...processedUser,
						oldId: processedUser.uid,
						migrated: true,
					}),
				},
			})

			progressBar.update()
		}

		await this.user.bulkWrite(bulkOps)
		progressBar.complete()
	}

	setUserStatus(user: CamelCased<OldUser>): string {
		switch (user.isVerified) {
			case 1:
				return 'active'
			case 2:
				return 'blocked'
			default:
				return 'inactive'
		}
	}

	setUndefinedProperties(camelCasedUser: CamelCased<OldUser>) {
		if (!camelCasedUser.otpAttempts) {
			camelCasedUser.otpSentTime = undefined
		}
		if (!camelCasedUser.isWithdrawReminded) {
			camelCasedUser.withdrawRemindDate = undefined
		}
		if (camelCasedUser.mobile && Number.isNaN(+camelCasedUser.mobile)) {
			camelCasedUser.mobile = undefined
		}

		// if camelCasedUser.mobileVerified is 0, set mobile to undefined
		if (camelCasedUser.mobileVerified === 0) {
			camelCasedUser.mobile = undefined
		}

		// Handle mobile field
		if (
			camelCasedUser.mobile === null ||
			camelCasedUser.mobile === '' ||
			typeof camelCasedUser.mobile === 'undefined' ||
			Number.isNaN(+camelCasedUser.mobile)
		) {
			camelCasedUser.mobile = undefined
		} else {
			camelCasedUser.mobile = String(+camelCasedUser.mobile) // Convert to string after ensuring it's a valid number
		}

		// Handle invalid date strings
		if (
			typeof camelCasedUser.dateRegistered === 'string' &&
			(camelCasedUser.dateRegistered === '0000-00-00 00:00:00' ||
				Number.isNaN(new Date(camelCasedUser.dateRegistered).getTime()))
		) {
			camelCasedUser.dateRegistered = new Date()
		} else if (
			camelCasedUser.dateRegistered instanceof Date &&
			Number.isNaN(camelCasedUser.dateRegistered.getTime())
		) {
			camelCasedUser.dateRegistered = new Date()
		}

		return camelCasedUser
	}

	async migrateCategories() {
		const categoriesTitleJsonData = destr<OldCategoryTitle[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/category_titles.json')}`, 'utf8')
		)
		const categoryProgressBar = new ProgressBar('Category', categoriesTitleJsonData.length)
		const bulkOps = []

		for (const [mainCategoryIndex, categoryTitle] of categoriesTitleJsonData.entries()) {
			const camelCasedCategoryTitle = convertKeysToCamelCase(
				categoryTitle
			) as CamelCased<OldCategoryTitle>
			bulkOps.push({
				insertOne: {
					document: {
						uid: mainCategoryIndex + 1,
						name: camelCasedCategoryTitle.catTitle,
						image: {
							secureUrl: camelCasedCategoryTitle.catImage,
						},
						active: true,
						oldId: camelCasedCategoryTitle.catTitleId,
					},
				},
			})
			categoryProgressBar.update(camelCasedCategoryTitle.catTitle)
		}

		await this.category.bulkWrite(bulkOps)
		categoryProgressBar.complete()
	}

	async migrateSubCategories() {
		const subCategoriesJsonData = destr<OldCategory[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/categories.json')}`, 'utf8')
		)
		const categoryProgressBar = new ProgressBar('SubCategory', subCategoriesJsonData.length)
		const bulkOps = []

		for (const [mainCategoryIndex, categoryTitle] of subCategoriesJsonData.entries()) {
			const camelCasedSubCategory = convertKeysToCamelCase(categoryTitle) as CamelCased<OldCategory>
			const category = await this.category
				.findOne({ oldId: camelCasedSubCategory.categoryTitleId })
				.exec()

			bulkOps.push({
				insertOne: {
					document: {
						uid: mainCategoryIndex + 1,
						name: camelCasedSubCategory.categoryName,
						image: {
							secureUrl: camelCasedSubCategory.categImg,
						},
						description: camelCasedSubCategory.categDesc,
						active: true,
						category,
						oldId: camelCasedSubCategory.categoryId,
					},
				},
			})

			categoryProgressBar.update(camelCasedSubCategory.categoryName)
		}

		await this.subCategory.bulkWrite(bulkOps)
		categoryProgressBar.complete()
	}

	async migrateAffiliations() {
		const jsonData = destr<OldPartner[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/partners.json')}`, 'utf8')
		)
		const affiliationBar = new ProgressBar('Affiliation', jsonData.length)
		const bulkOps = []

		for (const [affiliationIndex, oldAffiliation] of jsonData.entries()) {
			const camelCasedAffiliation = convertKeysToCamelCase(oldAffiliation) as CamelCased<OldPartner>

			bulkOps.push({
				insertOne: {
					document: {
						uid: affiliationIndex + 1,
						name: camelCasedAffiliation.partnerName,
						apiName: camelCasedAffiliation?.partnerApiName,
						apiKey: camelCasedAffiliation?.partnerApiKey,
						active: true,
						oldId: camelCasedAffiliation.partnerId,
					},
				},
			})

			affiliationBar.update(camelCasedAffiliation.partnerName)
		}

		await this.affiliation.bulkWrite(bulkOps)
		affiliationBar.complete()
	}

	async migrateStores() {
		const jsonData = destr<OldStore[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/stores.json')}`, 'utf8')
		)
		const storeProgressBar = new ProgressBar('Store', jsonData.length * 2)
		const affiliations = await this.affiliation.find().exec()
		const bulkOps = []

		for (const [storeIndex, oldStore] of jsonData.entries()) {
			const camelCasedStores = convertKeysToCamelCase(oldStore) as CamelCased<OldStore>

			bulkOps.push({
				insertOne: {
					document: {
						uid: storeIndex + 1,
						name: camelCasedStores.storeName,
						logo: {
							secureUrl: camelCasedStores.storeLogo,
							publicId: '',
						},
						banner: {
							secureUrl: camelCasedStores.storeBanner,
							publicId: '',
						},
						autoCheck: camelCasedStores.autoCheck === 1,
						noDesktopWebSaleCategory: camelCasedStores.noDesktopWebSaleCateg === 1,
						noMobileWebSaleCategory: camelCasedStores.noMobileWebSaleCateg === 1,
						noAppSaleCategory: camelCasedStores.noAppSaleCateg === 1,
						storeWarning: camelCasedStores.storeWarning,
						reliability: camelCasedStores.reliability,
						isSpecial: camelCasedStores.isSpecial === 1 ? 'special' : 'none',
						warningType: 'all',
						trackable: camelCasedStores.isTrackable === 1,
						isInstant: camelCasedStores.isInstant === 1,
						offerWarning: camelCasedStores.storeOfferWarning,
						description: camelCasedStores.storeDesc,
						affiliateLink: camelCasedStores.affiliateLink,
						campaignType: camelCasedStores.campaignType,
						active: camelCasedStores.priority >= 0,
						oldId: camelCasedStores.storeId,
						relatedStores: [],
						priority: camelCasedStores.priority,
						maximumAmount: camelCasedStores.maximumAmount,
						minimumAmount: camelCasedStores.minimumAmount,
						storeTerms: camelCasedStores.storeTerms,
						storeOffer: camelCasedStores.storeOffer,
						confirmationTime: camelCasedStores.confirmationTime,
						trackingTime: camelCasedStores.trackingTime,
						missingAccepted: camelCasedStores.missingAccepted === 'Yes',
						storeHowToGet: camelCasedStores.storeHowtoget,
						detailedDescription: camelCasedStores.detailedDesc,
						affiliation: affiliations.find(
							affiliation => affiliation.oldId === camelCasedStores.affiliation
						),
						deepLinkEnable: camelCasedStores.deeplinkEnable === 1,
					},
				},
			})

			storeProgressBar.update(camelCasedStores.storeName)
		}

		await this.store.bulkWrite(bulkOps)

		for (const oldStore of jsonData) {
			const camelCasedStores = convertKeysToCamelCase(oldStore) as CamelCased<OldStore>

			const store = (await this.store
				.findOne({ oldId: camelCasedStores.storeId })
				.exec()) as StoreDocument

			const storeIds = camelCasedStores.relatedStores.split(',')
			const relatedStores = await this.store
				.find({ oldId: { $in: storeIds.map(id => +id) } })
				.exec()

			store.relatedStores = relatedStores
			await store.save()
			storeProgressBar.update(store.name)
		}

		storeProgressBar.complete()
	}

	async migrateReferrals() {
		const jsonData = destr<OldReferrals[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/referrals.json')}`, 'utf8')
		)
		const progressBar = new ProgressBar('User Referrals', jsonData.length)

		for (const referral of jsonData) {
			const camelCasedReferral = convertKeysToCamelCase(referral) as CamelCased<OldReferrals>

			try {
				const user = (await this.user
					.findOne({ oldId: camelCasedReferral.uid })
					.exec()) as UserDocument
				const referralUser = (await this.user
					.findOne({ oldId: camelCasedReferral.referral })
					.exec()) as UserDocument

				if (referralUser) {
					referralUser.referral = user
					await referralUser.save()
				}
			} catch (_error) {}

			progressBar.update()
		}

		progressBar.complete()
	}

	async migrateUserEarnings() {
		const admin = await this.admin.findOne({ email: '<EMAIL>' }).exec()
		const jsonData = destr<OldUserEarnings[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/user_earnings.json')}`, 'utf8')
		)
		const progressBar = new ProgressBar('User Earnings', jsonData.length)

		for (const [earningIndex, user] of jsonData.entries()) {
			const camelCasedEarnings = convertKeysToCamelCase(user) as CamelCased<OldUserEarnings>

			const userEntity = await this.user.findOne({ oldId: camelCasedEarnings.uid }).exec()
			if (!userEntity) {
				continue
			}
			const affiliationEntity = await this.affiliation
				.findOne({ oldId: camelCasedEarnings.partnerId })
				.exec()
			const storeEntity = await this.store.findOne({ oldId: camelCasedEarnings.storeId }).exec()

			const status = this.getEarningStatus(camelCasedEarnings)

			const earning = new this.earning({
				user: userEntity,
				uid: earningIndex + 1,
				referenceId: `CBERN${hash({
					uid: camelCasedEarnings.earningId,
				}).toUpperCase()}`,
				oldId: camelCasedEarnings.earningId,
				affiliation: affiliationEntity,
				store: storeEntity,
				cashbackAmount: Number(camelCasedEarnings.amount.toFixed(2)),
				amountGot: Number(camelCasedEarnings.amountGot.toFixed(2)),
				amountPromised: Number.isNaN(+camelCasedEarnings.amountPromised)
					? 0
					: Number(Number(camelCasedEarnings.amountPromised).toFixed(2)),
				saleAmount: Number(camelCasedEarnings.saleAmount.toFixed(2)),
				orderCount: camelCasedEarnings.orderCount,
				notes: camelCasedEarnings.notes,
				remarks: camelCasedEarnings.remarks,
				dateConfirmedCancelled:
					camelCasedEarnings.dateConfirmedCancelled === '0000-00-00 00:00:00'
						? new Date()
						: camelCasedEarnings.dateConfirmedCancelled,
				status,
				createdAt: camelCasedEarnings.addedDate,
				confirmDate: camelCasedEarnings.confirmDate,
				createdBy: admin,
				updatedBy: admin,
				advertiserInfo: camelCasedEarnings.advertiserInfo,
				autoUpdated: camelCasedEarnings.autoUpdated === 1,
				migrated: true,
			})

			// Save the earning document
			await earning.save()

			progressBar.update()
		}

		progressBar.complete()
	}

	getEarningStatus(camelCasedEarnings: CamelCased<OldUserEarnings>): string {
		switch (camelCasedEarnings.status) {
			case 'Processing':
				return camelCasedEarnings.checkedToCancel === 1
					? 'tracked_for_cancel'
					: 'tracked_for_confirm'
			case 'Confirmed':
				return 'confirmed'
			case 'Cancelled':
				return 'cancelled'
			default:
				return 'pending'
		}
	}

	async migrateReferralEarnings() {
		const jsonData = destr<OldReferralCommission[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/referral_commissions.json')}`, 'utf8')
		)
		const progressBar = new ProgressBar('User Commission', jsonData.length)

		for (const referral of jsonData) {
			const camelCasedCommission = convertKeysToCamelCase(
				referral
			) as CamelCased<OldReferralCommission>

			const user = await this.user.findOne({ oldId: camelCasedCommission.uid }).exec()
			const earning = await this.earning.findOne({ oldId: camelCasedCommission.earningId }).exec()

			if (user && earning) {
				const commissionAmount = Number(camelCasedCommission.amount.toFixed(2))
				earning.referralCommission = commissionAmount
				// await earning.save()
			}
			progressBar.update()
		}
		progressBar.complete()
	}

	async migrateUserBalance() {
		for (let file = 1; file < 10; file++) {
			const jsonData = destr<UserBalance[]>(
				fs.readFileSync(
					`${path.join(process.cwd(), `json-dump/user_balances/${file}.json`)}`,
					'utf8'
				)
			)
			const progressBar = new ProgressBar(`User Balance ${file}`, jsonData.length)
			for (const balance of jsonData) {
				const user = await this.user.findOne({ oldId: +balance.uid }).exec()
				if (user) {
					user.balance = balance.withdrawable ? +balance.withdrawable.toFixed(2) : 0
					user.pendingBalance = balance.totalPending ? +(+balance.totalPending).toFixed(2) : 0
					user.totalEarned = balance.totalEarned ? +(+balance.totalEarned).toFixed(2) : 0
					user.countConfirmed = +(balance.countConfirmed || 0)
					user.countPending = +(balance.countPending || 0)
					user.paidAmount = +(balance.PaidAmount || 0)
					user.flipkartConfirmedAmount = +(balance.FlipConfirmedAmount || 0)
					user.countFlipkartConfirmed = +(balance.countFlipConfirmed || 0)
					user.flipkartPendingAmount = +(balance.FlipPendingAmount || 0)
					user.countFlipkartPending = +(balance.countFlipPending || 0)
					await user.save()
				}
				progressBar.update()
			}
			progressBar.complete()
		}
	}

	async migrateMissingCashback() {
		const jsonData = destr<OldMissingCashBacks[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/missing_cashbacks.json')}`, 'utf8')
		)

		const admin = await this.admin.findOne({ email: '<EMAIL>' }).exec()
		const progressBar = new ProgressBar('Missing Cashback', jsonData.length)

		for (const [index, missingCashbackData] of jsonData.entries()) {
			const camelCasedData = convertKeysToCamelCase(
				missingCashbackData
			) as CamelCased<OldMissingCashBacks>

			const user = await this.user.findOne({ oldId: +camelCasedData.uid }).exec()
			if (!user) {
				continue
			}
			const store = await this.store.findOne({ oldId: +camelCasedData.storeId }).exec()

			let affiliation = await this.affiliation.findOne({ oldId: +camelCasedData.affiliate }).exec()
			if (!affiliation) {
				affiliation = await this.affiliation.findOne({ name: 'Old Database' }).exec()
			}

			const platform =
				camelCasedData.platform === 'Mobile Site'
					? 'mobile'
					: camelCasedData.platform === 'Desktop site'
						? 'web'
						: 'app'

			// Determine user type
			const userType = camelCasedData.userType === '1' ? 'new' : 'old'

			// Determine status
			const status =
				camelCasedData.isSolved === '0'
					? 'not-solved'
					: camelCasedData.isSolved === '1'
						? 'solved'
						: camelCasedData.isSolved === '2'
							? 'rejected'
							: camelCasedData.isSolved === '3'
								? 'forwarded'
								: 'not-solved'

			const processedData = this.setUndefinedProperties2(camelCasedData)
			const missingCashback = new this.missingCashback({
				complaintId:
					camelCasedData.feedbackId ||
					`CBCOM${hash({
						uid: index + 1,
						user: user.email,
						orderId: processedData.orderID,
					}).toUpperCase()}`,
				user,
				store,
				storeName: processedData.storeName,
				affiliation,
				coupon: processedData.couponcode || undefined,
				orderId: processedData.orderID || undefined,
				message: processedData.message,
				paidAmount: Number.parseFloat(processedData.amount) || 0,
				userType,
				platform,
				orderDate: new Date(processedData.orderDate) || new Date(),
				status,
				migrated: true,
				oldId: Number.parseInt(processedData.feedbackId, 10) || 0,
				createdAt: new Date(processedData.addedDate) || new Date(),
				createdBy: admin,
			})

			await missingCashback.save()
			// wait 1 second
			await new Promise(resolve => setTimeout(resolve, 100))

			progressBar.update()
		}

		progressBar.complete()
	}

	async migratePaymentRequest() {
		const jsonData = destr<OldPaymentRequests[]>(
			fs.readFileSync(`${path.join(process.cwd(), 'json-dump/payment_requests.json')}`, 'utf8')
		)
		const admin = await this.admin.findOne({ email: '<EMAIL>' }).exec()
		const progressBar = new ProgressBar('Payment Requests', jsonData.length)

		for (const [index, paymentRequestData] of jsonData.entries()) {
			const camelCasedData = convertKeysToCamelCase(
				paymentRequestData
			) as CamelCased<OldPaymentRequests>
			const user = await this.user.findOne({ oldId: camelCasedData.uid }).exec()
			if (!user) {
				continue
			}

			const paymentType = this.mapMethodToPaymentType(camelCasedData.method)
			if (!paymentType) {
				continue
			}

			const status = 'approved'

			const details = this.getPaymentDetails(paymentType, camelCasedData)

			const paymentRequest = new this.paymentRequest({
				uid: index + 1,
				oldId: camelCasedData.paymentId,
				ipAddress: camelCasedData.userIp,
				referenceId: `PYMT${hash({
					uid: index + 1,
					user: user.toString(),
					time: Date.now(),
				}).toUpperCase()}`,
				withdrawer: user,
				withdrawAmount: Number(camelCasedData.amountPayable.toFixed(2)),
				paymentType,
				status,
				notes: camelCasedData.notes,
				requestedDate: camelCasedData.dateAdded,
				createdAt:
					camelCasedData.statusChangeDate === '0000-00-00 00:00:00'
						? camelCasedData.dateAdded
						: camelCasedData.statusChangeDate,
				updatedAt:
					camelCasedData.statusChangeDate === '0000-00-00 00:00:00'
						? camelCasedData.dateAdded
						: camelCasedData.statusChangeDate,
				updatedBy: admin,
				...details,
				mobileNumber: camelCasedData.mobileNum,
				migrated: true,
			})

			await paymentRequest.save()

			progressBar.update()
		}
		progressBar.complete()
	}

	mapMethodToPaymentType(method: string): string {
		switch (method) {
			case 'NEFT':
				return 'bank'
			case 'Recharge':
				return 'recharge'
			case 'GIFT':
				return 'giftVoucher'
			case 'WALLET':
				return 'wallet'
			default:
				return ''
		}
	}

	mapProgressStatusToStatus(progressStatus: number): string {
		switch (progressStatus) {
			case 0:
				return 'pending'
			case 1:
				return 'approved'
			case 2:
				return 'rejected'
			default:
				return 'pending'
		}
	}

	getPaymentDetails(paymentType: string, data: CamelCased<OldPaymentRequests>): any {
		switch (paymentType) {
			case 'bank':
				return {
					bankDetails: {
						accountNumber: data.accountNumber,
						IFSC: data.IFSC,
						bankName: data.bankName,
						branchName: data.branchName,
						accountHolderName: data.holderName,
					},
				}
			case 'recharge':
				return {
					rechargeDetails: {
						serviceProvider: data.serviceProvider,
						connectionType: data.connectionType,
						phoneNumber: data.mobileNum,
					},
				}
			case 'giftVoucher':
				return {
					giftCardDetails: {
						gvStore: data.gvStore,
					},
				}
			case 'wallet':
				return {
					mobileNumber: data.mobileNum,
				}
			default:
				return {}
		}
	}

	setUndefinedProperties2(camelCasedData: CamelCased<OldMissingCashBacks>) {
		if (
			typeof camelCasedData.orderDate === 'string' &&
			(camelCasedData.orderDate === '0000-00-00 00:00:00' ||
				Number.isNaN(new Date(camelCasedData.orderDate).getTime()))
		) {
			camelCasedData.orderDate = new Date()
		} else if (
			camelCasedData.orderDate instanceof Date &&
			Number.isNaN(camelCasedData.orderDate.getTime())
		) {
			camelCasedData.orderDate = new Date()
		}

		if (
			typeof camelCasedData.addedDate === 'string' &&
			(camelCasedData.addedDate === '0000-00-00 00:00:00' ||
				Number.isNaN(new Date(camelCasedData.addedDate).getTime()))
		) {
			camelCasedData.addedDate = new Date()
		} else if (
			camelCasedData.addedDate instanceof Date &&
			Number.isNaN(camelCasedData.addedDate.getTime())
		) {
			camelCasedData.addedDate = new Date()
		}

		return camelCasedData
	}

	async cloneDatabase() {
		await cloneDatabase(
			env.MIGRATION.sourceUrl,
			env.MIGRATION.targetUrl,
			env.MIGRATION.sourceDatabase,
			env.MIGRATION.targetDatabase
		)
	}
}
