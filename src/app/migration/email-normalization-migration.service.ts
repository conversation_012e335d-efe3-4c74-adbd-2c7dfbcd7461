import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { User, UserDocument } from 'shared/entities/user.entity'
import { EmailNormalizationService } from 'shared/services/email-normalization.service'

@Injectable()
export class EmailNormalizationMigrationService {
	private readonly logger = new Logger(EmailNormalizationMigrationService.name)

	constructor(
		@InjectModel(User.name)
		private userModel: Model<UserDocument>,
		private emailNormalizationService: EmailNormalizationService
	) {}

	/**
	 * Migrate all existing users to add normalized email field
	 */
	async migrateAllUsers(): Promise<{
		success: boolean
		totalUsers: number
		migratedUsers: number
		conflictUsers: number
		errors: string[]
	}> {
		this.logger.log('Starting email normalization migration...')

		const errors: string[] = []
		let migratedUsers = 0
		let conflictUsers = 0

		try {
			// Get all users without normalizedEmail field
			const users = await this.userModel
				.find({
					$or: [
						{ normalizedEmail: { $exists: false } },
						{ normalizedEmail: null },
						{ normalizedEmail: '' },
					],
				})
				.exec()

			const totalUsers = users.length
			this.logger.log(`Found ${totalUsers} users to migrate`)

			// Process users in batches to avoid memory issues
			const batchSize = 100
			for (let i = 0; i < users.length; i += batchSize) {
				const batch = users.slice(i, i + batchSize)
				await this.processBatch(batch, errors)

				// Count successful migrations in this batch
				for (const user of batch) {
					const normalizedEmail = this.emailNormalizationService.normalizeEmail(user.email)

					// Check for conflicts with other users
					const conflictingUser = await this.userModel
						.findOne({
							_id: { $ne: user._id },
							normalizedEmail,
						})
						.exec()

					if (conflictingUser) {
						conflictUsers++
						errors.push(
							`Conflict for user ${user._id} (${user.email}): conflicts with ${conflictingUser._id} (${conflictingUser.email})`
						)
					} else {
						migratedUsers++
					}
				}

				this.logger.log(
					`Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(users.length / batchSize)}`
				)
			}

			this.logger.log(
				`Migration completed: ${migratedUsers}/${totalUsers} users migrated successfully`
			)

			return {
				success: true,
				totalUsers,
				migratedUsers,
				conflictUsers,
				errors,
			}
		} catch (error) {
			this.logger.error('Migration failed:', error)
			return {
				success: false,
				totalUsers: 0,
				migratedUsers: 0,
				conflictUsers: 0,
				errors: [error.message],
			}
		}
	}

	/**
	 * Process a batch of users for migration
	 */
	private async processBatch(users: UserDocument[], errors: string[]): Promise<void> {
		const bulkOps = []

		for (const user of users) {
			try {
				const normalizedEmail = this.emailNormalizationService.normalizeEmail(user.email)

				// Check for conflicts with other users (excluding current user)
				const conflictingUser = await this.userModel
					.findOne({
						_id: { $ne: user._id },
						normalizedEmail,
					})
					.exec()

				if (conflictingUser) {
					errors.push(
						`Skipping user ${user._id} (${user.email}): conflicts with ${conflictingUser._id} (${conflictingUser.email})`
					)
				} else {
					bulkOps.push({
						updateOne: {
							filter: { _id: user._id },
							update: { $set: { normalizedEmail } },
						},
					})
				}
			} catch (error) {
				errors.push(`Error processing user ${user._id}: ${error.message}`)
			}
		}

		if (bulkOps.length > 0) {
			await this.userModel.bulkWrite(bulkOps)
		}
	}

	/**
	 * Rollback migration by removing normalizedEmail field
	 */
	async rollbackMigration(): Promise<{
		success: boolean
		affectedUsers: number
	}> {
		this.logger.log('Rolling back email normalization migration...')

		try {
			const result = await this.userModel.updateMany(
				{ normalizedEmail: { $exists: true } },
				{ $unset: { normalizedEmail: 1 } }
			)

			this.logger.log(`Rollback completed: ${result.modifiedCount} users affected`)

			return {
				success: true,
				affectedUsers: result.modifiedCount,
			}
		} catch (error) {
			this.logger.error('Rollback failed:', error)
			return {
				success: false,
				affectedUsers: 0,
			}
		}
	}

	/**
	 * Check for potential email conflicts
	 */
	private async checkForConflicts(): Promise<number> {
		const users = await this.userModel.find({}, { email: 1 }).exec()
		const normalizedEmails = new Set<string>()
		let conflicts = 0

		for (const user of users) {
			const normalized = this.emailNormalizationService.normalizeEmail(user.email)
			if (normalizedEmails.has(normalized)) {
				conflicts++
			} else {
				normalizedEmails.add(normalized)
			}
		}

		return conflicts
	}

	/**
	 * Get detailed conflict report
	 */
	async getConflictReport(): Promise<
		Array<{
			normalizedEmail: string
			conflictingUsers: Array<{ id: string; email: string }>
		}>
	> {
		const users = await this.userModel.find({}, { email: 1 }).exec()
		const emailGroups = new Map<string, Array<{ id: string; email: string }>>()

		// Group users by normalized email
		for (const user of users) {
			const normalized = this.emailNormalizationService.normalizeEmail(user.email)
			if (!emailGroups.has(normalized)) {
				emailGroups.set(normalized, [])
			}
			emailGroups.get(normalized)?.push({
				id: user._id.toString(),
				email: user.email,
			})
		}

		// Return only groups with conflicts (more than 1 user)
		const conflicts = []
		for (const [normalizedEmail, users] of emailGroups) {
			if (users.length > 1) {
				conflicts.push({
					normalizedEmail,
					conflictingUsers: users,
				})
			}
		}

		return conflicts
	}
}
