export type OldUserEarnings = {
	readonly earningId: number
	readonly uid: number
	readonly storeId: number
	readonly amount: number
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly amount_got: number
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly amount_promised: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly sale_amount: number
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly order_count: number
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly partner_id: number
	readonly notes: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly advertiser_info: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly other_info: string
	readonly remarks: string
	readonly status: Status
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly order_unique_id: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly checked_to_cancel: number
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly checked_to_confirm: number
	readonly addedDate: Date
	readonly confirmDate: Date
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly date_Confirmed_cancelled: Date | string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly auto_updated: number
}

export type Status = 'Confirmed' | 'Cancelled' | 'Processing' | 'Pending'
