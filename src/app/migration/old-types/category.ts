export type OldCategory = {
	categoryId: number
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	category_title_id: number
	categoryName: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	categ_desc: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	categ_img: string
}

export type OldCategoryTitle = {
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	cat_title_id: number
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	cat_title: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	cat_image: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	cat_image_name: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	categ_stores: string
	addedDate: Date
}
