export type OldPaymentRequests = {
	readonly paymentId: number
	readonly uid: number
	readonly userIp: string
	readonly method: Method
	readonly amountPayable: number
	readonly gvStore: string
	readonly status: 'Paid'
	readonly mobileNum: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly connection_type: string
	readonly serviceProvider: string
	readonly dateAdded: Date
	readonly holderName: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly addressNEFT: string
	readonly bankName: string
	readonly branchName: string
	readonly accountNumber: string
	readonly IFSC: string
	readonly fullName: string
	readonly address: string
	readonly city: string
	readonly state: number
	readonly pincode: string
	readonly phone: string
	// biome-ignore lint/style/useNamingConvention: Legacy database field name
	readonly progress_status: number
	readonly notes: string
	readonly statusChangeDate: StatusChangeDateUnion
}

export type Method = 'NEFT' | 'Recharge' | 'GIFT' | 'WALLET'

export type StatusChangeDateUnion = Date | '0000-00-00 00:00:00'
