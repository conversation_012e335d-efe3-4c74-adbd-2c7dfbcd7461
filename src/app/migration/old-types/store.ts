// biome-ignore lint/style/useNamingConvention: Legacy database schema - maintaining original field names
export type OldStore = {
	storeId: number
	storeName: string
	storeOfferWarning: string
	storeLogo: string
	storeBanner: string
	categories: string
	affiliateLink: string
	affiliation: number
	campaign_type: string
	storeOffer: string
	related_stores: string
	minimum_amount: number
	maximum_amount: number
	store_desc: string
	detailed_desc: string
	reliability: number
	isTrackable: number
	priority: number
	auto_check: number
	home_offer: number
	isSpecial: number
	store_warning: string
	store_top_warning: string
	top_warning_link: string
	top_warning_show_inactive: number
	warning_type: number
	store_howtoget: string
	store_terms: string
	no_app_sale_categ: number
	no_mobile_web_sale_categ: number
	no_desktop_web_sale_categ: number
	addedDate: string
	added_by: string
	giftcard_id: number
	instant_store_id: number
	is_instant: number
	offer_id: number
	deeplink_enable: number
	tracking_time: string
	confirmation_time: string
	missing_accepted: string
}
