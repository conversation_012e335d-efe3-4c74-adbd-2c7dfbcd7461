import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import {
	Admin,
	AdminSchema,
	Affiliation,
	AffiliationSchema,
	Category,
	CategorySchema,
	Store,
	StoreSchema,
	SubCategory,
	SubCategorySchema,
} from 'shared/entities'
import { Earning, EarningSchema } from 'shared/entities/earning.entity'
import { MissingCashback, MissingCashbackSchema } from 'shared/entities/missing-cashback.entity'
import { PaymentRequest, PaymentRequestSchema } from 'shared/entities/payment-request.entity'
import { UnverifiedUser, UnverifiedUserSchema } from 'shared/entities/unverified-user.entity'
import { User, UserSchema } from 'shared/entities/user.entity'
import { SharedServicesModule } from 'shared/services/shared-services.module'
import { EmailNormalizationMigrationController } from './email-normalization-migration.controller'
import { EmailNormalizationMigrationService } from './email-normalization-migration.service'
import { MigrationController } from './migration.controller'
import { MigrationService } from './migration.service'

@Module({
	imports: [
		SharedServicesModule,
		MongooseModule.forFeature([
			{
				name: Admin.name,
				schema: AdminSchema,
			},
			{
				name: User.name,
				schema: UserSchema,
			},
			{
				name: UnverifiedUser.name,
				schema: UnverifiedUserSchema,
			},
			{
				name: Category.name,
				schema: CategorySchema,
			},
			{
				name: SubCategory.name,
				schema: SubCategorySchema,
			},
			{
				name: Store.name,
				schema: StoreSchema,
			},
			{
				name: Affiliation.name,
				schema: AffiliationSchema,
			},
			{
				name: Earning.name,
				schema: EarningSchema,
			},
			{
				name: MissingCashback.name,
				schema: MissingCashbackSchema,
			},
			{
				name: PaymentRequest.name,
				schema: PaymentRequestSchema,
			},
		]),
	],
	controllers: [MigrationController, EmailNormalizationMigrationController],
	providers: [MigrationService, EmailNormalizationMigrationService],
})
export class MigrationModule {}
