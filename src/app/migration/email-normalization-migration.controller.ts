import { <PERSON>, Get, Post } from '@nestjs/common'
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger'
import { EmailNormalizationMigrationService } from './email-normalization-migration.service'

@ApiTags('Migration')
@Controller('migration/email-normalization')
export class EmailNormalizationMigrationController {
	constructor(private emailNormalizationMigrationService: EmailNormalizationMigrationService) {}

	@Post('migrate')
	@ApiOperation({ summary: 'Migrate all users to add normalized email field' })
	@ApiResponse({ status: 200, description: 'Migration completed' })
	async migrateUsers() {
		return this.emailNormalizationMigrationService.migrateAllUsers()
	}

	@Post('rollback')
	@ApiOperation({ summary: 'Rollback email normalization migration' })
	@ApiResponse({ status: 200, description: 'Rollback completed' })
	async rollbackMigration() {
		return this.emailNormalizationMigrationService.rollbackMigration()
	}

	@Get('conflicts')
	@ApiOperation({ summary: 'Get detailed conflict report' })
	@ApiResponse({ status: 200, description: 'Conflict report' })
	async getConflictReport() {
		return this.emailNormalizationMigrationService.getConflictReport()
	}
}
