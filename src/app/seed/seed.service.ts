/* cSpell:disable */
import { faker } from '@faker-js/faker'
import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { ProgressBar } from 'app/migration/utils/progress-bar'
import { Model, Types } from 'mongoose'
import { hash } from 'ohash'
import {
	Admin,
	AdminDocument,
	Affiliation,
	AffiliationDocument,
	Banner,
	BannerDocument,
	Category,
	CategoryDocument,
	Click,
	ClickDocument,
	GiftCard,
	GiftCardDocument,
	MobileStory,
	MobileStoryDocument,
	Offer,
	OfferDocument,
	PersonalInterest,
	PersonalInterestDocument,
	QuickAccess,
	QuickAccessDocument,
	Store,
	StoreCategory,
	StoreCategoryDocument,
	StoreDocument,
	SubCategory,
	SubCategoryDocument,
	TermsAndPrivacy,
	TermsAndPrivacyDocument,
	User,
	UserDocument,
} from 'shared/entities'
import { Earning, EarningDocument } from 'shared/entities/earning.entity'
import { HeroAccess, HeroAccessDocument } from 'shared/entities/hero-access.entity'
import { IcbGiftCard, IcbGiftCardDocument } from 'shared/entities/icb-gift-card'
import { MissingCashback, MissingCashbackDocument } from 'shared/entities/missing-cashback.entity'
import {
	OngoingSaleOffers,
	OngoingSaleOffersDocument,
} from 'shared/entities/ongoing-sale-offers.entity'
import { Review, ReviewDocument } from 'shared/entities/review.entity'
import { Testimonial, TestimonialDocument } from 'shared/entities/testimonial.entity'
import { MeiliSearchService } from 'shared/modules/meilisearch'
import { GiftCardSchema, OfferSchema, StoreSchema } from 'shared/modules/meilisearch/schema'

@Injectable()
export class SeedService {
	constructor(
		@InjectModel(Admin.name)
		private admin: Model<AdminDocument>,
		@InjectModel(Affiliation.name)
		private affiliation: Model<AffiliationDocument>,
		@InjectModel(Category.name)
		private category: Model<CategoryDocument>,
		@InjectModel(SubCategory.name)
		private subCategory: Model<SubCategoryDocument>,
		@InjectModel(Store.name)
		private store: Model<StoreDocument>,
		@InjectModel(StoreCategory.name)
		private storeCategory: Model<StoreCategoryDocument>,
		@InjectModel(Offer.name)
		private offer: Model<OfferDocument>,
		@InjectModel(Banner.name)
		private banner: Model<BannerDocument>,
		@InjectModel(MobileStory.name)
		private mobileStory: Model<MobileStoryDocument>,
		@InjectModel(QuickAccess.name)
		private quickAccess: Model<QuickAccessDocument>,
		@InjectModel(HeroAccess.name)
		private heroAccess: Model<HeroAccessDocument>,
		@InjectModel(OngoingSaleOffers.name)
		private ongoingSales: Model<OngoingSaleOffersDocument>,
		@InjectModel(GiftCard.name)
		private giftCard: Model<GiftCardDocument>,
		@InjectModel(IcbGiftCard.name)
		private icbGiftCard: Model<IcbGiftCardDocument>,
		@InjectModel(User.name)
		private user: Model<UserDocument>,
		@InjectModel(Click.name)
		private click: Model<ClickDocument>,
		@InjectModel(Review.name)
		private review: Model<ReviewDocument>,
		@InjectModel(Earning.name)
		private earning: Model<EarningDocument>,
		@InjectModel(Testimonial.name)
		private testimonial: Model<TestimonialDocument>,
		@InjectModel(PersonalInterest.name)
		private personalInterest: Model<PersonalInterestDocument>,
		@InjectModel(TermsAndPrivacy.name)
		private termsAndPrivacy: Model<TermsAndPrivacyDocument>,
		@InjectModel(MissingCashback.name)
		private missingCashback: Model<MissingCashbackDocument>
	) {}

	async seed() {
		const totalItems = 33 // Total number of seeding operations
		const progressBar = new ProgressBar('Seeding', totalItems)
		await this.setAdmin()
		await this.affiliation.deleteMany({}).exec()
		progressBar.update('Affiliation deleted')
		await this.seedAffiliation()
		progressBar.update('Affiliation seeded')

		await this.category.deleteMany({}).exec()
		progressBar.update('Category deleted')
		await this.seedCategory()
		progressBar.update('Category seeded')

		await this.subCategory.deleteMany({}).exec()
		progressBar.update('SubCategory deleted')
		await this.seedSubCategory()
		progressBar.update('SubCategory seeded')

		await this.store.deleteMany({}).exec()
		progressBar.update('Store deleted')
		await this.seedStore()
		progressBar.update('Store seeded')

		await this.addCategoriesToStores()
		progressBar.update('Categories added to stores')

		await this.addRelatedStoresToStores()
		progressBar.update('Related stores added to stores')

		await this.storeCategory.deleteMany({}).exec()
		progressBar.update('StoreCategory deleted')
		await this.seedStoreCategory()
		progressBar.update('StoreCategory seeded')

		await this.offer.deleteMany({}).exec()
		progressBar.update('Offer deleted')
		await this.seedOffers()
		progressBar.update('Offers seeded')

		await this.banner.deleteMany({}).exec()
		progressBar.update('Banner deleted')
		await this.seedBanners()
		progressBar.update('Banners seeded')

		await this.mobileStory.deleteMany({}).exec()
		progressBar.update('MobileStory deleted')
		await this.seedMobileStory()
		progressBar.update('MobileStory seeded')

		await this.quickAccess.deleteMany({}).exec()
		progressBar.update('QuickAccess deleted')
		await this.seedQuickAccess()
		progressBar.update('QuickAccess seeded')

		await this.heroAccess.deleteMany({}).exec()
		progressBar.update('HeroAccess deleted')
		await this.seedHeroAccess()
		progressBar.update('HeroAccess seeded')

		await this.ongoingSales.deleteMany({}).exec()
		progressBar.update('OngoingSales deleted')
		await this.seedOngoingSales()

		await this.giftCard.deleteMany({}).exec()
		progressBar.update('GiftCards deleted')
		await this.seedGiftCard()
		progressBar.update('GiftCards seeded')
		await this.seedRelatedGiftCards()
		progressBar.update('Related GiftCards seeded')

		await this.addCategoriesToGiftCard()
		progressBar.update('Categories added to GiftCards')

		await this.user.deleteMany({}).exec()
		await this.seedUsers()
		progressBar.update('Users seeded')

		await this.click.deleteMany({}).exec()
		progressBar.update('Clicks deleted')
		await this.seedClicks()
		progressBar.update('Clicks seeded')

		await this.earning.deleteMany({}).exec()
		progressBar.update('Earning deleted')
		await this.seedEarning()
		progressBar.update('Earning seeded')

		await this.seedReviews()
		progressBar.update('Reviews seeded')

		await this.seedTestimonials()
		progressBar.update('Testimonials seeded')

		await this.seedPrivacyAndTerms()
		progressBar.update('Terms And Privacy seeded')

		await this.seedPersonalInterests()
		progressBar.update('Personal Interest seeded')

		await this.seedIcbGiftCards()
		progressBar.update('ICB Gift Cards seeded')

		await this.seedMeiliSearch()
		progressBar.update('MeiliSearch seeded')
		progressBar.complete()
	}

	async setAdmin() {
		await this.admin.deleteMany({}).exec()
		await this.admin.create({
			uid: 1,
			email: '<EMAIL>',
			role: 'super admin',
			block: false,
			name: 'Dennis',
			password: '$2a$10$MYHZRD1vWeMvPpUtTtpfj.3o9pP0OLtbiZYP8scgvBWJt0wSAYon6', //12345678
		})
	}
	async getAdmin() {
		return await this.admin.findOne({ email: '<EMAIL>' }).exec()
	}

	async seedAffiliation() {
		const admin = await this.getAdmin()
		const affiliations = Array.from({ length: 30 }).map((_, index) => ({
			uid: index + 1,
			name: faker.company.name(),
			apiName: faker.internet.domainWord(),
			apiKey: faker.string.alpha(20),
			active: true,
			createdBy: admin,
			updatedBy: admin,
		}))
		await this.affiliation.insertMany(affiliations)
	}

	async getAffiliations() {
		return await this.affiliation.find().exec()
	}

	async seedCategory() {
		const admin = (await this.getAdmin()) as AdminDocument
		const imageArray = [
			'https://cdn.iconscout.com/icon/free/png-512/free-discount-3564492-2980371.png?f=webp&w=512',
			'https://cdn.iconscout.com/icon/free/png-512/free-shopping-cart-3564516-2980301.png?f=webp&w=512',
			'https://cdn.iconscout.com/icon/free/png-512/free-discount-shopping-3564478-2980357.png?f=webp&w=512',
			'https://cdn.iconscout.com/icon/free/png-512/free-money-3564504-2980289.png?f=webp&w=512',
			'https://cdn.iconscout.com/icon/free/png-512/free-gift-7413177-6128262.png?f=webp&w=512',
		]
		const categories: Category[] = Array.from({ length: 30 }).map((_, index) => ({
			uid: index + 1,
			name: faker.commerce.department(),
			image: {
				publicId: faker.number.int().toString(),
				secureUrl: imageArray[Math.floor(Math.random() * imageArray.length)] as string,
			},
			trending: faker.datatype.boolean(),
			trendingPriority: faker.number.int({ min: 1, max: 5 }),
			priority: faker.number.int({ min: 1, max: 10 }),
			active: true,
			createdBy: admin,
			updatedBy: admin,
			isTop: false,
		}))
		await this.category.insertMany(categories)
	}

	async getCategories() {
		return await this.category.find().exec()
	}

	async seedSubCategory() {
		const admin = (await this.getAdmin()) as AdminDocument
		const imageArray = [
			'https://c0.klipartz.com/pngpicture/510/125/gratis-png-carrito-de-compras-software-compras-en-linea-e-commerce-carrito-de-compras.png',
			'https://img.freepik.com/premium-psd/blue-sneakers-shoes-isolated-transparent-background-png-psd_888962-1578.jpg',
			'https://rukminim2.flixcart.com/flap/128/128/image/69c6589653afdb9a.png?q=100',
			'https://rukminim2.flixcart.com/fk-p-flap/128/128/image/05d708653beff580.png?q=100',
			'https://rukminim2.flixcart.com/image/170/170/xif0q/dslr-camera/i/d/6/eos-r5-24-105-45-r5-canon-original-imagkk365tvry2gz.jpeg?q=90',
			'https://rukminim2.flixcart.com/image/340/340/xif0q/power-bank/d/a/f/-original-imagky3e8yp5ebvr.jpeg?q=60',
			'https://rukminim2.flixcart.com/flap/128/128/image/ab7e2b022a4587dd.jpg?q=100',
			'https://rukminim2.flixcart.com/fk-p-flap/128/128/image/0139228b2f7eb413.jpg?q=100',
			'https://rukminim2.flixcart.com/flap/128/128/image/29327f40e9c4d26b.png?q=100',
			'https://rukminim2.flixcart.com/flap/128/128/image/dff3f7adcf3a90c6.png?q=100',
		]
		const categories = (await this.getCategories()) as CategoryDocument[]
		const subCategories: SubCategory[] = Array.from({ length: 60 }).map((_, index) => ({
			uid: index + 1,
			name: faker.commerce.productName(),
			description: faker.commerce.productDescription(),
			image: {
				publicId: faker.number.int().toString(),
				secureUrl: imageArray[Math.floor(Math.random() * imageArray.length)] as string,
			},
			active: faker.datatype.boolean(),
			category: categories[Math.floor(Math.random() * categories.length)] as CategoryDocument,
			createdBy: admin,
			updatedBy: admin,
		}))

		await this.subCategory.insertMany(subCategories)
		const extraCategory = await this.category.create({
			uid: 31,
			name: 'More Shoppings',
			image: {
				publicId: faker.number.int().toString(),
				secureUrl:
					'https://cdn.iconscout.com/icon/free/png-512/free-discount-3564492-2980371.png?f=webp&w=512',
			},
			trending: faker.datatype.boolean(),
			trendingPriority: faker.number.int({ min: 1, max: 5 }),
			priority: faker.number.int({ min: 1, max: 10 }),
			active: true,
			createdBy: admin,
			updatedBy: admin,
		})
		const extraLinkedSubCategory: SubCategory[] = Array.from({ length: 3 }).map((_, index) => ({
			uid: index + 61,
			name: ['100% Cashback Stores', '50% Cashback Stores', '25% Cashback Stores'][index] as string,
			description: faker.commerce.productDescription(),
			image: {
				publicId: faker.number.int().toString(),
				secureUrl:
					'https://cdn.iconscout.com/icon/free/png-512/free-discount-3564492-2980371.png?f=webp&w=512',
			},
			active: true,
			category: extraCategory as CategoryDocument,
			createdBy: admin,
			updatedBy: admin,
		}))
		await this.subCategory.insertMany(extraLinkedSubCategory)
	}

	async getSubCategories() {
		return await this.subCategory.find().exec()
	}

	async seedStore() {
		const admin = (await this.getAdmin()) as AdminDocument
		const affiliations = (await this.getAffiliations()) as AffiliationDocument[]
		const imageArray = [
			'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/2560px-Amazon_logo.svg.png',
			'https://cdn.worldvectorlogo.com/logos/flipkart.svg',
			'https://cdn.worldvectorlogo.com/logos/philips.svg',
			'https://cdn.worldvectorlogo.com/logos/bosch-rexroth-logo-1.svg',
			'https://cdn.worldvectorlogo.com/logos/bosch.svg',
			'https://cdn.worldvectorlogo.com/logos/vale-logo.svg',
			'https://cdn.worldvectorlogo.com/logos/vale-music.svg',
			'https://cdn.worldvectorlogo.com/logos/designtech-international-1.svg',
			'https://cdn.worldvectorlogo.com/logos/bekins.svg',
			'https://cdn.worldvectorlogo.com/logos/panalytical-logo.svg',
		]
		const storePromises = Array.from({ length: 50 }).map(
			async (_, index): Promise<Partial<Store>> => {
				const cashback = {
					type: ['fiat', '%'][Math.floor(Math.random() * 2)],
					value: faker.number.int({ min: 1, max: 100 }),
				}
				const ratesCount = faker.number.int({ min: 1, max: 1000 })
				return {
					uid: index + 1,
					name: faker.company.name(),
					logo: {
						publicId: faker.number.int().toString(),
						secureUrl: imageArray[Math.floor(Math.random() * imageArray.length)] as string,
					},
					banner: {
						publicId: faker.number.int().toString(),
						secureUrl: faker.image.url({ height: 280, width: 1600 }) as string,
					},
					offerWarning: `<ul><li>Up to ${cashback.value}${
						cashback.type === '%' ? '%' : 'Rs'
					} Cashback</li><li>Extra 10% Cashback</li></ul>`,
					affiliation: affiliations[
						Math.floor(Math.random() * affiliations.length)
					] as AffiliationDocument,
					campaignType: faker.lorem.word(),
					storeOffer: `Up to ${cashback.value}${cashback.type === '%' ? '%' : 'Rs'} Cashback`,
					relatedStores: [], // Adjust based on your logic or seed data
					minimumAmount: faker.number.int({ min: 1, max: 1000 }),
					maximumAmount: faker.number.int({ min: 1001, max: 10_000 }),
					description: faker.lorem.paragraph(),
					detailedDescription: faker.lorem.paragraphs(),
					reliability: faker.number.int({ min: 1, max: 5 }),
					trackable: faker.datatype.boolean(),
					priority: faker.number.int({ min: 1, max: 5 }),
					autoCheck: faker.datatype.boolean(),
					homeOffer: faker.lorem.sentence(),
					cashbackPercent: cashback.type === '%' ? cashback.value : 0,
					cashbackAmount: cashback.type === 'fiat' ? cashback.value : 0,
					isSpecial: faker.helpers.arrayElement(['none', 'special', 'new', 'high', '100%']),
					storeWarning: faker.lorem.sentence(),
					affiliateLink: faker.internet.url(),
					offerType: faker.helpers.arrayElement(['flat', 'upto']),
					storeTopWarning: faker.lorem.sentence(),
					topWarningLink: faker.internet.url(),
					topWarningShowInactive: faker.datatype.boolean(),
					warningType: 'all',
					storeHowToGet: faker.lorem.sentence(),
					storeTerms: `<ul>${Array.from({ length: 5 })
						.map(() => `<li>${faker.lorem.sentence()}</li>`)
						.join('')}</ul>`,
					noAppSaleCategory: faker.datatype.boolean(),
					noMobileWebSaleCategory: faker.datatype.boolean(),
					noDesktopWebSaleCategory: faker.datatype.boolean(),
					isInstant: faker.datatype.boolean(),
					deepLinkEnable: faker.datatype.boolean(),
					trackingTime: `${faker.number.int({
						min: 3,
						max: 5,
					})}-${faker.number.int({ min: 6, max: 9 })} days`,
					ratesCount,
					ratesTotal: faker.number.int({ min: 1, max: ratesCount * 5 }),
					confirmationTime: `${faker.number.int({
						min: 40,
						max: 50,
					})}-${faker.number.int({ min: 51, max: 90 })} days`,
					missingAccepted: faker.datatype.boolean(),
					importantPoints: faker.lorem.sentence(),
					bgColor: faker.internet.color(),
					trending: faker.datatype.boolean(),
					createdBy: admin,
					updatedBy: admin,
					active: true,
					// giftCard
				}
			}
		)
		const stores = await Promise.all(storePromises)
		await this.store.insertMany(stores)
	}

	async getStores() {
		return await this.store.find().populate('affiliation').exec()
	}

	async addCategoriesToStores() {
		const stores = (await this.getStores()) as StoreDocument[]
		const categoriesData = (await this.getCategories()) as CategoryDocument[]

		const operations = (
			await Promise.all(
				stores.map(async store => {
					const categoriesWithSubsPromises = categoriesData.map(async category => {
						const allSubCategories = await this.subCategory.find().populate('category').exec()
						const subCategories = allSubCategories.filter(sc => sc.category.uid === category.uid)
						if (subCategories.length > 0) {
							return {
								category,
								subCategories: faker.helpers.arrayElements(
									subCategories,
									faker.number.int({ min: 1, max: subCategories.length })
								),
							}
						}
					})

					const categoriesWithSubs = await Promise.all(categoriesWithSubsPromises)

					if (categoriesWithSubs.length > 0) {
						return {
							updateOne: {
								filter: { _id: store._id },
								update: { $set: { categories: categoriesWithSubs } },
							},
						}
					}
					return null
				})
			)
		)
			// biome-ignore lint/suspicious/noExplicitAny: required for external API
			.filter(op => op !== null) as any[]

		if (operations.length > 0) {
			await this.store.bulkWrite(operations).catch(console.error)
		}
	}

	async addRelatedStoresToStores() {
		const stores = (await this.getStores()) as StoreDocument[]
		const operations = stores.map(store => {
			const allStores = stores.filter(s => s._id.toString() !== store._id.toString())
			return {
				updateOne: {
					filter: { _id: store._id },
					update: {
						$set: {
							relatedStores: faker.helpers.arrayElements(
								allStores,
								faker.number.int({ min: 0, max: allStores.length })
							),
							instantStore: faker.helpers.arrayElement(allStores),
						},
					},
				},
			}
		})

		await this.store.bulkWrite(operations)
	}

	async seedStoreCategory() {
		const admin = (await this.getAdmin()) as AdminDocument
		const stores = (await this.getStores()) as StoreDocument[]
		const affiliation = (await this.getAffiliations()) as AffiliationDocument[]
		const storeCategories = Array.from({ length: 10 }).map((_, index) => {
			return {
				uid: index + 1,
				affiliation: affiliation[
					Math.floor(Math.random() * affiliation.length)
				] as AffiliationDocument,
				store: stores[Math.floor(Math.random() * stores.length)] as StoreDocument,
				couponCode: faker.lorem.word(),
				active: true,
				createdBy: admin,
				description: faker.lorem.sentence(),
				updatedBy: admin,
				dateExpiry: faker.date.future(),
				device: ['app', 'website'][Math.floor(Math.random() * 3)] as string,
				gettingNewUserRate: faker.number.int({ min: 50, max: 1000 }),
				gettingOldUserRate: faker.number.int({ min: 50, max: 1000 }),
				newUserOfferPercent: faker.number.int({ min: 1, max: 100 }),
				oldUserOfferPercent: faker.number.int({ min: 1, max: 100 }),
				name: faker.lorem.word(),
				newUserOfferAmount: faker.number.int({ min: 50, max: 1000 }),
				oldUserOfferAmount: faker.number.int({ min: 1, max: 600 }),
				gettingType: faker.helpers.arrayElement(['percent', 'amount']),
				notes: faker.lorem.sentence(),
				sectionLink: faker.internet.url(),
				type: ['coupon', 'deal', 'offer'][Math.floor(Math.random() * 3)] as string,
				weeklyImage: {
					publicId: faker.number.int().toString(),
					secureUrl: faker.image.url() as string,
				},
			}
		})

		await this.storeCategory.insertMany(storeCategories)
	}

	async getStoreCategoryStore(id: Types.ObjectId) {
		return await this.storeCategory.find({ store: id }).exec()
	}
	async seedOffers() {
		const stores = (await this.getStores()) as StoreDocument[]
		const categories = (await this.getCategories()) as CategoryDocument[]
		const affiliations = (await this.getAffiliations()) as AffiliationDocument[]
		const admin = (await this.getAdmin()) as AdminDocument
		// const storeCategories = await
		const offers: Partial<Offer>[] = []
		for (let i = 1; i < 30; i++) {
			const categoriesWithSubs = []
			for (const category of categories) {
				if (faker.datatype.boolean()) {
					const allSubCategories = await this.subCategory.find().populate('category').exec()
					const subCategories = allSubCategories.filter(sc => sc.category.uid === category.uid)
					if (subCategories.length > 0) {
						categoriesWithSubs.push({
							category,
							subCategories: faker.helpers.arrayElements(
								subCategories.map(sc => sc),
								faker.number.int({ min: 1, max: subCategories.length })
							),
						})
					}
				}
			}
			const cashback = {
				type: ['fiat', '%'][Math.floor(Math.random() * 2)],
				value: faker.number.int({ min: 1, max: 100 }),
			}
			const store = stores[Math.floor(Math.random() * stores.length)] as StoreDocument
			const storeCategories = (await this.getStoreCategoryStore(
				store._id
			)) as StoreCategoryDocument[]
			offers.push({
				uid: i,
				store,
				storeCategory: storeCategories[Math.floor(Math.random() * storeCategories.length)],
				categories: categoriesWithSubs, // Ensure this is an array of Categories document references
				title: faker.commerce.productName(),
				offer: `Up to ${cashback.value}${cashback.type === '%' ? '%' : 'Rs'} Cashback`,
				discount: faker.number.int({ min: 1, max: 100 }),
				itemPrice: +faker.commerce.price(),
				importantUpdate: `<ul>${Array.from({ length: 5 })
					.map(() => `<li>${faker.lorem.sentence()}</li>`)
					.join('')}</ul>`,
				url: faker.internet.url(),
				productImage: {
					publicId: faker.number.int().toString(),
					secureUrl: faker.image.url({ height: 200, width: 200 }) as string,
				},
				description: faker.lorem.sentences(2),
				affiliation: affiliations[
					Math.floor(Math.random() * affiliations.length)
				] as AffiliationDocument,
				couponCode: faker.number.int().toString(),
				isCoupon: faker.datatype.boolean(),
				link: faker.internet.url(),
				offerPercent: cashback.type === '%' ? cashback.value : undefined,
				offerAmount: cashback.type === 'fiat' ? cashback.value : undefined,
				userType: ['new', 'existing', 'both'][Math.floor(Math.random() * 3)],
				howToGet: faker.lorem.sentence(),
				terms: `<ul>${Array.from({ length: 5 })
					.map(() => `<li>${faker.lorem.sentence()}</li>`)
					.join()}</ul>`,
				priority: faker.number.int({ min: 1, max: 10 }),
				keySpecs: `<ul>${Array.from({ length: 5 })
					.map(() => `<li>${faker.lorem.sentence()}</li>`)
					.join()}</ul>`,
				stockEnds: faker.datatype.boolean(),
				dateExpiry: faker.date.between({
					from: new Date(new Date().setDate(new Date().getDate() - 3)),
					to: new Date('2024-12-20'),
				}),
				dateStart: faker.date.past(),
				visibility: faker.datatype.boolean(),
				active: faker.datatype.boolean(),
				trending: faker.datatype.boolean(),
				missedDeal: faker.datatype.boolean(),
				migrated: false,
				createdBy: admin,
				updatedBy: admin,
			})
		}
		await this.offer.insertMany(offers)
	}

	async seedBanners() {
		const admin = (await this.getAdmin()) as AdminDocument
		const banner: Banner[] = Array.from({ length: 10 }).map((_, index) => {
			const type = ['context', 'giftCard'][Math.floor(Math.random() * 2)] as string
			return {
				uid: index + 1,
				desktopBanner: {
					publicId: faker.number.int().toString(),
					secureUrl: faker.image.url({ height: 280, width: 1600 }) as string,
				},
				mobileBanner: {
					publicId: faker.number.int().toString(),
					secureUrl: faker.image.url({ height: 260, width: 450 }) as string,
				},
				expiryDate: faker.date.between({
					from: new Date(),
					to: new Date('2024-12-20'),
				}),
				redirectUrl: faker.internet.url(),
				active: true,
				createdBy: admin,
				updatedBy: admin,
				type,
				priority: type === 'context' ? 0 : faker.number.int({ min: 1, max: 10 }),
				termsContent: type === 'context' ? 'not-available' : faker.lorem.sentence(),
				termsTitle: type === 'context' ? 'not-available' : faker.lorem.sentence(),
			}
		})
		await this.banner.insertMany(banner)
	}

	async seedMobileStory() {
		const admin = (await this.getAdmin()) as AdminDocument
		const stores = (await this.getStores()) as StoreDocument[]
		const mobileStories: MobileStory[] = Array.from({ length: 10 }).map((_, index) => ({
			uid: index + 1,
			image: {
				publicId: faker.number.int().toString(),
				secureUrl: faker.image.url({ height: 1280, width: 720 }) as string,
			},

			duration: faker.number.int({ min: 3000, max: 5000 }),
			title: faker.lorem.sentence(),
			description: faker.lorem.sentences(2),
			buttonText: faker.lorem.word(),
			redirectUrl: faker.internet.url(),
			expiryDate: faker.date.between({
				from: new Date(),
				to: new Date('2024-12-20'),
			}),
			createdBy: admin,
			updatedBy: admin,
			store: stores[Math.floor(Math.random() * stores.length)] as StoreDocument,
			active: true,
		}))
		await this.mobileStory.insertMany(mobileStories)
	}

	async seedQuickAccess() {
		const admin = (await this.getAdmin()) as AdminDocument
		const imageArray = [
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1706902203/plpypaaqko21f0337x1x.svg',
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1706902269/epo1cbbqpjtmzl6ncy1r.svg',
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1706902295/jmefkgrsnypah39jczrv.svg',
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1706902328/ihinuyadx5as8ukodv0m.svg',
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1707464095/ofdel95tvbff4obnwj2f.svg',
		]
		const redirectUrl = [
			'/deals-and-coupons',
			'/giftcards',
			'/on-going-sale-offers',
			'/online-free-shopping-stores',
			'/my-earnings-overview',
			'/payment-history',
		]
		const quickAccess: QuickAccess[] = Array.from({ length: 10 }).map((_, index) => ({
			uid: index + 1,
			title: faker.vehicle.manufacturer(),
			redirectUrl: redirectUrl[Math.floor(Math.random() * redirectUrl.length)] as string,
			icon: {
				publicId: faker.number.int().toString(),
				secureUrl: imageArray[Math.floor(Math.random() * imageArray.length)] as string,
			},
			createdBy: admin,
			active: true,
		}))
		await this.quickAccess.insertMany(quickAccess)
	}

	async seedHeroAccess() {
		const admin = (await this.getAdmin()) as AdminDocument
		const imageArray = [
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1706902203/plpypaaqko21f0337x1x.svg',
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1706902269/epo1cbbqpjtmzl6ncy1r.svg',
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1706902295/jmefkgrsnypah39jczrv.svg',
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1706902328/ihinuyadx5as8ukodv0m.svg',
			'https://res.cloudinary.com/dzjgv6djl/image/upload/v1707464095/ofdel95tvbff4obnwj2f.svg',
		]
		const redirectUrl = [
			'/deals-and-coupons',
			'/giftcards',
			'/on-going-sale-offers',
			'/online-free-shopping-stores',
			'/my-earnings-overview',
			'/payment-history',
		]
		const heroAccess: HeroAccess[] = Array.from({ length: 10 }).map((_, index) => ({
			uid: index + 1,
			title: faker.vehicle.manufacturer(),
			redirectUrl: redirectUrl[Math.floor(Math.random() * redirectUrl.length)] as string,
			icon: {
				publicId: faker.number.int().toString(),
				secureUrl: imageArray[Math.floor(Math.random() * imageArray.length)] as string,
			},
			createdBy: admin,
			active: true,
		}))
		await this.heroAccess.insertMany(heroAccess)
	}

	async seedOngoingSales() {
		const admin = (await this.getAdmin()) as AdminDocument
		const offers = await this.offer.find().exec()
		const ongoingSales: OngoingSaleOffers[] = Array.from({ length: 25 }).map((_, index) => ({
			uid: index + 1,
			saleName: faker.music.songName(),
			saleStartDate: faker.date.past(),
			saleEndDate: faker.date.future(),
			saleLogo: {
				publicId: faker.number.int().toString(),
				secureUrl: faker.image.url({ height: 150, width: 150 }) as string,
			},
			offers: faker.helpers.arrayElements(offers, faker.number.int({ min: 1, max: offers.length })),
			createdBy: admin,
			updatedBy: admin,
		}))
		await this.ongoingSales.insertMany(ongoingSales)
	}

	async seedGiftCard() {
		const admin = (await this.getAdmin()) as Admin
		const stores = await this.getStores()
		const imageArray = [
			'https://cdn.iconscout.com/icon/free/png-512/free-discount-3564492-2980371.png?f=webp&w=512',
			'https://cdn.iconscout.com/icon/free/png-512/free-shopping-cart-3564516-2980301.png?f=webp&w=512',
			'https://cdn.iconscout.com/icon/free/png-512/free-discount-shopping-3564478-2980357.png?f=webp&w=512',
			'https://cdn.iconscout.com/icon/free/png-512/free-money-3564504-2980289.png?f=webp&w=512',
			'https://cdn.iconscout.com/icon/free/png-512/free-gift-7413177-6128262.png?f=webp&w=512',
		]

		const icbGiftCard = {
			uid: 1,
			name: faker.commerce.productName(),
			cardType: 'icb',
			active: true,
			minimumAmount: faker.number.int({ min: 1, max: 5 }) * 100,
			maximumAmount: faker.number.int({ min: 2, max: 20 }) * 1000,
			refresh: 'yes',
			relatedCbStore: faker.helpers.arrayElement(stores),
			logo: {
				publicId: faker.number.int().toString(),
				secureUrl: imageArray[Math.floor(Math.random() * imageArray.length)] as string,
			},
			image: {
				publicId: faker.number.int().toString(),
				secureUrl: '/_next/image?url=%2Ftemp%2Fgiftcards%2Fgiftcard1.png&w=640&q=75',
			},
			categories: [],
			relatedInstantStore: stores[Math.floor(Math.random() * stores.length)] as StoreDocument,
			isCustomDenomination: true,
			relatedGiftCards: [], // Adjust based on your logic or seed data
			discountGetting: faker.number.int({ min: 1, max: 100 }),
			cashbackGiving: faker.number.int({ min: 1, max: 100 }),
			validity: faker.date.future(),
			notes: faker.lorem.sentence(),
			howToUse: Array.from({ length: 3 }).map(() => faker.lorem.sentence()),
			terms: `<ul>${Array.from({ length: 5 })
				.map(() => `<li>${faker.lorem.sentence()}</li>`)
				.join('')}</ul>`,
			description: faker.lorem.sentence(),
			priority: faker.number.int({ min: 1, max: 10 }),
			qwickcilverImage: {
				publicId: faker.number.int().toString(),
				secureUrl: '/_next/image?url=%2Ftemp%2Fgiftcard.png&w=2048&q=75',
			},
			qwickcilverId: faker.lorem.word(),
			tncMail: faker.internet.email(),
			createdBy: admin,
			updatedBy: admin,
			denominations: [100, 200, 500, 1000, 2000, 5000],
			provider: faker.company.buzzPhrase(),
		}
		const giftCards = Array.from({ length: 30 }).map(
			(_, index): GiftCard => ({
				uid: index + 2,
				name: faker.commerce.productName(),
				cardType: 'qwickcilver',
				active: true,
				minimumAmount: faker.number.int({ min: 1, max: 5 }) * 100,
				maximumAmount: faker.number.int({ min: 2, max: 50 }) * 1000,
				refresh: 'yes',
				relatedCbStore: faker.helpers.arrayElement(stores),
				logo: {
					publicId: faker.number.int().toString(),
					secureUrl: imageArray[Math.floor(Math.random() * imageArray.length)] as string,
				},
				isCustomDenomination: faker.datatype.boolean(),
				image: {
					publicId: faker.number.int().toString(),
					secureUrl: faker.image.url({ height: 285, width: 174 }) as string,
				},
				categories: [],
				relatedInstantStore: stores[Math.floor(Math.random() * stores.length)] as StoreDocument,
				relatedGiftCards: [], // Adjust based on your logic or seed data
				discountGetting: faker.number.int({ min: 1, max: 100 }),
				cashbackGiving: faker.number.int({ min: 1, max: 100 }),
				validity: faker.date.future(),
				notes: faker.lorem.sentence(),
				howToUse: Array.from({ length: 3 }).map(() => faker.lorem.sentence()),
				terms: `<ul>${Array.from({ length: 5 })
					.map(() => `<li>${faker.lorem.sentence()}</li>`)
					.join('')}</ul>`,
				description: faker.lorem.sentence(),
				priority: faker.number.int({ min: 1, max: 10 }),
				qwickcilverImage: {
					publicId: faker.number.int().toString(),
					secureUrl: '/_next/image?url=%2Ftemp%2Fgiftcard.png&w=2048&q=75',
				},
				qwickcilverId: faker.lorem.word(),
				tncMail: faker.internet.email(),
				createdBy: admin,
				updatedBy: admin,
				denominations: [100, 200, 500, 1000, 2000, 5000],
				provider: faker.company.buzzPhrase(),
			})
		)
		await this.giftCard.insertMany([icbGiftCard, ...giftCards])
	}

	async getGiftCards() {
		return await this.giftCard.find().exec()
	}

	async seedRelatedGiftCards() {
		const giftCards = (await this.getGiftCards()) as GiftCardDocument[]
		const operations = giftCards.map(giftCard => {
			const allGiftCards = giftCards.filter(s => s._id.toString() !== giftCard._id.toString())
			return {
				updateOne: {
					filter: { _id: giftCard._id },
					update: {
						$set: {
							relatedGiftCards: faker.helpers.arrayElements(
								allGiftCards,
								faker.number.int({ min: 1, max: 10 })
							),
						},
					},
				},
			}
		})

		await this.giftCard.bulkWrite(operations)

		const stores = (await this.getStores()) as StoreDocument[]
		for (const store of stores) {
			store.giftCard = faker.helpers.arrayElement(giftCards)
			await store.save()
		}
	}

	async addCategoriesToGiftCard() {
		const giftCards = (await this.getGiftCards()) as GiftCardDocument[]
		const categoriesData = (await this.getCategories()) as CategoryDocument[]

		const operations = (
			await Promise.all(
				giftCards.map(async giftCards => {
					const categoriesWithSubsPromises = categoriesData.map(async category => {
						const allSubCategories = await this.subCategory.find().populate('category').exec()
						const subCategories = allSubCategories.filter(sc => sc.category.uid === category.uid)
						if (subCategories.length > 0) {
							return {
								category,
								subCategories: faker.helpers.arrayElements(
									subCategories,
									faker.number.int({ min: 1, max: subCategories.length })
								),
							}
						}
					})

					const categoriesWithSubs = await Promise.all(categoriesWithSubsPromises)

					if (categoriesWithSubs.length > 0) {
						return {
							updateOne: {
								filter: { _id: giftCards._id },
								update: { $set: { categories: categoriesWithSubs } },
							},
						}
					}
					return null
				})
			)
		)
			// biome-ignore lint/suspicious/noExplicitAny: required for external API
			.filter(op => op !== null) as any[]

		if (operations.length > 0) {
			await this.giftCard.bulkWrite(operations).catch(console.error)
		}
	}

	async seedUsers() {
		const admin = (await this.getAdmin()) as AdminDocument
		const users = Array.from({ length: 5 }).map(
			(_, index): Partial<User> => ({
				uid: index + 1,
				name: faker.person.firstName(),
				email: faker.internet.email(),
				mobile: faker.number.int({ min: 1_000_000_000, max: 9_999_999_999 }),
				mobileVerified: true,
				status: 'active',
				avatar: {
					publicId: faker.number.int().toString(),
					secureUrl: faker.image.avatar(),
				},
				updatedBy: admin,
				pendingBalance: 2000,
				balance: 10_000,
				giftCardBalance: 5000,
				referralCode: hash({
					email: index + 1,
				})
					.toUpperCase()
					.slice(0, 6),
			})
		)
		await this.user.insertMany(users)

		const lastDocument = await this.user.findOne().sort({ _id: -1 })
		let uid = 1
		if (lastDocument) {
			uid = lastDocument.uid + 1
		}
		await this.user.create({
			uid,
			name: 'Dennis Sam',
			email: '<EMAIL>',
			balance: 10_000,
			status: 'active',
		})
		await this.user.create({
			uid: uid + 1,
			name: 'Ashish',
			balance: 10_000,
			email: '<EMAIL>',
			status: 'active',
		})
	}

	async seedReferral(email: string) {
		// await this.earning.deleteMany({}).exec()
		const user = await this.user.findOne({ email }).exec()
		if (!user) {
			throw new Error('User not found')
		}
		const lastDocument = await this.user.findOne().sort({ _id: -1 })
		let uid = 1
		if (lastDocument) {
			uid = lastDocument.uid + 1
		}
		const admin = (await this.getAdmin()) as AdminDocument
		const users = Array.from({ length: 10 }).map((_, index) => ({
			uid: uid + index,
			name: faker.person.fullName(),
			email: faker.internet.email(),
			referral: user,
			mobile: faker.number.int({ min: 1_000_000_000, max: 9_999_999_999 }),
			mobileVerified: true,
			status: 'active',
			avatar: {
				publicId: faker.number.int().toString(),
				secureUrl: faker.image.avatar(),
			},
			updatedBy: admin,
			pendingBalance: 20,
			balance: 10_000,
			giftCardBalance: 50,
		}))
		await this.user.insertMany(users)

		//////////////

		const stores = (await this.getStores()) as StoreDocument[]
		const clicks = (await this.click.find().exec()) as ClickDocument[]

		const earning = users
			.flatMap((user, _index) => {
				const userEarnings = Array.from({ length: 5 }).map(() => {
					const randomStoreIndex = Math.floor(Math.random() * stores.length)
					const randomClickIndex = Math.floor(Math.random() * clicks.length)

					const randomId = Math.floor(Math.random() * 100_000_000_000)
					const randomStore = stores[randomStoreIndex] as StoreDocument
					const randomClick = clicks[randomClickIndex] as ClickDocument

					if (user) {
						return {
							uid: randomId,
							referenceId: hash({
								email: user.email,
								uid: randomId,
							}).toUpperCase(),
							user,
							store: randomStore,
							click: randomClick,
							status: faker.helpers.arrayElement(['pending', 'confirmed', 'cancelled']),
							amountGot: 20,
							referralCommission: 10,
							cashbackAmount: 20,
							confirmDate: new Date(),
							remarks: faker.lorem.sentence(),
							updatedBy: admin,
							createdBy: admin,
						}
					}
				})
				return userEarnings
			})
			.filter(op => op !== null) // Added return statement here

		await this.earning.insertMany(earning)

		const myEarnings = Array.from({ length: 10 }).map(() => {
			const randomStoreIndex = Math.floor(Math.random() * stores.length)
			const randomClickIndex = Math.floor(Math.random() * clicks.length)

			const randomId = Math.floor(Math.random() * 100_000_000_000)
			const randomStore = stores[randomStoreIndex] as StoreDocument
			const randomClick = clicks[randomClickIndex] as ClickDocument

			return {
				uid: randomId,
				referenceId: hash({
					email: user.email,
					uid: randomId,
				}).toUpperCase(),
				user,
				store: randomStore,
				click: randomClick,
				status: faker.helpers.arrayElement(['pending', 'confirmed', 'cancelled']),
				amountGot: 20,
				referralCommission: 10,
				cashbackAmount: 20,
				confirmDate: new Date(),
				remarks: faker.lorem.sentence(),
				updatedBy: admin,
				createdBy: admin,
				createdAt: faker.date.between({
					from: '2024-01-01T10:36:57.975Z',
					to: '2024-12-01T10:36:57.975Z',
				}),
			}
		})
		await this.earning.insertMany(myEarnings)
	}

	async seedClicks() {
		const users = (await this.user.find().exec()) as UserDocument[]
		const stores = (await this.getStores()) as StoreDocument[]
		const clicks = users
			.flatMap((user, _index) => {
				const userClicks = Array.from({ length: 5 }).map(() => {
					const randomIndex = Math.floor(Math.random() * stores.length)
					const randomId = Math.floor(Math.random() * 100_000_000_000)
					const store = stores[randomIndex] as StoreDocument
					if (store) {
						return {
							uid: randomId,
							user,
							store,
							type: 'express',
							title: store.name,
							url: store.logo.secureUrl,
							affiliation: store.affiliation,
							referenceId: `CBCLK${hash({
								uid: randomId,
								user,
								affiliation: store.affiliation,
								time: Date.now(),
							}).toUpperCase()}`,
						}
					}
				})
				return userClicks
			})
			.filter(op => op !== null)
		await this.click.insertMany(clicks)
	}

	async seedReviews() {
		await this.review.deleteMany({}).exec()
		const stores = (await this.getStores()) as StoreDocument[]
		const users = (await this.user.find().exec()) as UserDocument[]
		const reviews = stores
			.flatMap((store, _index) => {
				const userReviews = Array.from({ length: 5 }).map(() => {
					const randomIndex = Math.floor(Math.random() * users.length)
					const user = users[randomIndex] as UserDocument
					if (store) {
						return {
							uid: faker.number.int({ min: 1, max: 10_000_000 }),
							reviewer: user,
							store,
							rating: faker.helpers.arrayElement([1, 2, 3, 4, 5]),
							review: faker.lorem.paragraph(),
							status: faker.helpers.arrayElement(['approved', 'awaiting_to_approve', 'rejected']),
						}
					}
				})
				return userReviews
			})
			.filter(op => op !== null)
		await this.review.insertMany(reviews)
	}

	async seedReviewsOfUser(email: string) {
		await this.review.deleteMany({}).exec()
		const stores = (await this.getStores()) as StoreDocument[]
		const user = await this.user.findOne({ email }).exec()
		if (!user) {
			throw new Error('User not found')
		}
		const reviews = stores
			.flatMap(store => {
				const userReviews = Array.from({ length: 30 }).map(() => {
					if (store) {
						return {
							uid: faker.number.int({ min: 1, max: 10_000_000 }),
							reviewer: user,
							store,
							rating: faker.helpers.arrayElement([1, 2, 3, 4, 5]),
							review: faker.lorem.paragraph(),
							status: faker.helpers.arrayElement(['approved', 'awaiting_to_approve', 'rejected']),
						}
					}
				})
				return userReviews
			})
			.filter(op => op !== null)
		await this.review.insertMany(reviews)
	}

	async seedEarning() {
		await this.earning.deleteMany({}).exec()
		const stores = (await this.getStores()) as StoreDocument[]
		const users = (await this.user.find().exec()) as UserDocument[]
		const clicks = (await this.click.find().exec()) as ClickDocument[]
		const admin = (await this.getAdmin()) as AdminDocument
		const affiliations = (await this.getAffiliations()) as AffiliationDocument[]

		const earning = users
			.flatMap((_user, _index) => {
				const userEarnings = Array.from({ length: 15 }).map(() => {
					const randomUserIndex = Math.floor(Math.random() * users.length)
					const randomStoreIndex = Math.floor(Math.random() * stores.length)
					const randomClickIndex = Math.floor(Math.random() * clicks.length)
					const randomAffiliationIndex = Math.floor(Math.random() * affiliations.length)

					const randomId = Math.floor(Math.random() * 100_000_000_000)
					const randomUser = users[randomUserIndex] as UserDocument
					const randomStore = stores[randomStoreIndex] as StoreDocument
					const randomClick = clicks[randomClickIndex] as ClickDocument
					const randomAffiliation = affiliations[randomAffiliationIndex] as AffiliationDocument

					if (randomUser) {
						return {
							uid: randomId,
							referenceId: hash({
								email: randomUser.email,
								createdAt: new Date(),
								random: Math.random(),
							}).toUpperCase(),
							user: randomUser,
							store: randomStore,
							click: randomClick,
							status: 'pending',
							amountGot: 20,
							remarks: faker.lorem.sentence(),
							confirmDate: faker.date.future(),
							referralCommission: 10,
							affiliations: randomAffiliation,
							updatedBy: admin,
							createdBy: admin,
						}
					}
				})
				return userEarnings
			})
			.filter(op => op !== null) // Added return statement here

		await this.earning.insertMany(earning)
	}

	async seedTestimonials() {
		await this.testimonial.deleteMany({}).exec()

		const testimonial = Array.from({ length: 10 }).map((_, index) => ({
			uid: index + 1,
			active: true,
			rating: faker.helpers.arrayElement([1, 2, 3, 4, 5]),
			review: faker.lorem.paragraph(),
			reviewerAvatar: {
				publicId: faker.number.int().toString(),
				secureUrl: faker.image.avatar(),
			},
			reviewerName: faker.company.name(),
		}))
		await this.testimonial.insertMany(testimonial)
	}

	async seedPersonalInterests() {
		await this.personalInterest.deleteMany({}).exec()
		const interestsData = [
			'Electronics',
			'Fashion',
			'Home Appliances',
			'Mobile',
			'Beauty and Cosmetics',
			'Health and Personal Care',
			'Medicine and Drugs',
			'Kids and Baby Care',
			'Mobile',
			'Food and Beverages',
			'Stationary',
			'Books',
			'Mobile',
			'Grocery',
			'Food and Beverages',
			'Stationary',
		]
		const admin = (await this.getAdmin()) as AdminDocument
		const interests = interestsData.map((name, _index) => ({
			uid: faker.number.int({ min: 1, max: 10_000_000 }),
			name,
			active: true,
			createdBy: admin,
			updatedBy: admin,
		}))
		await this.personalInterest.insertMany(interests)
	}
	async seedPrivacyAndTerms() {
		await this.termsAndPrivacy.deleteMany({}).exec()
		const content =
			'<p>&nbsp;</p><blockquote class="bg-gray-100 p-4 rounded-lg"><p class="text-xl font-semibold mb-4"><strong>Terms and Conditions for Indiancashback</strong></p><p class="mb-2"><strong>Last updated: 20-10-2024</strong></p><p>Welcome to indiancashback! By accessing and using our services, you agree to comply with and be bound by the following terms and conditions. If you disagree with any part of these terms, please do not use our services.</p><p class="text-lg font-semibold mt-4">1. General Information:</p><p class="ml-4">1.1 <strong>Organization Details:</strong></p><ul class="list-disc ml-8 mb-2"><li><strong>Indiancashback</strong> is india\'s best cashback application. Our registered office is located in <strong>Banglore, India</strong>.</li></ul><p class="ml-4">1.2 <strong>Acceptance of Terms:</strong></p><ul class="list-disc ml-8 mb-2"><li>By using our services, you agree to these terms and conditions, including any policies referred to herein.</li></ul><p class="text-lg font-semibold mt-4">2. Use of Services:</p><p class="ml-4">2.1 <strong>Eligibility:</strong></p><ul class="list-disc ml-8 mb-2"><li>You must be at least 10 years old to use our services. By using our services, you represent and warrant that you are of legal age.</li></ul><p class="ml-4">2.2 <strong>Account Registration:</strong></p><ul class="list-disc ml-8 mb-2"><li>Some services may require account registration. You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account.</li></ul><p class="ml-4">2.3 <strong>Prohibited Activities:</strong></p><ul class="list-disc ml-8 mb-2"><li>You agree not to engage in any activity that may disrupt or interfere with our services, including but not limited to hacking, spreading malware, or violating any applicable laws.</li></ul><p class="text-lg font-semibold mt-4">3. Intellectual Property:</p><p class="ml-4">3.1 <strong>Ownership:</strong></p><ul class="list-disc ml-8 mb-2"><li>All content and materials provided by [Your Organization Name], including but not limited to logos, text, graphics, and software, are the property of [Your Organization Name] and are protected by copyright and other intellectual property laws.</li></ul><p class="ml-4">3.2 <strong>Use of Materials:</strong></p><ul class="list-disc ml-8 mb-2"><li>You may not reproduce, distribute, modify, display, or create derivative works of any content provided by [Your Organization Name] without our explicit written permission.</li></ul><p class="text-lg font-semibold mt-4">4. Privacy and Data Protection:</p><p class="ml-4">4.1 <strong>Data Collection:</strong></p><ul class="list-disc ml-8 mb-2"><li>We may collect and process personal information in accordance with our Privacy Policy. By using our services, you consent to such collection and processing.</li></ul><p class="ml-4">4.2 <strong>Data Security:</strong></p><ul class="list-disc ml-8 mb-2"><li>We implement reasonable security measures to protect your data. However, we cannot guarantee the security of information transmitted over the internet.</li></ul><p class="text-lg font-semibold mt-4">5. Limitation of Liability:</p><p class="ml-4">5.1 <strong>Disclaimer:</strong></p><ul class="list-disc ml-8 mb-2"><li>[Your Organization Name] provides services "as is" and does not warrant the accuracy, completeness, or reliability of any content.</li></ul><p class="ml-4">5.2 <strong>Limitation of Liability:</strong></p><ul class="list-disc ml-8 mb-2"><li>[Your Organization Name] shall not be liable for any direct, indirect, incidental, consequential, or punitive damages arising out of or relating to your use of our services.</li></ul><p class="text-lg font-semibold mt-4">6. Governing Law and Dispute Resolution:</p><p class="ml-4">6.1 <strong>Governing Law:</strong></p><ul class="list-disc ml-8 mb-2"><li>These terms and conditions are governed by the laws of [Jurisdiction]. Any disputes arising under or in connection with these terms shall be subject to the exclusive jurisdiction of the courts in [Jurisdiction].</li></ul><p class="text-lg font-semibold mt-4">7. Changes to Terms and Conditions:</p><p class="ml-4">7.1 <strong>Notification of Changes:</strong></p><ul class="list-disc ml-8 mb-2"><li>We may update these terms and conditions to reflect changes in our practices. We will notify you of any significant changes via email or through a prominent notice on our website.</li></ul><p class="text-lg font-semibold mt-4">8. Contact Us:</p><p class="ml-4">If you have any questions or concerns about these terms and conditions, please contact us at <a href="mailto:<EMAIL>" class="text-blue-500 hover:underline"><EMAIL></a></p></blockquote>'

		const admin = (await this.getAdmin()) as AdminDocument
		const types = ['terms', 'privacy']
		const documents = types.map((type, _index) => ({
			uid: faker.number.int({ min: 1, max: 10_000_000 }),
			content,
			active: true,
			type,
			createdBy: admin,
			updatedBy: admin,
		}))
		await this.termsAndPrivacy.insertMany(documents)
	}

	async seedIcbGiftCards() {
		await this.icbGiftCard.deleteMany({}).exec()
		const admin = (await this.getAdmin()) as AdminDocument
		const users = (await this.user.find().exec()) as UserDocument[]
		const giftCards = (await this.giftCard.find().exec()) as GiftCardDocument[]

		const icbGiftCards = Array.from({ length: 30 }).flatMap((_, index) => {
			const randomUser = faker.helpers.arrayElement(users)
			const randomGiftCard = faker.helpers.arrayElement(giftCards)

			return {
				uid: index + 1,
				giftCard: randomGiftCard,
				giftCardNumber: faker.number.int({
					min: 100_000_000_000,
					max: 999_999_999_999,
				}),
				orderId: faker.number.int().toString(),
				giftCardPin: faker.number.int({ min: 1000, max: 9999 }),
				createdAt: faker.date.between({
					from: new Date(),
					to: new Date('2024-12-20'),
				}),
				buyer: randomUser,
				name: randomUser.name,
				email: randomUser.email,
				mobile: randomUser.mobile,
				amount: 50,
				expiryDate: faker.date.between({
					from: new Date(),
					to: new Date('2024-12-20'),
				}),
				updatedBy: admin,
			}
		})
		await this.icbGiftCard.insertMany(icbGiftCards)
	}

	async seedMeiliSearch() {
		const client = new MeiliSearchService()

		const stores = await this.store
			.find()
			.populate('giftCard', 'name')
			.populate('categories.category', 'name')
			.populate('categories.subCategories', 'name')
			.exec()

		for (const store of stores) {
			const categories: { name: string }[] = []
			const subcategories: { name: string }[] = []
			store.categories.map(item => {
				categories.push({ name: item?.category?.name })
				item?.subCategories?.map(subCat => {
					if (!subcategories.includes({ name: subCat?.name })) {
						subcategories.push({ name: subCat?.name })
					}
				})
			})
			const storecategories = await this.storeCategory.find(
				{ store: store._id },
				{ name: 1, _id: 0 }
			)
			const storeDocument = new StoreSchema({
				id: store._id.toString(),
				uid: store.uid,
				url: store.logo.secureUrl,
				active: store.active,
				name: store.name,
				description: store.description,
				detailedDescription: store.detailedDescription,
				offerWarning: store.offerWarning,
				giftCard: store?.giftCard?.name,
				categories,
				storecategories,
				subcategories,
			})
			await client.addDocuments(storeDocument)
		}

		const offers = await this.offer
			.find()
			.populate('store', 'name')
			.populate('categories.category', 'name')
			.populate('categories.subCategories', 'name')
			.populate('storeCategory')
			.exec()

		for (const offer of offers) {
			const categories: { name: string }[] = []
			const subcategories: { name: string }[] = []
			offer.categories.map(item => {
				categories.push({ name: item?.category?.name })
				item?.subCategories?.map(subCat => {
					if (!subcategories.includes({ name: subCat?.name })) {
						subcategories.push({ name: subCat?.name })
					}
				})
			})
			const newUser = offer.storeCategory
				? offer.storeCategory?.gettingType === 'amount'
					? `₹${offer.storeCategory?.newUserOfferAmount}`
					: `${offer.storeCategory?.newUserOfferPercent}%`
				: ''
			const oldUser = offer.storeCategory
				? offer.storeCategory?.gettingType === 'amount'
					? `₹${offer.storeCategory?.oldUserOfferAmount}`
					: `${offer.storeCategory?.oldUserOfferPercent}%`
				: ''
			const offerDocument = new OfferSchema({
				id: offer._id.toString(),
				uid: offer?.uid,
				active: !!offer.active,
				title: offer?.title,
				url: offer.productImage.secureUrl,
				offerType: offer?.offerType,
				caption: offer?.offer ? offer?.offer : '',
				description: offer?.description,
				couponCode: offer?.couponCode ? offer?.couponCode : '',
				offerPercent: offer?.offerPercent,
				offerAmount: offer?.offerPercent,
				offerWarning: offer?.store?.offerWarning,
				newUserOffer: newUser,
				oldUserOffer: oldUser,
				name: offer?.store?.name,
				dateExpiry: offer?.dateExpiry ? new Date(offer.dateExpiry).getTime() : Date.now(),
				categories,
				subcategories,
				storecategories: [
					{
						name: offer?.storeCategory?.name ? offer?.storeCategory?.name : '',
					},
				],
			})

			await client.addDocuments(offerDocument)
		}
		const giftCards = await this.giftCard
			.find()
			.populate('relatedCbStore')
			.populate('relatedInstantStore', 'name')
			.populate('categories.category', 'name')
			.populate('categories.subCategories', 'name')
			.exec()

		for (const giftCard of giftCards) {
			const categories: { name: string }[] = []
			const subcategories: { name: string }[] = []
			giftCard.categories.map(item => {
				categories.push({ name: item?.category?.name })
				item?.subCategories?.map(subCat => {
					if (!subcategories.includes({ name: subCat?.name })) {
						subcategories.push({ name: subCat?.name })
					}
				})
			})

			const storecategories = await this.storeCategory.find(
				{ store: giftCard.relatedCbStore._id },
				{ name: 1, _id: 0 }
			)

			const giftCardDocument = new GiftCardSchema({
				id: giftCard._id.toString(),
				uid: giftCard.uid,
				url: giftCard.logo.secureUrl,
				active: giftCard.active,
				name: giftCard.name,
				description: giftCard.description,
				cashbackGiving: giftCard.cashbackGiving,
				relatedInstantStore: giftCard?.relatedInstantStore?.name
					? giftCard?.relatedInstantStore?.name
					: '',
				categories,
				subcategories,
				storecategories,
			})

			await client.addDocuments(giftCardDocument)
		}
	}

	async seedMissingCashback(email: string) {
		const user = await this.user.findOne({ email }).exec()
		if (!user) {
			throw new Error('User not found')
		}
		const stores = (await this.getStores()) as StoreDocument[]
		const clicks = (await this.click.find().exec()) as ClickDocument[]
		const admin = (await this.getAdmin()) as AdminDocument

		const missingCashbacks = Array.from({ length: 10 }).flatMap((_, _index) => {
			const randomStore = faker.helpers.arrayElement(stores)
			const randomClick = faker.helpers.arrayElement(clicks)
			return {
				uid: Math.floor(Math.random() * 1_000_000),
				click: randomClick,
				title: randomClick.title,
				store: randomStore,
				invoice: {
					key: faker.lorem.word(),
					location: faker.image.avatar(),
				},
				complaintId: `CBCOM${hash({
					user,
					random: Math.random(),
				}).toUpperCase()}`,
				coupon: faker.datatype.boolean(),
				orderId: faker.lorem.word(),
				message: faker.lorem.sentence(),
				paidAmount: 1000,
				userType: faker.helpers.arrayElement(['new', 'old']),
				platform: faker.helpers.arrayElement(['mobile', 'web']),
				status: faker.helpers.arrayElement(['pending', 'confirmed', 'cancelled']),
				user,
				updatedBy: admin,
			}
		})
		await this.missingCashback.insertMany(missingCashbacks)
	}
}
