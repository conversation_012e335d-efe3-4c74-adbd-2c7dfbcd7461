import { operations } from '@app/click/tags'
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { ClientInfoData } from '@shared/decorators'
import { Earning, EarningDocument } from '@shared/entities/earning.entity'
import moment from 'moment'
import { Model, Types } from 'mongoose'

import {
	Click,
	ClickDocument,
	Link,
	LinkDocument,
	Offer,
	ReqUser,
	Store,
	StoreDocument,
	User,
	UserDocument,
} from 'shared/entities'
import { generateAffiliateUrlByType, validateStoreUrl } from 'shared/helpers/affiliate-url.helper'
import {
	GenerateLinkDto,
	LinkResponseDto,
	MetricWithChangeDto,
	PaginationResponseDto,
	PeriodType,
	UserAnalyticsQueryDto,
	UserAnalyticsResponseDto,
	UserLinkResponseDto,
	UserLinksQueryDto,
} from './dto/link.dto'
import { UrlExpanderService } from './exapnd-url.service'

@Injectable()
export class LinkService {
	constructor(
		@InjectModel(Link.name) private link: Model<LinkDocument>,
		@InjectModel(Store.name) private store: Model<StoreDocument>,
		@InjectModel(User.name) private user: Model<UserDocument>,
		@InjectModel(Click.name) private click: Model<ClickDocument>,
		@InjectModel(Offer.name) private offer: Model<Offer>,
		@InjectModel(Earning.name) private earningModel: Model<EarningDocument>,
		private readonly urlExpanderService: UrlExpanderService
	) {}

	/**
	 * Detect store from URL
	 * @param url The URL to check
	 * @returns Store ID and store type
	 */
	private async detectStoreFromUrl(url: string): Promise<{ storeId: string; storeType: string }> {
		const validation = validateStoreUrl(url)

		if (!(validation.isValid && validation.storeId && validation.storeType)) {
			throw new BadRequestException(validation.message || 'Invalid URL')
		}

		return {
			storeId: validation.storeId,
			storeType: validation.storeType,
		}
	}

	private async expandUrl(shortUrl: string): Promise<string | null> {
		return this.urlExpanderService.expandUrl(shortUrl)
	}

	/**
	 * Generate a link for the given URL
	 * @param generateLinkDto DTO containing the URL
	 * @param user The user generating the link
	 * @returns The generated link details
	 */
	async generateLink(generateLinkDto: GenerateLinkDto, user: ReqUser): Promise<LinkResponseDto> {
		let { url: originalUrl } = generateLinkDto

		//if url is from indian cashback and contain uid
		if (originalUrl?.includes('indiancashback.com') && originalUrl?.includes('uid=')) {
			// Extract the UID from the URL
			const urlObj = new URL(originalUrl)
			const uid = urlObj.searchParams.get('uid')

			if (uid) {
				// If UID is present, get data from db
				const existingLink = await this.offer.findOne({ uid: parseInt(uid, 10) })
				if (existingLink?.link) {
					originalUrl = existingLink.link
				}
			}
		}

		if (['amazon.in', 'amazon.com'].some(domain => originalUrl.includes(domain))) {
			throw new BadRequestException('Amazon links are not supported')
		}
		let url: string | null = null
		if (
			[
				'flipkart.com',
				'croma.com',
				'ajio.com',
				'myntra.com',
				'shopsy.in',
				'tatacliq.com',
				'indiancashback.com',
			].some(domain => originalUrl.includes(domain))
		) {
			url = originalUrl
		} else {
			url = await this.expandUrl(originalUrl)
		}

		if (url?.includes('linkredirect.in')) {
			const urlObj = new URL(url)
			url = decodeURIComponent(urlObj.searchParams.get('dl') || '')
		}
		if (!url) {
			throw new BadRequestException('Invalid URL')
		}

		// Detect store from URL
		const { storeId, storeType } = await this.detectStoreFromUrl(url)

		// remove utm tags if any
		const urlObj = new URL(url)
		for (const key of urlObj.searchParams.keys()) {
			if (key.startsWith('utm_')) {
				urlObj.searchParams.delete(key)
			}
		}

		// Remove affiliate parameters for all stores
		urlObj.searchParams.delete('affid')
		urlObj.searchParams.delete('tag')
		for (const key of urlObj.searchParams.keys()) {
			if (key.startsWith('affExtParam')) {
				urlObj.searchParams.delete(key)
			}
		}
		url = urlObj.toString()

		// Get store and user documents
		const store = await this.store.findById(storeId)
		if (!store) {
			throw new NotFoundException('Store not found')
		}

		const userDoc = await this.user.findOne({ email: user.email })
		if (!userDoc) {
			throw new NotFoundException('User not found')
		}

		// Get the store's affiliation type
		const affiliationType = store.affiliation
			? store.affiliation.toString()
			: storeType === 'flipkart'
				? 'flipkart'
				: 'admitad' // Default to admitad for other stores

		// Generate affiliate URL using the store's affiliation type
		const generatedUrl = generateAffiliateUrlByType(url, affiliationType, store.affiliateLink)

		// Create and save the link
		const newLink = new this.link({
			originalUrl,
			generatedUrl,
			user: userDoc._id,
			store: store._id,
			affiliation: store.affiliation,
			active: true,
			uid: 0,
			shortUrl: Math.random().toString(36).substring(2, 15), // Generate a random short URL
		})

		await newLink.save()

		// Create a short URL for the generated link
		const response = await fetch(
			`https://icashbk.in/yourls-api.php?signature=4b64181bf6&action=shorturl&format=json&url=${encodeURIComponent('https://api-main.indiancashback.com/links/') + newLink.linkId}`,
			{
				method: 'GET',
			}
		)

		if (!response.ok) {
			throw new Error('Failed to create short URL')
		}
		const data = await response.json()

		newLink.shortUrl = data.shorturl
		await newLink.save()

		// Return response
		return {
			linkId: newLink.linkId,
			originalUrl: newLink.originalUrl,
			generatedUrl: newLink.generatedUrl,
			storeName: store.name,
			createdAt: newLink.createdAt.toISOString(),
			shortUrl: data.shorturl,
		}
	}

	/**
	 * Get all links for a user
	 * @param user The user
	 * @returns List of links and total count
	 */
	async getUserLinks(
		userId: Types.ObjectId,
		queryParams: UserLinksQueryDto
	): Promise<PaginationResponseDto<UserLinkResponseDto>> {
		const { storeId, startDate, endDate, page = 1, limit = 10 } = queryParams

		// Build base query for links
		const query: any = { user: userId }

		// Add store filter if provided
		if (storeId) {
			query.store = new Types.ObjectId(storeId)
		}

		// Add date filter if provided
		if (startDate || endDate) {
			query.createdAt = {}
			if (startDate) {
				// Set start date to beginning of the day
				const startOfDay = new Date(startDate)
				startOfDay.setHours(0, 0, 0, 0)
				query.createdAt.$gte = startOfDay
			}
			if (endDate) {
				// Set end date to end of the day
				const endOfDay = new Date(endDate)
				endOfDay.setHours(23, 59, 59, 999)
				query.createdAt.$lte = endOfDay
			}
		}

		// Count total documents for pagination
		const total = await this.link.countDocuments(query)

		// Calculate pages
		const pages = Math.ceil(total / limit)
		const skip = (page - 1) * limit

		// Fetch links
		const links = await this.link
			.find(query)
			.populate('store', 'name')
			.sort({ createdAt: -1 })
			.skip(skip)
			.limit(limit)
			.lean()

		// Prepare response data
		const items = await Promise.all(
			links.map(async link => {
				// Get total clicks for this link
				const totalClicks = await this.click.countDocuments({
					link: link._id,
				})

				// Get converted clicks (tracked or confirmed status)
				const convertedClicks = await this.click.countDocuments({
					link: link._id,
					status: { $in: ['tracked', 'confirmed'] },
				})

				// Get earnings data from this link's clicks
				const clickIds = await this.click.find({ link: link._id }).distinct('_id')

				// Calculate total cashback earned
				const totalCashbackEarned = await this.earningModel
					.aggregate([
						{
							$match: {
								click: { $in: clickIds },
								status: { $in: ['tracked_for_confirm', 'confirmed'] },
							},
						},
						{
							$group: {
								_id: null,
								total: { $sum: '$cashbackAmount' },
							},
						},
					])
					.then(result => (result.length > 0 ? result[0].total : 0))

				return {
					linkId: link.linkId,
					shortUrl: link.shortUrl,
					storeName: link.store?.name || 'Unknown Store',
					totalClicks,
					convertedClicks,
					totalCashbackEarned,
					createdAt: link.createdAt,
				}
			})
		)

		return {
			items,
			total,
			page,
			pages,
			limit,
		}
	}

	async getLinkDetails(linkId: string, clientInfo: ClientInfoData): Promise<string> {
		const processUrl = (url: string, operations: string[][]): string => {
			let currentUrl = url
			for (const [substring, paramToAppend] of operations) {
				if (substring && currentUrl.includes(substring)) {
					const separator = currentUrl.includes('?') ? '&' : '?'
					currentUrl = `${currentUrl}${separator}${paramToAppend}`
				}
			}
			return currentUrl
		}

		const link = await this.link.findOne({ linkId })
		if (!link) {
			throw new NotFoundException('Link not found')
		}
		//generate click
		const click: Partial<Click> = {
			user: link.user,
			userCity: clientInfo.geo?.city,
			userIp: clientInfo.ip,
			device: clientInfo.device,
			type: 'express',
			link,
		}
		click.store = link.store
		const store = await this.store.findById(link.store).populate('affiliation')
		if (!store) {
			throw new NotFoundException('Store not found')
		}

		click.title = store.name
		click.url = store.logo.secureUrl
		click.affiliation = store.affiliation
		const newClick = new this.click({ ...click, uid: 1, referenceId: '1' })
		await newClick.save()
		const params = [
			`UID=${newClick.referenceId}`,
			`s1=${newClick.referenceId}`,
			`subid1=${newClick.referenceId}`,
			`subid=${newClick.referenceId}`,
			`sid=${newClick.referenceId}`,
			`aff_sub=${newClick.referenceId}`,
			`subTrackId=${newClick.referenceId}`,
			`p1=${newClick.referenceId}`,
			`source=${newClick.referenceId}`,
		].join('&')

		const redirectUrl = link.generatedUrl

		const processedUrl = processUrl(redirectUrl || '', operations(newClick.referenceId))

		newClick.offerUrl =
			processedUrl === redirectUrl
				? `${redirectUrl}${redirectUrl.includes('?') ? '&' : '?'}${params}`
				: processedUrl

		await newClick.save()

		return newClick.offerUrl
	}

	async getUserAnalytics(
		userId: Types.ObjectId,
		queryParams: UserAnalyticsQueryDto
	): Promise<UserAnalyticsResponseDto> {
		const { storeId, startDate, endDate, periodType = PeriodType.YEAR } = queryParams

		// Set default dates if not provided
		const end = endDate || new Date()
		const start = startDate || this.getDefaultStartDate(end, periodType)

		// Ensure proper date boundaries
		const normalizedStart = new Date(start)
		normalizedStart.setHours(0, 0, 0, 0)

		const normalizedEnd = new Date(end)
		normalizedEnd.setHours(23, 59, 59, 999)

		// Calculate previous period dates
		const previousPeriodDates = this.getPreviousPeriodDates(
			normalizedStart,
			normalizedEnd,
			periodType
		)

		// Get current period analytics
		const currentPeriodAnalytics = await this.getPeriodAnalytics(
			userId,
			storeId,
			normalizedStart,
			normalizedEnd
		)

		// Get previous period analytics
		const previousPeriodAnalytics = await this.getPeriodAnalytics(
			userId,
			storeId,
			previousPeriodDates.start,
			previousPeriodDates.end
		)

		// Calculate percentage changes
		const totalCashbackEarned = this.calculateMetricWithChange(
			currentPeriodAnalytics.totalCashbackEarned,
			previousPeriodAnalytics.totalCashbackEarned
		)

		const conversionRate = this.calculateMetricWithChange(
			currentPeriodAnalytics.conversionRate,
			previousPeriodAnalytics.conversionRate
		)

		const totalClicks = this.calculateMetricWithChange(
			currentPeriodAnalytics.totalClicks,
			previousPeriodAnalytics.totalClicks
		)

		return {
			totalCashbackEarned,
			conversionRate,
			totalClicks,
		}
	}

	private getDefaultStartDate(endDate: Date, periodType: PeriodType): Date {
		const end = moment(endDate)
		// biome-ignore lint/suspicious/noImplicitAnyLet: required for external API
		let start

		switch (periodType) {
			case PeriodType.DAY:
				start = end.clone().subtract(1, 'day')
				break
			case PeriodType.WEEK:
				start = end.clone().subtract(7, 'days')
				break
			case PeriodType.YEAR:
				start = end.clone().subtract(1, 'year')
				break
			// biome-ignore lint/complexity/noUselessSwitchCase: required for external API
			case PeriodType.MONTH:
			default:
				start = end.clone().subtract(1, 'month')
				break
		}

		return start.toDate()
	}

	private getPreviousPeriodDates(
		start: Date,
		end: Date,
		_periodType: PeriodType
	): { start: Date; end: Date } {
		const currentStart = moment(start)
		const currentEnd = moment(end)
		const duration = moment.duration(currentEnd.diff(currentStart))
		const durationDays = duration.asDays()

		const previousEnd = currentStart.clone().subtract(1, 'day').endOf('day')
		const previousStart = previousEnd.clone().subtract(durationDays, 'days').startOf('day')

		return {
			start: previousStart.toDate(),
			end: previousEnd.toDate(),
		}
	}

	private async getPeriodAnalytics(
		userId: Types.ObjectId,
		storeId: string | undefined,
		startDate: Date,
		endDate: Date
	): Promise<{
		totalCashbackEarned: number
		conversionRate: number
		totalClicks: number
	}> {
		// Build base query for user's links
		const linkQuery: any = {
			user: userId,
			createdAt: {
				$gte: startDate,
				$lte: endDate,
			},
		}

		if (storeId) {
			linkQuery.store = new Types.ObjectId(storeId)
		}

		// Get all links for this user in the period
		const links = await this.link.find(linkQuery).lean().select('_id')

		const linkIds = links.map(link => link._id)

		// Build click query
		const clickQuery: any = {
			link: { $in: linkIds },
			createdAt: {
				$gte: startDate,
				$lte: endDate,
			},
		}

		// Get total clicks
		const totalClicks = await this.click.countDocuments(clickQuery)

		// Get converted clicks (tracked or confirmed)
		const convertedClicks = await this.click.countDocuments({
			...clickQuery,
			status: { $in: ['tracked', 'confirmed'] },
		})

		// Calculate conversion rate
		const conversionRate = totalClicks > 0 ? (convertedClicks / totalClicks) * 100 : 0

		// Get click IDs to find earnings
		const clickIds = await this.click
			.find(clickQuery)
			.lean()
			.select('_id')
			.then(clicks => clicks.map(click => click._id))

		// Get total cashback earned
		const earningsResult = await this.earningModel.aggregate([
			{
				$match: {
					click: { $in: clickIds },
					status: { $in: ['tracked_for_confirm', 'confirmed'] },
					createdAt: {
						$gte: startDate,
						$lte: endDate,
					},
				},
			},
			{
				$group: {
					_id: null,
					total: { $sum: '$cashbackAmount' },
				},
			},
		])

		const totalCashbackEarned = earningsResult.length > 0 ? earningsResult[0].total : 0

		return {
			totalCashbackEarned,
			conversionRate,
			totalClicks,
		}
	}

	private calculateMetricWithChange(
		currentValue: number,
		previousValue: number
	): MetricWithChangeDto {
		const percentageChange =
			previousValue !== 0
				? ((currentValue - previousValue) / previousValue) * 100
				: currentValue > 0
					? 100
					: 0

		return {
			value: currentValue,
			percentageChange: Number.parseFloat(percentageChange.toFixed(2)),
		}
	}
}
