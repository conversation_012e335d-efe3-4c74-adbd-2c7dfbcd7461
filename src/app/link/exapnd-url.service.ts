import { HttpService } from '@nestjs/axios'
import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common'
import * as puppeteer from 'puppeteer'
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer'
import { firstValueFrom, timeout } from 'rxjs'

@Injectable()
export class UrlExpanderService implements OnModuleDestroy {
	private readonly logger = new Logger(UrlExpanderService.name)
	private browser: Browser | null = null
	private browserInitPromise: Promise<Browser> | null = null

	constructor(private readonly httpService: HttpService) {}

	async onModuleDestroy() {
		if (this.browser) {
			await this.browser.close()
		}
	}

	private async getBrowser(): Promise<Browser> {
		if (this.browser?.connected) {
			return this.browser
		}

		if (this.browserInitPromise) {
			return this.browserInitPromise
		}

		this.browserInitPromise = puppeteer.launch({
			headless: true,
			args: [
				'--no-sandbox',
				'--disable-setuid-sandbox',
				'--disable-dev-shm-usage',
				'--disable-gpu',
				'--no-first-run',
				'--no-default-browser-check',
				'--disable-default-apps',
				'--disable-extensions',
				'--disable-background-timer-throttling',
				'--disable-renderer-backgrounding',
				'--disable-backgrounding-occluded-windows',
			],
		})

		this.browser = await this.browserInitPromise
		this.browserInitPromise = null
		return this.browser
	}

	// Method for Flipkart and similar JavaScript-heavy URLs
	private async expandUrlWithPuppeteer(shortUrl: string): Promise<string | null> {
		let page: Page | null = null

		try {
			const browser = await this.getBrowser()
			page = await browser.newPage()

			// Set realistic browser headers
			await page.setUserAgent(
				'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
			)

			await page.setViewport({ width: 1920, height: 1080 })

			// Set extra headers
			await page.setExtraHTTPHeaders({
				Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
				'Accept-Language': 'en-US,en;q=0.5',
				'Accept-Encoding': 'gzip, deflate, br',
				DNT: '1',
				Connection: 'keep-alive',
				'Upgrade-Insecure-Requests': '1',
			})

			// Block unnecessary resources to speed up loading
			await page.setRequestInterception(true)
			page.on('request', req => {
				const resourceType = req.resourceType()
				if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
					req.abort()
				} else {
					req.continue()
				}
			})

			// Navigate and wait for network to be mostly idle
			const _response = await page.goto(shortUrl, {
				waitUntil: 'networkidle2',
				timeout: 30000,
			})

			// Wait a bit more for any JavaScript redirects
			await page.evaluate(() => new Promise(resolve => setTimeout(resolve, 2000)))

			const finalUrl = page.url()

			// Check if we actually got redirected
			if (finalUrl && finalUrl !== shortUrl) {
				this.logger.log(`Puppeteer successfully expanded: ${shortUrl} -> ${finalUrl}`)
				return finalUrl
			}

			// If no redirect, try to find redirect links or meta refresh
			const redirectUrl = await page.evaluate(() => {
				// Check for meta refresh
				const metaRefresh = document.querySelector('meta[http-equiv="refresh"]')
				if (metaRefresh) {
					const content = metaRefresh.getAttribute('content')
					const urlMatch = content?.match(/url=(.+)/i)
					if (urlMatch) return urlMatch[1]
				}

				// Check for JavaScript redirects
				const scripts = Array.from(document.querySelectorAll('script'))
				for (const script of scripts) {
					const text = script.textContent || ''
					const locationMatch = text.match(/location\.href\s*=\s*['"](.*?)['"]/)
					if (locationMatch) return locationMatch[1]

					const replaceMatch = text.match(/location\.replace\s*\(\s*['"](.*?)['"]\s*\)/)
					if (replaceMatch) return replaceMatch[1]
				}

				return null
			})

			if (redirectUrl) {
				this.logger.log(`Found redirect URL in page content: ${redirectUrl}`)
				return new URL(redirectUrl, finalUrl).href
			}

			return finalUrl !== shortUrl ? finalUrl : null
		} catch (error) {
			this.logger.error(`Puppeteer failed for ${shortUrl}:`, error.message)
			return null
		} finally {
			if (page) {
				await page.close()
			}
		}
	}

	// Check if URL needs browser-based expansion
	private needsBrowserExpansion(url: string): boolean {
		const browserRequiredDomains = [
			'dl.flipkart.com',

			// Add more domains as needed
		]

		return browserRequiredDomains.some(domain => url.toLowerCase().includes(domain.toLowerCase()))
	}

	// Method 1: Improved fetch with multiple strategies
	private async expandUrlWithFetch(shortUrl: string): Promise<string | null> {
		const strategies = [
			// Strategy 1: HEAD request (fastest)
			async () => {
				const response = await fetch(shortUrl, {
					method: 'HEAD',
					redirect: 'follow',
					headers: {
						'User-Agent':
							'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
					},
					signal: AbortSignal.timeout(10000), // 10 second timeout
				})
				return response.url
			},

			// Strategy 2: GET request with early abort
			async () => {
				const controller = new AbortController()
				const timeoutId = setTimeout(() => controller.abort(), 10000)

				try {
					const response = await fetch(shortUrl, {
						method: 'GET',
						redirect: 'follow',
						headers: {
							'User-Agent':
								'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
							Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
						},
						signal: controller.signal,
					})
					clearTimeout(timeoutId)
					return response.url
				} finally {
					clearTimeout(timeoutId)
				}
			},
		]

		for (const strategy of strategies) {
			try {
				const result = await strategy()
				if (result && result !== shortUrl) {
					return result
				}
			} catch (error) {
				this.logger.warn(`Strategy failed for ${shortUrl}:`, error.message)
			}
		}

		return null
	}

	// Method 2: Using Axios with better redirect handling
	private async expandUrlWithAxios(shortUrl: string): Promise<string | null> {
		try {
			const response = await firstValueFrom(
				this.httpService
					.head(shortUrl, {
						maxRedirects: 10,
						timeout: 10000,
						headers: {
							'User-Agent':
								'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
						},
						validateStatus: status => status < 400, // Accept redirects and success
					})
					.pipe(timeout(10000))
			)

			return response.request.res.responseUrl || response.config.url
		} catch (_error) {
			this.logger.warn(`Axios HEAD failed for ${shortUrl}, trying GET...`)

			try {
				const response = await firstValueFrom(
					this.httpService
						.get(shortUrl, {
							maxRedirects: 10,
							timeout: 10000,
							responseType: 'stream', // Don't download the full content
							headers: {
								'User-Agent':
									'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
							},
						})
						.pipe(timeout(10000))
				)

				// Close the stream immediately
				response.data.destroy()
				return response.request.res.responseUrl || response.config.url
			} catch (getError) {
				this.logger.error(`Both HEAD and GET failed for ${shortUrl}:`, getError.message)
				return null
			}
		}
	}

	// Method 3: Manual redirect following (most reliable)
	private async expandUrlManually(shortUrl: string, maxRedirects = 10): Promise<string | null> {
		let currentUrl = shortUrl
		let redirectCount = 0

		while (redirectCount < maxRedirects) {
			try {
				const response = await fetch(currentUrl, {
					method: 'HEAD',
					redirect: 'manual', // Handle redirects manually
					headers: {
						'User-Agent':
							'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
					},
					signal: AbortSignal.timeout(10000),
				})

				// Check if it's a redirect
				if (response.status >= 300 && response.status < 400) {
					const location = response.headers.get('location')
					if (!location) {
						this.logger.warn(`Redirect response but no location header for ${currentUrl}`)
						break
					}

					// Handle relative URLs
					currentUrl = new URL(location, currentUrl).href
					redirectCount++
					this.logger.debug(`Redirect ${redirectCount}: ${currentUrl}`)
					continue
				}

				// Not a redirect, we have the final URL
				return currentUrl
			} catch (error) {
				this.logger.error(`Error during manual expansion for ${currentUrl}:`, error.message)
				break
			}
		}

		if (redirectCount >= maxRedirects) {
			this.logger.warn(`Too many redirects for ${shortUrl}`)
		}

		return currentUrl !== shortUrl ? currentUrl : null
	}

	// Method 4: Using a third-party service (fallback)
	private async expandUrlWithService(shortUrl: string): Promise<string | null> {
		try {
			// Using unshorten.me API (free tier available)
			const response = await fetch(`https://unshorten.me/json/${encodeURIComponent(shortUrl)}`, {
				method: 'GET',
				headers: {
					Accept: 'application/json',
				},
				signal: AbortSignal.timeout(15000),
			})

			const data = await response.json()
			return data.resolved_url || data.url || null
		} catch (error) {
			this.logger.error(`Third-party service failed for ${shortUrl}:`, error.message)
			return null
		}
	}

	// Main public method with fallback strategies
	public async expandUrl(shortUrl: string): Promise<string | null> {
		// Validate URL first
		try {
			new URL(shortUrl)
		} catch {
			this.logger.error(`Invalid URL: ${shortUrl}`)
			return null
		}

		// Check if this URL needs browser-based expansion
		// if (this.needsBrowserExpansion(shortUrl)) {
		this.logger.log(`Using browser expansion for ${shortUrl}`)

		const browserMethods = [{ name: 'Puppeteer', fn: () => this.expandUrlWithPuppeteer(shortUrl) }]

		for (const method of browserMethods) {
			try {
				const result = await method.fn()
				// if (result && result !== shortUrl) {
				// 	this.logger.log(`Successfully expanded ${shortUrl} to ${result} using ${method.name}`)
				return result
				// }
			} catch (error) {
				this.logger.warn(`${method.name} method failed for ${shortUrl}:`, error.message)
			}
		}

		// // If browser methods fail, fall back to regular methods
		// this.logger.warn(`Browser methods failed for ${shortUrl}, trying regular methods...`)
		// // }

		// // Regular expansion methods for non-browser-required URLs
		// const methods = [
		// 	{ name: 'Manual', fn: () => this.expandUrlManually(shortUrl) },
		// 	{ name: 'Axios', fn: () => this.expandUrlWithAxios(shortUrl) },
		// 	{ name: 'Fetch', fn: () => this.expandUrlWithFetch(shortUrl) },
		// 	{ name: 'Service', fn: () => this.expandUrlWithService(shortUrl) },
		// ]

		// for (const method of methods) {
		// 	try {
		// 		this.logger.debug(`Trying ${method.name} method for ${shortUrl}`)
		// 		const result = await method.fn()

		// 		if (result && result !== shortUrl) {
		// 			this.logger.log(`Successfully expanded ${shortUrl} to ${result} using ${method.name}`)
		// 			return result
		// 		}
		// 	} catch (error) {
		// 		this.logger.warn(`${method.name} method failed for ${shortUrl}:`, error.message)
		// 	}
		// }

		// this.logger.error(`All methods failed to expand ${shortUrl}`)
		return null
	}

	// Utility method to expand multiple URLs concurrently
	public async expandUrls(shortUrls: string[]): Promise<Map<string, string | null>> {
		const results = new Map<string, string | null>()

		const promises = shortUrls.map(async url => {
			const expanded = await this.expandUrl(url)
			results.set(url, expanded)
			return { original: url, expanded }
		})

		await Promise.allSettled(promises)
		return results
	}
}
