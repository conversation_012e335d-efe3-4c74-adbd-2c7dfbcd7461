import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsEnum, IsNotEmpty, IsNumber, IsOptional } from 'class-validator'

export class UpdateCredentialsDto {
	@ApiProperty({ example: '<EMAIL>', type: String, required: false })
	@IsEmail()
	@IsOptional()
	email?: string

	@ApiProperty({
		example: 1_234_567_890,
		description: 'Mobile Number',
		type: Number,
		required: false,
	})
	@IsNumber()
	@IsOptional()
	mobile?: number

	@ApiProperty({
		example: 'email',
		enum: ['mobile', 'email'],
		type: String,
		description: 'Please add the type mobile or email',
	})
	@IsEnum(['mobile', 'email'])
	@IsNotEmpty()
	type!: 'mobile' | 'email'
}
