import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'

export class UpdateBankAccountDto {
	@ApiProperty({
		example: '<PERSON>',
		description: 'Full name as per the bank account holder',
		required: true,
	})
	@IsString()
	@IsNotEmpty()
	holderName!: string

	@ApiProperty({
		example: 'HDFC',
		description: 'Bank Name',
		required: true,
	})
	@IsString()
	@IsNotEmpty()
	bankName!: string

	@ApiProperty({
		example: 'Hdfc@0koramangala',
		description: 'Bank IFSC code',
		required: true,
	})
	@IsString()
	@IsNotEmpty()
	ifsc!: string

	@ApiProperty({
		example: 'HDFC Bank, Koramangala',
		description: 'Branch Name',
	})
	@IsString()
	@IsNotEmpty()
	branchName!: number

	@ApiProperty({ example: '************** ', description: 'Account Number' })
	@IsString()
	@IsNotEmpty()
	accountNumber!: string

	@ApiProperty({
		example: '560068',
		description: 'Postcode/Zipcode of the bank account holder',
	})
	@IsString()
	@IsNotEmpty()
	postcode!: number

	@ApiProperty({
		example: `2393 15B Cross Road
	 Building Sector 1
	 Bengaluru
	 Bangalore South
	 Karnataka
	 India `,
		description: 'Address of the bank account holder',
	})
	@IsString()
	@IsNotEmpty()
	address!: string
}
