import { ApiProperty } from '@nestjs/swagger'

export class GetReferralLeaderboardResponse {
	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: Number })
	referralCount: number

	@ApiProperty({ type: Number })
	totalReferralCommission: number
}

export class GetReferralLeaderboardDto {
	@ApiProperty({ type: String, enum: ['weekly', 'monthly', 'all'] })
	timeFrame: 'weekly' | 'monthly' | 'all'
}
