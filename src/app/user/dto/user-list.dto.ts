import { ApiProperty } from '@nestjs/swagger'
import { StatusType } from 'shared/types'

export class UserListItemDto {
	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	email: string

	@ApiProperty({ type: Number, required: false })
	mobile?: number

	@ApiProperty({ type: String })
	referralCode: string

	@ApiProperty({ type: String, required: false })
	userNotes?: string

	@ApiProperty({ type: Boolean })
	mobileVerified: boolean

	@ApiProperty({ type: String })
	status: StatusType

	@ApiProperty({ type: Boolean })
	migrated: boolean

	@ApiProperty({ type: Date, required: false })
	lastLoggedDate?: Date

	@ApiProperty({ type: String, required: false })
	referredByEmail?: string
}

export class UserListResponseDto {
	@ApiProperty({ type: [UserListItemDto] })
	users: UserListItemDto[]
}
