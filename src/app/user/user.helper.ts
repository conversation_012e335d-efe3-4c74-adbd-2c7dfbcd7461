import { Types } from 'mongoose'
import { buildUserHistorySortQuery } from 'shared/helpers/query-builder'
import { GetUserHistoryDto } from './dto/user.dto'

export function buildUserHistoryAggregateQuery(
	queryParams: GetUserHistoryDto,
	user?: Types.ObjectId
) {
	const page = queryParams.page ?? 1
	const _skip = (page - 1) * queryParams.pageSize

	const _query = { name: { $regex: new RegExp(queryParams.searchParam, 'i') } }
	const _sortQuery = buildUserHistorySortQuery(
		queryParams.sortType,
		'createdAt',
		'createdAt'
		// 'commission',
	)

	const aggregationPipeline = []

	// Match stage before Offer lookup
	aggregationPipeline.push({
		$match: {
			referral: user,
		},
	})

	// Add a lookup stage to fetch earnings for the user
	aggregationPipeline.push({
		$lookup: {
			from: 'earnings', // Assuming the collection name is 'earnings'
			localField: '_id',
			foreignField: 'user',
			as: 'earnings',
		},
	})

	// Add a project stage to rename the earnings field if necessary
	aggregationPipeline.push({
		$project: {
			_id: 1, // Include other fields as needed
			name: 1,
			email: 1,
			avatar: 1,
			status: 1,
			createdAt: 1,
			earnings: 1, // Rename the earnings field if necessary
		},
	})

	return aggregationPipeline
}

export function convertIstToUtc(istDateTime: string): string {
	// Parse the IST datetime string to a Date object
	const istDate = new Date(istDateTime)

	// IST is 5 hours and 30 minutes ahead of UTC, so we subtract that to get UTC
	const utcDate = new Date(istDate.getTime() - (5 * 60 * 60 * 1000 + 30 * 60 * 1000))

	// Format the UTC date to the desired string format
	return utcDate.toISOString()
}
