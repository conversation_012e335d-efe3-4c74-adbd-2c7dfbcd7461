import { ApiProperty } from '@nestjs/swagger'

export class GetBankAccountDataResponse {
	@ApiProperty({ type: String })
	holderName: string

	@ApiProperty({ type: String })
	bankName: string

	@ApiProperty({ type: String })
	branchName: string

	@ApiProperty({ type: String })
	postcode: string

	@ApiProperty({ type: String })
	accountNumber: string

	@ApiProperty({ type: String })
	ifsc: string

	@ApiProperty({ type: String })
	upi: string

	@ApiProperty({ type: String })
	address: string
}
