import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsEnum } from 'class-validator'
import { PaginationResponseType } from 'shared/dto'
import { Status, Status2, StatusType } from 'shared/types'

export class GetUserReferralCodeResponse {
	@ApiProperty({ type: String })
	referralCode: string
}

export class UserData {
	@ApiProperty({ type: Number })
	totalOrders: number

	@ApiProperty({ type: Number })
	uid: number

	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	joined: string

	@ApiProperty({ type: String })
	avatar: string

	@IsEnum(Status)
	@ApiProperty({
		enum: Status,
	})
	status?: StatusType

	@ApiProperty({ type: Number })
	totalPendingReferralCommission: number

	@ApiProperty({ type: Number })
	totalConfirmedReferralCommission: number
}

export class GetUsersByReferralCodeResponse {
	@ApiProperty({ type: [UserData] })
	users: UserData[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}

export class CbItem {
	@ApiProperty({ type: Number })
	orderAmount: number

	@ApiProperty({ type: Number })
	cashbackAmount: number

	@ApiProperty({ type: String })
	storeLogo: string

	@ApiPropertyOptional({ type: String })
	storeBgColor: string

	@ApiProperty({ type: String })
	referenceId: string

	@ApiProperty({ type: String })
	orderDate: string

	@ApiProperty({ type: String })
	approxConfirmDate: string

	@ApiProperty({ type: String })
	remarks: string

	@IsEnum(Status2)
	@ApiProperty({
		enum: Status2,
	})
	status?: 'confirmed' | 'pending' | 'cancelled'
}

export class GetCbHistoryResponse {
	@ApiProperty({ type: [CbItem] })
	cbItems: CbItem[]

	@ApiProperty({ type: PaginationResponseType })
	pagination: PaginationResponseType
}
