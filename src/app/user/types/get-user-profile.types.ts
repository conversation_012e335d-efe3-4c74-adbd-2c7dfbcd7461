import { ApiProperty } from '@nestjs/swagger'
import type { Types } from 'mongoose'

export class GetUserProfileTypes {
	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	email: string

	@ApiProperty({ type: Number })
	mobile?: number
}

export class PersonalInterestTypes {
	@ApiProperty({ type: String })
	id: Types.ObjectId

	@ApiProperty({ type: String })
	name: string
}

export class GetUserProfileResponseItem {
	@ApiProperty({ type: String })
	name: string

	@ApiProperty({ type: String })
	email: string

	@ApiProperty({ type: String, required: false })
	avatar?: string

	@ApiProperty({ type: Number, required: false })
	mobile?: number | null | undefined

	@ApiProperty({ type: Number })
	balance: number

	@ApiProperty({ type: Number })
	pendingCount: number

	@ApiProperty({ type: Number })
	confirmedCount: number

	@ApiProperty({ type: Number })
	cancelledCount: number

	@ApiProperty({ type: String })
	referralCode: string

	@ApiProperty({ type: [PersonalInterestTypes] })
	personalInterest: PersonalInterestTypes[]

	@ApiProperty({ type: Boolean })
	sendNotification: boolean
}
