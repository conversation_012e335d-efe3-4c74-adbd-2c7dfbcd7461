import { Controller, Get, Query } from '@nestjs/common'
import { ApiResponse, ApiTags } from '@nestjs/swagger'
import { GetReferralLeaderboardDto, GetReferralLeaderboardResponse } from './dto/leader-board'
import { ReferralCommissionService } from './referral-commission.service'
import { UserService } from './user.service'
import { UserEarningService } from './user-earning.service'

@ApiTags('ReferralCampaign')
@Controller('campaign')
export class ReferralCampaignController {
	constructor(
		private readonly userService: UserService,
		private readonly userEarningService: UserEarningService,
		private readonly referralCommissionService: ReferralCommissionService
	) {}

	// referral earnings leaderboard to 7 users with there referral code and referral earnings in weekly basis
	// monthly basis and all time basis

	@ApiResponse({
		type: [GetReferralLeaderboardResponse],
	})
	@Get('referral-earnings-leaderboard')
	async getReferralEarningsLeaderboard(
		@Query() queryParams: GetReferralLeaderboardDto
	): Promise<GetReferralLeaderboardResponse[]> {
		return await this.referralCommissionService.getReferralEarningsLeaderboard(
			queryParams.timeFrame
		)
	}
}
