import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { hash } from 'ohash'
import { Earning, EarningDocument } from 'shared/entities/earning.entity'
import { User, UserDocument } from 'shared/entities/user.entity'
import { GetReferralLeaderboardResponse } from './dto/leader-board'

@Injectable()
export class ReferralCommissionService {
	private readonly logger = new Logger(ReferralCommissionService.name)

	constructor(
		@InjectModel(User.name)
		private user: Model<UserDocument>,

		@InjectModel(Earning.name)
		private earning: Model<EarningDocument>
	) {}

	/**
	 * Checks if the campaign period is active
	 * @returns boolean indicating if the signup bonuses should be applied
	 */
	isCampaignActive(): boolean {
		// Define campaign start and end dates
		const campaignStart = new Date('2025-06-01T00:00:00Z') // Example: June 1, 2024
		const campaignEnd = new Date('2025-10-30T23:59:59Z') // Example: June 30, 2024

		const now = new Date()

		return now >= campaignStart && now <= campaignEnd
	}

	async createJoinEarning({
		userId,
		referralEarnings,
		newUserId,
		newUserEarnings,
	}: {
		userId: Types.ObjectId | null | undefined
		referralEarnings: number | null | undefined
		newUserId: Types.ObjectId
		newUserEarnings: number
	}): Promise<void> {
		try {
			this.logger.log('Creating join earnings', {
				userId: userId?.toString(),
				referralEarnings,
				newUserId: newUserId.toString(),
				newUserEarnings,
				isCampaignActive: this.isCampaignActive(),
			})

			if (userId && referralEarnings && referralEarnings > 0 && this.isCampaignActive()) {
				this.logger.debug('Creating earning record for referrer', {
					userId: userId.toString(),
					referralEarnings,
					newUserId: newUserId.toString(),
				})

				// Create earning record for referrer
				const userEarning = new this.earning({
					uid: 0,
					user: new Types.ObjectId(userId),
					referralUser: new Types.ObjectId(newUserId),
					cashbackAmount: referralEarnings,
					earningsType: 'referral',
					status: 'pending',
					notes: 'Referral commission for new user signup bonus',
					referenceId: `CBERN${hash({
						user: userId,
						referralUser: newUserId,
						referralEarnings,
					}).toUpperCase()}`,
				})
				await userEarning.save()

				const user = (await this.user.findById(userId)) as UserDocument
				user.pendingBalance += referralEarnings
				await user.save()

				this.logger.debug('Referrer earning record created successfully', {
					userId: userId.toString(),
					earningId: userEarning._id.toString(),
					referralEarnings,
				})
			}

			if (newUserEarnings > 0 && this.isCampaignActive()) {
				this.logger.debug('Creating earning record for new user', {
					newUserId: newUserId.toString(),
					newUserEarnings,
				})

				// Create earning record for referred user
				const newUserEarning = new this.earning({
					uid: 0,
					user: new Types.ObjectId(newUserId),
					cashbackAmount: newUserEarnings,
					earningsType: 'referral',
					status: 'confirmed',
					notes: 'Signup bonus for joining',
					referenceId: `CBERN${hash({
						user: newUserId,
						referralEarnings: newUserEarnings,
					}).toUpperCase()}`,
				})
				await newUserEarning.save()

				const newUser = (await this.user.findById(newUserId)) as UserDocument
				newUser.balance += newUserEarnings
				await newUser.save()

				this.logger.debug('New user earning record created successfully', {
					newUserId: newUserId.toString(),
					earningId: newUserEarning._id.toString(),
					newUserEarnings,
				})
			}

			this.logger.log('Join earnings created successfully', {
				userId: userId?.toString(),
				newUserId: newUserId.toString(),
			})
		} catch (error) {
			this.logger.error('Failed to create join earnings', {
				userId: userId?.toString(),
				referralEarnings,
				newUserId: newUserId.toString(),
				newUserEarnings,
				error: error.message,
				stack: error.stack,
			})
			throw error
		}
	}

	async getReferralEarningsLeaderboard(
		timeFrame: 'weekly' | 'monthly' | 'all'
	): Promise<GetReferralLeaderboardResponse[]> {
		try {
			this.logger.log('Fetching referral earnings leaderboard', {
				timeFrame,
			})

			// Define the date range based on the timeFrame
			const now = new Date()
			let startDate: Date

			// Set the start date based on the time frame
			if (timeFrame === 'weekly') {
				// Set to the beginning of the current week (Sunday)
				startDate = new Date(now)
				startDate.setDate(now.getDate() - now.getDay())
				startDate.setHours(0, 0, 0, 0)
			} else if (timeFrame === 'monthly') {
				// Set to the beginning of the current month
				startDate = new Date(now.getFullYear(), now.getMonth(), 1)
			} else {
				// 'all' or default: No date filtering for all-time
				startDate = new Date(0) // January 1, 1970
			}

			this.logger.debug('Date range calculated for leaderboard', {
				timeFrame,
				startDate: startDate.toISOString(),
				endDate: now.toISOString(),
			})

			const result = await this.earning.aggregate([
				{
					$match: {
						createdAt: { $gte: startDate },
						cashbackAmount: { $gt: 0 },
						earningsType: 'referral',
						referralUser: { $exists: true, $ne: null },
					},
				},
				{
					$group: {
						_id: '$user',
						totalReferralCommission: {
							$sum: '$cashbackAmount',
						},
						referralCount: { $sum: 1 },
					},
				},
				{
					// Sort by highest commission
					$sort: { totalReferralCommission: -1 },
				},
				{
					// Limit to top 7
					$limit: 7,
				},
				{
					// Lookup user details
					$lookup: {
						from: 'users',
						localField: '_id',
						foreignField: '_id',
						as: 'userDetails',
					},
				},
				{
					$unwind: '$userDetails',
				},
				{
					// Project final shape
					$project: {
						_id: 0,
						name: '$userDetails.name',
						totalReferralCommission: 1,
						referralCount: 1,
					},
				},
			])

			this.logger.log('Referral earnings leaderboard fetched successfully', {
				timeFrame,
				resultCount: result.length,
			})

			return result
		} catch (error) {
			this.logger.error('Failed to fetch referral earnings leaderboard', {
				timeFrame,
				error: error.message,
				stack: error.stack,
			})
			throw error
		}
	}
}
