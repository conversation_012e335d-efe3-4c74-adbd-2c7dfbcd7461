import fs from 'node:fs'
import path from 'node:path'
import {
	BadRequestException,
	Injectable,
	NotAcceptableException,
	NotFoundException,
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { PersonalInterestService } from 'app/context/personal-interest/personal-interest.service'
import { Model, Types } from 'mongoose'
import { hash } from 'ohash'
import { uploadImageToCloudinary } from 'shared/decorators'
import { Click, ClickDocument, Otp, type OtpDocument } from 'shared/entities'
import { CookieSession, CookieSessionDocument } from 'shared/entities/cookie-session.entity'
import { Earning, EarningDocument } from 'shared/entities/earning.entity'
import { OtpMailPayload, OtpSmsPayload, sendOtpMail, smsOtp } from 'shared/functions'
import { EmailNormalizationService } from 'shared/services/email-normalization.service'
import { Status } from 'shared/types'
import { type ReqUser, User, UserDocument } from '../../shared/entities/user.entity'
import { BankAccountDetailsService } from './bank-details.service'
import { UpdateCredentialsDto, UpdateProfileDto } from './dto'
import { GetUserCashbackDto, GetUserHistoryDto } from './dto/user.dto'
import { GetCbHistoryResponse, GetUsersByReferralCodeResponse } from './types/user.types'
import { UserCountService } from './user-count.service'
import { UserEarningService } from './user-earning.service'

@Injectable()
export class UserService {
	constructor(
		@InjectModel(User.name)
		private user: Model<UserDocument>,
		@InjectModel(Otp.name)
		private otp: Model<OtpDocument>,
		@InjectModel(Earning.name)
		private earning: Model<EarningDocument>,
		@InjectModel(Click.name)
		private click: Model<ClickDocument>,
		@InjectModel(CookieSession.name)
		private cookieSession: Model<CookieSessionDocument>,

		private personalInterestService: PersonalInterestService,
		private userCountService: UserCountService,
		private userEarningService: UserEarningService,
		private bankService: BankAccountDetailsService,
		private emailNormalizationService: EmailNormalizationService
	) {}
	async getUserByEmail(email?: string) {
		return email ? await this.emailNormalizationService.findUserByEmail(email) : null
	}

	async getUserByMobile(mobile?: string) {
		return mobile ? await this.user.findOne({ mobile }).exec() : null
	}

	async activateUser(
		email: string,
		payload: {
			mobileVerified?: boolean | undefined
			status?: 'active' | undefined
		}
	) {
		await this.user.updateOne({ email }, { $set: payload })
	}

	async getUserProfileById(id: Types.ObjectId) {
		const user = (await this.user.findById(id).populate('personalInterest').exec()) as UserDocument

		// Get earnings counts using centralized service
		const { pendingCount, confirmedCount, cancelledCount } =
			await this.userCountService.getUserEarningsCounts(id, user)

		return {
			name: user.name,
			email: user.email,
			avatar: user?.avatar?.secureUrl,
			mobile: user?.mobile,
			referralCode: user.referralCode,
			personalInterest: user.personalInterest?.map(i => {
				return {
					id: i._id,
					name: i.name,
				}
			}) as { id: Types.ObjectId; name: string }[],
			sendNotification: user.sendNotification,
			balance: user.balance + user.giftCardBalance,
			pendingCount,
			confirmedCount,
			cancelledCount,
		}
	}

	async updateProfile(body: UpdateProfileDto, userId: Types.ObjectId, bufferImage: Buffer) {
		let avatar = null

		if (bufferImage) {
			avatar = await uploadImageToCloudinary(bufferImage)
		}

		const user = (await this.user.findById(userId)) as UserDocument
		if (!user) {
			throw new BadRequestException('User not found')
		}
		if (body?.name || avatar || body?.sendNotification) {
			await this.user.updateOne(
				{ _id: userId },
				{
					$set: {
						name: body?.name ? body?.name : user.name,
						avatar: avatar ? avatar : user.avatar,
						sendNotification: body?.sendNotification
							? body?.sendNotification
							: user.sendNotification,
					},
				}
			)
		}

		if (body?.personalInterest) {
			const personalInterestIds = user?.personalInterest as unknown as Types.ObjectId[]

			const hasPersonalInterest = personalInterestIds.includes(body?.personalInterest)

			if (hasPersonalInterest) {
				await this.user.updateOne(
					{ _id: user._id },
					{
						$pull: {
							personalInterest: new Types.ObjectId(body?.personalInterest),
						},
					}
				)
			} else {
				await this.user.updateOne(
					{ _id: user._id },
					{
						$addToSet: {
							personalInterest: new Types.ObjectId(body?.personalInterest),
						},
					}
				)
			}
		}
		return true
	}

	async sendOtpToUpdateUserCredentials(userOldEmail: string, credentials: UpdateCredentialsDto) {
		const user = await this.emailNormalizationService.findUserByEmail(userOldEmail)
		if (!user) {
			throw new NotFoundException('User not found')
		}
		const generatedOtp = this.generateOtp()

		if (credentials.type === 'email' && credentials?.email) {
			// Validate new email for conflicts
			const emailValidation = await this.emailNormalizationService.validateEmailForRegistration(
				credentials.email
			)
			if (!emailValidation.isValid) {
				throw new BadRequestException(emailValidation.error)
			}

			const emailVerifyParams: OtpMailPayload = {
				to: credentials.email,
				otp: generatedOtp,
			}
			const newOtp = new this.otp({
				email: credentials.email, // Use original email for delivery
				type: 'email',
				otp: generatedOtp,
				userEmail: userOldEmail,
			})

			await newOtp.save()
			await sendOtpMail(emailVerifyParams)
		}

		if (credentials.type === 'mobile' && credentials?.mobile) {
			const phoneVerifyParams: OtpSmsPayload = {
				to: credentials.mobile,
				otp: generatedOtp,
			}
			const newOtp = new this.otp({
				mobile: credentials.mobile,
				type: 'mobile',
				otp: generatedOtp,
				userEmail: userOldEmail,
			})

			await newOtp.save()
			await smsOtp(phoneVerifyParams)
		}

		return {
			message:
				credentials.type === 'mobile'
					? 'Please check your mobile for the OTP.'
					: 'Please check your email for the OTP.',
			credential: credentials.type === 'mobile' ? credentials.mobile : credentials.email,
		}
	}
	generateOtp(): number {
		return Math.floor(1000 + Math.random() * 9000)
	}
	checkIfOtpIsExpired(otpDoc: OtpDocument): boolean {
		// Check if OTP is expired
		const otpExpiration = 5 * 60 * 1000 // 5 minutes in milliseconds
		if (Date.now() - otpDoc.createdAt.valueOf() > otpExpiration) {
			// OTP is expired
			return true
		}
		return false
	}

	async getOtpData(otp: number, email: string): Promise<OtpDocument | null> {
		return await this.otp.findOne({ otp, userEmail: email }).sort({ createdAt: -1 })
	}

	async verifyOtpToUpdateCredentials(userDta: ReqUser, otp: OtpDocument) {
		const user = (await this.getUserByEmail(userDta.email)) as UserDocument

		if (this.checkIfOtpIsExpired(otp)) {
			throw new NotAcceptableException('OTP Expired, Please try again!.')
		}

		const newDtaUser =
			otp.type === 'email'
				? await this.getUserByEmail(otp.email)
				: await this.getUserByMobile(otp.mobile)

		if (newDtaUser) {
			throw new NotAcceptableException(
				`User with this ${otp.type} is already registered, Please try again!.`
			)
		}

		const updates: Record<string, unknown> = {
			...(user.status === 'inactive' && { status: 'active' }),
			...(user.mobileVerified === false &&
				otp.type === 'mobile' && { mobileVerified: true, mobile: otp.mobile }),
		}

		// Handle email updates with normalization
		if (otp.type === 'email') {
			const normalizedEmail = this.emailNormalizationService.normalizeEmail(otp.email)
			updates.email = otp.email
			updates.normalizedEmail = normalizedEmail
		}

		if (Object.keys(updates).length > 0) {
			await this.user.updateOne({ _id: user._id }, { $set: updates })
		}

		return {
			id: user._id,
			email: otp.type === 'email' ? otp.email : user.email,
		}
	}

	async getAllPersonalInterestData() {
		return await this.personalInterestService.getAllPersonalInterestData()
	}

	async updateGiftCardBalance(userId: Types.ObjectId, amount: number) {
		const userData = await this.user.findById(userId)
		if (!userData) {
			throw new BadRequestException('User not found')
		}
		userData.giftCardBalance += amount
		await userData.save()
	}

	async getUserBalance(userId: Types.ObjectId) {
		const userData = await this.user.findById(userId)
		if (!userData) {
			throw new BadRequestException('User not found')
		}
		return {
			giftCard: userData.giftCardBalance,
			balance: userData.balance,
		}
	}

	async lockUserBalance(userId: Types.ObjectId, amount: number) {
		const userData = await this.user.findById(userId)
		if (!userData) {
			throw new BadRequestException('User not found')
		}
		if (userData.balance < amount) {
			throw new BadRequestException('Insufficient balance')
		}
		userData.balance -= amount
		await userData.save()
	}

	async unlockUserBalance(userId: Types.ObjectId, amount: number) {
		const userData = await this.user.findById(userId)
		if (!userData) {
			throw new BadRequestException('User not found')
		}
		userData.balance += amount
		await userData.save()
	}

	async getReferralHistory(
		userData: ReqUser,
		queryParams: GetUserHistoryDto
	): Promise<GetUsersByReferralCodeResponse> {
		const loggedUser = (await this.getUserByEmail(userData.email)) as UserDocument

		if (!loggedUser) {
			throw new BadRequestException('User not found')
		}

		function getStatusList(statusValue: string) {
			switch (statusValue) {
				case Status.active:
					return [Status.active]
				case Status.inactive:
					return [Status.inactive, Status.blocked]
				case Status.blocked:
					return [Status.blocked]
				default:
					return ['active', 'inactive', 'blocked'] // Return an empty array or handle invalid input as per your requirement
			}
		}
		const page = queryParams.page ?? 1
		const skip = (page - 1) * queryParams.pageSize

		const aggregationPipeline = []

		// Match stage for user referral
		aggregationPipeline.push({
			$match: {
				referral: loggedUser._id,
			},
		})

		// // Lookup stage to join with earnings collection
		aggregationPipeline.push({
			$lookup: {
				from: 'earnings', // The name of the earnings collection
				let: { userId: '$_id' }, // Define a variable to hold the user's ID
				pipeline: [
					{
						$match: {
							$expr: {
								$and: [
									// Use $expr to use aggregation operators
									{ $eq: ['$user', '$$userId'] }, // Match the user ID
									{ $ne: ['$status', 'cancelled'] }, // Exclude "cancelled" earnings
								],
							},
						},
					},
				],
				as: 'earnings', // Alias for the joined data
			},
		})

		// Unwind the earnings array
		aggregationPipeline.push({
			$unwind: { path: '$earnings', preserveNullAndEmptyArrays: true }, // Preserve null and empty arrays for users without earnings
		})

		// Group stage to aggregate users and their earnings
		aggregationPipeline.push({
			$group: {
				_id: '$_id', // Group by user ID
				// user: { $first: '$$ROOT' }, // Keep user information
				earnings: { $push: '$earnings' }, // Collect earnings into an array
				uid: { $first: '$uid' }, // Keep the user's uid
				name: { $first: '$name' }, // Keep the user's uid
				avatar: { $first: '$avatar.secureUrl' }, // Keep the user's uid
				status: { $first: '$status' }, // Keep the user's uid
				joined: { $first: '$createdAt' }, // Keep the user's uid
				totalOrders: {
					$sum: { $cond: [{ $ifNull: ['$earnings', false] }, 1, 0] },
				}, // Count the number of earnings
				totalCommission: { $sum: '$earnings.referralCommission' },
				totalPendingReferralCommission: {
					$sum: {
						$cond: [
							{
								$and: [
									{
										$or: [
											{ $eq: ['$earnings.status', 'pending'] },
											{ $eq: ['$earnings.status', 'tracked_for_confirm'] },
										],
									},
									{ $ne: ['$earnings', null] },
								],
							},
							'$earnings.referralCommission',
							0,
						],
					},
				}, // Sum of referral commission for pending earnings
				totalConfirmedReferralCommission: {
					$sum: {
						$cond: [
							{
								$and: [{ $eq: ['$earnings.status', 'confirmed'] }, { $ne: ['$earnings', null] }],
							},
							'$earnings.referralCommission',
							0,
						],
					},
				}, // Sum of referral commission for confirmed earnings
			},
		})

		// Match stage for status
		if (queryParams.status) {
			aggregationPipeline.push({
				$match: {
					status: { $in: getStatusList(queryParams.status) },
				},
			})
		}

		// Match stage for joined date
		if (queryParams.startDate && queryParams.endDate) {
			aggregationPipeline.push({
				$match: {
					joined: {
						$gte: new Date(queryParams.startDate),
						$lte: new Date(queryParams.endDate),
					},
				},
			})
		}

		// Match stage for name search
		if (queryParams.searchParam) {
			aggregationPipeline.push({
				$match: {
					name: { $regex: queryParams?.searchParam ?? '', $options: 'i' },
				},
			})
		}

		// Sort stage
		if (queryParams.sortType && ['newest', 'oldest'].includes(queryParams.sortType)) {
			aggregationPipeline.push({
				$match: {
					joined: {
						$exists: true, // Ensure the field 'joined' exists
					},
				},
			})
			aggregationPipeline.push({
				$sort: {
					joined: queryParams.sortType === 'newest' ? -1 : 1,
				},
			})
		}

		if (queryParams.sortType === 'highToLow' || queryParams.sortType === 'lowToHigh') {
			aggregationPipeline.push({
				$sort: {
					totalCommission: queryParams.sortType === 'highToLow' ? -1 : 1,
				},
			})
		}

		if (queryParams.sortType === 'noOfOrders') {
			aggregationPipeline.push({
				$sort: {
					totalOrders: queryParams.sortType === 'noOfOrders' ? -1 : 1,
				},
			})
		}

		// Facet stage
		aggregationPipeline.push({
			$facet: {
				aggregatedData: [
					{ $skip: skip },
					{ $limit: queryParams.pageSize },
					{
						$project: {
							_id: 0,
							totalOrders: 1,
							// totalCommission: 1,
							totalPendingReferralCommission: 1,
							totalConfirmedReferralCommission: 1,
							uid: 1,
							name: 1,
							avatar: 1,
							email: 1,
							status: 1,
							joined: 1,
						},
					},
				],
				totalCount: [
					{
						$group: {
							_id: null,
							totalCount: { $sum: 1 },
						},
					},
					{
						$project: {
							_id: 0,
							totalCount: 1,
						},
					},
				],
			},
		})

		const userEarnings = await this.user
			// biome-ignore lint/suspicious/noExplicitAny: required for external API
			.aggregate(aggregationPipeline as any)
			.exec()

		return {
			users: [...userEarnings[0].aggregatedData],
			pagination: {
				page: queryParams.page,
				pageSize: userEarnings[0]?.aggregatedData?.length,
				total: userEarnings[0]?.totalCount[0]?.totalCount ?? 0,
			},
		}
	}

	async getCashBackHistory(
		userData: ReqUser,
		queryParams: GetUserCashbackDto
	): Promise<GetCbHistoryResponse> {
		const loggedUser = (await this.getUserByEmail(userData.email)) as UserDocument

		if (!loggedUser) {
			throw new BadRequestException('User not found')
		}
		return this.userEarningService.getCashBackHistory(queryParams, loggedUser)
	}

	async withdrawBalance(userId: Types.ObjectId, amount: number, type: 'bank' | 'upi') {
		const userData = await this.user.findById(userId)
		if (!userData) {
			throw new BadRequestException('User not found')
		}
		const bankDetails = await this.bankService.getBankAccountDetails({
			id: userId,
			email: userData.email,
		})

		if (type === 'bank' && !bankDetails.accountNumber) {
			throw new BadRequestException('Bank account details not found')
		}

		if (type === 'upi' && !bankDetails.upi) {
			throw new BadRequestException('UPI ID not found')
		}

		if (userData.balance < amount) {
			throw new BadRequestException('Insufficient balance')
		}

		userData.balance -= amount

		await userData.save()

		return true
	}

	/**
	 * Helper method to get the last 12 months in chronological order
	 * Returns array of month objects with year, month, and key for mapping
	 */
	private getLast12Months() {
		const months = []
		const now = new Date()

		for (let i = 11; i >= 0; i--) {
			const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
			months.push({
				year: date.getFullYear(),
				month: date.getMonth() + 1, // MongoDB months are 1-indexed
				key: `${date.getFullYear()}-${date.getMonth() + 1}`,
			})
		}

		return months
	}

	async getUsersOverViewDetails(userData: ReqUser) {
		const loggedUser = (await this.getUserByEmail(userData.email)) as UserDocument

		if (!loggedUser) {
			throw new BadRequestException('User not found')
		}

		// Calculate date range for last 12 months
		const twelveMonthsAgo = new Date()
		twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12)
		twelveMonthsAgo.setDate(1) // Start from beginning of the month
		twelveMonthsAgo.setHours(0, 0, 0, 0) // Start of day

		// Get earnings counts using centralized service
		const { pendingCount, confirmedCount, cancelledCount } =
			await this.userCountService.getUserEarningsCounts(loggedUser._id, loggedUser)

		//getting total cashback Amount for graph (last 12 months)
		const earnings = await this.earning
			.aggregate([
				{
					$match: {
						user: loggedUser._id,
						createdAt: { $gte: twelveMonthsAgo },
					},
				},
				{
					$group: {
						_id: {
							year: { $year: '$createdAt' },
							month: { $month: '$createdAt' },
						},
						totalCashback: { $sum: '$cashbackAmount' },
					},
				},
				{
					$project: {
						totalCashback: 1,
					},
				},
			])
			.exec()
		// const earnings = this.earning.find({ user: loggedUser._id }).exec()

		//getting total clicks for graph (last 12 months)
		const clicks = await this.click
			.aggregate([
				{
					$match: {
						user: loggedUser._id,
						createdAt: { $gte: twelveMonthsAgo },
					},
				},
				{
					$group: {
						_id: {
							year: { $year: '$createdAt' },
							month: { $month: '$createdAt' },
						},
						totalClicks: { $sum: 1 },
					},
				},
				{
					$project: {
						totalClicks: 1,
					},
				},
			])
			.exec()

		//getting total Order Amount for graph (last 12 months)
		const orderAmount = await this.earning
			.aggregate([
				{
					$match: {
						user: loggedUser._id,
						createdAt: { $gte: twelveMonthsAgo },
					},
				},
				{
					$group: {
						_id: {
							year: { $year: '$createdAt' },
							month: { $month: '$createdAt' },
						},
						totalOrderAmount: { $sum: '$saleAmount' },
					},
				},
				{
					$project: {
						totalOrderAmount: 1,
					},
				},
			])
			.exec()

		//getting Total Cashback Earned (excluding reward points)
		const _totalEarnings = await this.earning
			.aggregate([
				{
					$match: {
						user: loggedUser._id,
						status: { $in: ['confirmed', 'tracked_for_confirm'] },
						rewardPoint: { $ne: true }, // Exclude reward points (false or null)
					},
				},
				{
					$group: {
						_id: null,
						totalCashback: { $sum: '$cashbackAmount' },
					},
				},
				{
					$project: {
						_id: 0, // Exclude _id field
						totalCashback: 1,
					},
				},
			])
			.exec()

		//getting total ReferralCommission
		const totalReferralCommission = await this.user
			.aggregate([
				{
					$match: {
						referral: loggedUser._id,
					},
				},
				{
					$lookup: {
						from: 'earnings', // Name of the collection to join
						localField: '_id', // Field from the 'user' collection
						foreignField: 'user', // Field from the 'earnings' collection
						as: 'userEarnings', // Output array field
					},
				},
				{
					$unwind: '$userEarnings', // Deconstruct the array field
				},
				{
					$match: {
						'userEarnings.status': 'confirmed', // Filter by status 'confirmed'
					},
				},
				{
					$group: {
						_id: null,
						totalCommission: { $sum: '$userEarnings.referralCommission' }, // Sum referralCommission
					},
				},
				{
					$project: {
						_id: 0, // Exclude _id field
						totalCommission: 1,
					},
				},
			])
			.exec()

		//getting total Rewards for rewardPoint = true
		const totalRewardsQuery = await this.earning
			.aggregate([
				{
					$match: {
						user: loggedUser._id,
						status: { $in: ['confirmed', 'tracked_for_confirm'] },
						rewardPoint: true, // Added filter for rewardPoint
					},
				},
				{
					$group: {
						_id: null,
						totalRewards: { $sum: '$cashbackAmount' }, // Summing cashbackAmount
					},
				},
				{
					$project: {
						_id: 0,
						totalRewards: 1,
					},
				},
			])
			.exec()

		// Get the last 12 months in chronological order
		const last12Months = this.getLast12Months()

		// Map aggregation results to chronological month arrays
		const cashbackAmounts = last12Months.map(monthInfo => {
			const found = earnings.find(
				item => item._id.year === monthInfo.year && item._id.month === monthInfo.month
			)
			return found ? found.totalCashback : 0
		})

		const totalClicksPerMonth = last12Months.map(monthInfo => {
			const found = clicks.find(
				item => item._id.year === monthInfo.year && item._id.month === monthInfo.month
			)
			return found ? found.totalClicks : 0
		})

		const totalOrderAmountsPerMonth = last12Months.map(monthInfo => {
			const found = orderAmount.find(
				item => item._id.year === monthInfo.year && item._id.month === monthInfo.month
			)
			return found ? found.totalOrderAmount : 0
		})

		// Counts are already calculated correctly by UserCountService
		const totalPendingCount = pendingCount
		const totalApprovedCount = confirmedCount
		const totalCancelledCount = cancelledCount

		return {
			totalCashback: cashbackAmounts,
			totalClicks: totalClicksPerMonth,
			totalOrderAmount: totalOrderAmountsPerMonth,
			readyToWithdraw: loggedUser?.balance,
			totalPendingCount,
			totalCashbackEarned: _totalEarnings[0]?.totalCashback ?? 0,
			totalReferralCommission: totalReferralCommission[0]?.totalCommission ?? 0,
			totalApprovedCount,
			totalCancelledCount,
			flipkartRewardPoints: totalRewardsQuery[0]?.totalRewards ?? 0,
		}
	}

	async generateReferralCode(email: string) {
		return hash({ email }).toUpperCase().slice(0, 6)
	}

	async updateAllUsersReferralCode() {
		const users = await this.user
			.find({
				referralCode: null,
			})
			.exec()

		let updatedCount = 0
		let skippedCount = 0

		for (const user of users) {
			try {
				let referralCode: string
				let isUnique = false
				let attempts = 0
				const maxAttempts = 5

				while (!isUnique && attempts < maxAttempts) {
					if (user.email) {
						referralCode = hash({ email: user.email, attempt: attempts }).toUpperCase().slice(0, 6)
					} else if (user.mobile) {
						referralCode = hash({
							mobile: user.mobile.toString(),
							attempt: attempts,
						})
							.toUpperCase()
							.slice(0, 6)
					} else {
						skippedCount++
						break
					}

					// Check if referral code already exists
					const existingUser = await this.user.findOne({ referralCode }).exec()
					if (existingUser) {
						attempts++
					} else {
						isUnique = true
						user.referralCode = referralCode
						await user.save()
						updatedCount++
					}
				}

				if (attempts >= maxAttempts) {
					skippedCount++
				}
			} catch (_error) {
				skippedCount++
			}
		}

		return {
			total: users.length,
			updated: updatedCount,
			skipped: skippedCount,
		}
	}

	async listUsers() {
		// Get all users with required fields
		const users = await this.user
			.find()
			.populate('referral', 'email')
			.select(
				'uid name email mobile referralCode userNotes mobileVerified status migrated referral'
			)
			.lean()
			.exec()

		// Get last login time from cookie sessions
		const usersWithLastLogin = await this.cookieSession.aggregate([
			{ $match: { status: 'active' } },
			{ $sort: { createdAt: -1 } },
			{
				$group: {
					_id: '$user',
					lastLoggedDate: { $first: '$createdAt' },
				},
			},
		])

		// Create a map of user IDs to last login dates
		const lastLoginMap = new Map()
		for (const session of usersWithLastLogin) {
			lastLoginMap.set(session._id.toString(), session.lastLoggedDate)
		}

		// Add last login time to users and extract referral email
		const usersList = users.map(user => {
			const userId = user._id.toString()
			return {
				...user,
				lastLoggedDate: lastLoginMap.get(userId) || null,
				referredByEmail: user.referral?.email || null,
				referral: undefined, // Remove the referral object from the response
				_id: undefined, // Remove the _id field from the response
			}
		})

		// Save data to JSON file
		this.saveUsersToJsonFile(usersList)

		return { users: usersList }
	}

	private saveUsersToJsonFile(users: Record<string, unknown>[]) {
		try {
			const filePath = path.join(process.cwd(), 'users-lastRecorded.json')
			fs.writeFileSync(filePath, JSON.stringify(users, null, 2))
		} catch (_error) {}
	}
}
