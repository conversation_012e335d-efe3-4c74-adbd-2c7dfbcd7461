import { ExecutionContext, Injectable } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'

@Injectable()
export class UpdateDetailsOtpGuard extends AuthGuard('update-details-otp-session') {
	async canActivate(context: ExecutionContext) {
		const result = (await super.canActivate(context)) as boolean
		const request = context.switchToHttp().getRequest()
		request.res.clearCookie('accessToken')
		await super.logIn(request)
		return result
	}
}
