import { Injectable, NotFoundException } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { Strategy } from 'passport-custom'
import { UserService } from './user.service'

// Importing necessary modules and dependencies
@Injectable()
export class UpdateDetailsOtpStrategy extends PassportStrategy(
	Strategy,
	'update-details-otp-session'
) {
	constructor(private readonly userService: UserService) {
		super()
	}

	// biome-ignore lint/suspicious/noExplicitAny: required for external API
	async validate(request: any): Promise<any> {
		const { otp } = request.body
		const otpData = await this.userService.getOtpData(otp, request.user.email)
		if (!otpData) {
			throw new NotFoundException('Invalid OTP')
		}
		const user = await this.userService.verifyOtpToUpdateCredentials(request.user, otpData)
		return { email: user.email, id: user.id }
	}
}
