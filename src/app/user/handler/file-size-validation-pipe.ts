import { ArgumentMetadata, Injectable, PipeTransform } from '@nestjs/common'

@Injectable()
export class FileSizeValidationPipe implements PipeTransform {
	// biome-ignore lint/suspicious/noExplicitAny: required for external API
	transform(value: any, _metadata: ArgumentMetadata) {
		// "value" is an object containing the file's attributes and metadata
		const oneKb = 1000
		return value.size < oneKb
	}
}
