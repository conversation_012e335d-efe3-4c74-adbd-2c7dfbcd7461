import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { Earning, EarningDocument } from 'shared/entities/earning.entity'
import { UserDocument } from 'shared/entities/user.entity'

export interface UserCounts {
	pendingCount: number
	confirmedCount: number
	cancelledCount: number
}

@Injectable()
export class UserCountService {
	constructor(
		@InjectModel(Earning.name)
		private earning: Model<EarningDocument>
	) {}

	/**
	 * Get user earnings counts based only on earnings collection
	 * @param userId - User ID to get counts for
	 * @param user - User document (optional, not used but kept for compatibility)
	 * @returns Object with pendingCount, confirmedCount, cancelledCount
	 */
	async getUserEarningsCounts(userId: Types.ObjectId, _user?: UserDocument): Promise<UserCounts> {
		const [pendingCount, confirmedCount, cancelledCount] = await Promise.all([
			this.getEarningsCountByStatus(userId, ['pending', 'tracked_for_confirm']),
			this.getEarningsCountByStatus(userId, ['confirmed']),
			this.getEarningsCountByStatus(userId, ['cancelled', 'tracked_for_cancel']),
		])

		return {
			pendingCount,
			confirmedCount,
			cancelledCount,
		}
	}

	/**
	 * Get earnings count by status
	 * @param userId - User ID
	 * @param statuses - Array of statuses to count
	 * @returns Count of earnings
	 */
	private async getEarningsCountByStatus(
		userId: Types.ObjectId,
		statuses: string[]
	): Promise<number> {
		const query = {
			user: userId,
			status: { $in: statuses },
		}

		return await this.earning.countDocuments(query).exec()
	}

	/**
	 * Get earnings count using aggregation (for complex queries)
	 * @param userId - User ID
	 * @param statuses - Array of statuses to count
	 * @returns Aggregation result with count
	 */
	async getEarningsCountByAggregation(userId: Types.ObjectId, statuses: string[]): Promise<number> {
		const pipeline = [
			{ $match: { user: userId, status: { $in: statuses } } },
			{
				$group: {
					_id: null,
					count: { $sum: 1 },
				},
			},
			{
				$project: {
					_id: 0,
					count: 1,
				},
			},
		]

		const result = await this.earning.aggregate(pipeline).exec()
		return result.length > 0 ? result[0].count : 0
	}

	/**
	 * Get detailed breakdown of earnings for debugging purposes
	 * @param userId - User ID
	 * @returns Detailed breakdown of earnings by status
	 */
	async getEarningsBreakdown(userId: Types.ObjectId): Promise<{
		breakdown: Array<{ _id: string; count: number; totalAmount: number }>
		totalEarnings: number
	}> {
		const pipeline = [
			{ $match: { user: userId } },
			{
				$group: {
					_id: '$status',
					count: { $sum: 1 },
					totalAmount: { $sum: '$cashbackAmount' },
				},
			},
			{
				$sort: { _id: 1 as const },
			},
		]

		const breakdown = await this.earning.aggregate(pipeline).exec()
		const totalEarnings = await this.earning.countDocuments({ user: userId }).exec()

		return {
			breakdown,
			totalEarnings,
		}
	}
}
