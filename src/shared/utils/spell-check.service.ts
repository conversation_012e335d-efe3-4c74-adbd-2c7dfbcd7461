import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { GiftCard, GiftCardDocument } from 'shared/entities/gift-card.entity'
import { Offer, OfferDocument } from 'shared/entities/offer.entity'
import { Store, StoreDocument } from 'shared/entities/store.entity'

export interface SpellCheckSuggestion {
	suggestion: string
	confidence: number
}

export interface WordSuggestion {
	originalWord: string
	suggestedWord: string
	confidence: number
}

@Injectable()
export class SpellCheckService {
	private dictionary: Set<string> = new Set()
	private lastDictionaryUpdate = 0
	private readonly dictionaryCacheDuration = 1000 * 60 * 60 // 1 hour

	constructor(
		@InjectModel(Store.name) private readonly storeModel: Model<StoreDocument>,
		@InjectModel(Offer.name) private readonly offerModel: Model<OfferDocument>,
		@InjectModel(GiftCard.name)
		private readonly giftCardModel: Model<GiftCardDocument>
	) {}

	/**
	 * Calculate Levenshtein distance between two strings using a simple approach
	 */
	private levenshteinDistance(str1: string, str2: string): number {
		if (str1.length === 0) {
			return str2.length
		}
		if (str2.length === 0) {
			return str1.length
		}

		// Use a simple character-by-character comparison for similarity
		let matches = 0
		const minLength = Math.min(str1.length, str2.length)

		for (let i = 0; i < minLength; i++) {
			if (str1.charAt(i).toLowerCase() === str2.charAt(i).toLowerCase()) {
				matches++
			}
		}

		// Calculate distance based on matches and length difference
		const maxLength = Math.max(str1.length, str2.length)
		return maxLength - matches
	}

	/**
	 * Calculate similarity score between two strings (0-1, where 1 is identical)
	 */
	private calculateSimilarity(str1: string, str2: string): number {
		const maxLength = Math.max(str1.length, str2.length)
		if (maxLength === 0) {
			return 1
		}
		const distance = this.levenshteinDistance(str1.toLowerCase(), str2.toLowerCase())
		return (maxLength - distance) / maxLength
	}

	/**
	 * Extract meaningful words from a text string
	 */
	private extractWords(text: string): string[] {
		return text
			.toLowerCase()
			.replace(/[^\w\s]/g, ' ') // Replace non-word characters with spaces
			.split(/\s+/)
			.filter(word => word.length > 2) // Only words longer than 2 characters
			.filter(word => !/^\d+$/.test(word)) // Exclude pure numbers
	}

	/**
	 * Build dictionary from database content
	 */
	// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex dictionary building logic - refactoring would impact performance
	private async buildDictionary(): Promise<void> {
		const now = Date.now()
		if (
			now - this.lastDictionaryUpdate < this.dictionaryCacheDuration &&
			this.dictionary.size > 0
		) {
			return // Use cached dictionary
		}

		this.dictionary.clear()

		try {
			// Get store names
			const stores = await this.storeModel.find({ active: true }, 'name').lean()
			for (const store of stores) {
				if (store.name) {
					for (const word of this.extractWords(store.name)) {
						this.dictionary.add(word)
					}
				}
			}

			// Get offer titles
			const offers = await this.offerModel.find({ active: true }, 'title').lean()
			for (const offer of offers) {
				if (offer.title) {
					for (const word of this.extractWords(offer.title)) {
						this.dictionary.add(word)
					}
				}
			}

			// Get gift card names
			const giftCards = await this.giftCardModel.find({ active: true }, 'name').lean()
			for (const giftCard of giftCards) {
				if (giftCard.name) {
					for (const word of this.extractWords(giftCard.name)) {
						this.dictionary.add(word)
					}
				}
			}

			this.lastDictionaryUpdate = now
		} catch (_error) {}
	}

	/**
	 * Check if a word exists in the dictionary
	 */
	private async isValidWord(word: string): Promise<boolean> {
		await this.buildDictionary()
		return this.dictionary.has(word.toLowerCase())
	}

	/**
	 * Find suggestions for a single word
	 */
	private async findWordSuggestions(word: string, maxSuggestions = 3): Promise<WordSuggestion[]> {
		await this.buildDictionary()

		const dictionaryArray = Array.from(this.dictionary)
		const suggestions = dictionaryArray
			.map(dictWord => ({
				originalWord: word,
				suggestedWord: dictWord,
				confidence: this.calculateSimilarity(word, dictWord),
			}))
			.filter(item => item.confidence > 0.6) // Only suggest words with >60% similarity
			.sort((a, b) => b.confidence - a.confidence)
			.slice(0, maxSuggestions)

		return suggestions
	}

	/**
	 * Generate spelling suggestions for a given search term (supports multi-word queries)
	 */
	async generateSuggestions(
		searchTerm: string,
		maxSuggestions = 3
	): Promise<SpellCheckSuggestion[]> {
		if (!searchTerm || searchTerm.trim().length < 2) {
			return []
		}

		await this.buildDictionary()

		const cleanSearchTerm = searchTerm.trim().toLowerCase()
		const words = this.extractWords(cleanSearchTerm)

		if (words.length === 0) {
			return []
		}

		// Check each word and collect invalid ones with their suggestions
		const wordSuggestions: WordSuggestion[] = []

		for (const word of words) {
			if (!(await this.isValidWord(word))) {
				const suggestions = await this.findWordSuggestions(word, maxSuggestions)
				wordSuggestions.push(...suggestions)
			}
		}

		if (wordSuggestions.length === 0) {
			return []
		}

		// Generate complete phrase suggestions by replacing misspelled words
		const phraseSuggestions: SpellCheckSuggestion[] = []

		// Group suggestions by original word
		const suggestionsByWord = new Map<string, WordSuggestion[]>()
		for (const suggestion of wordSuggestions) {
			if (!suggestionsByWord.has(suggestion.originalWord)) {
				suggestionsByWord.set(suggestion.originalWord, [])
			}
			const wordSuggestionList = suggestionsByWord.get(suggestion.originalWord)
			if (wordSuggestionList) {
				wordSuggestionList.push(suggestion)
			}
		}

		// Generate combinations of corrected phrases
		const generatePhrases = (
			wordIndex: number,
			currentPhrase: string[],
			currentConfidence: number
		): void => {
			if (wordIndex >= words.length) {
				if (currentPhrase.length > 0) {
					phraseSuggestions.push({
						suggestion: currentPhrase.join(' '),
						confidence: currentConfidence / words.length, // Average confidence
					})
				}
				return
			}

			const currentWord = words[wordIndex]
			if (!currentWord) {
				return
			}

			const suggestions = suggestionsByWord.get(currentWord)

			if (suggestions && suggestions.length > 0) {
				// Try each suggestion for this word
				for (const suggestion of suggestions.slice(0, 2)) {
					// Limit to top 2 suggestions per word
					generatePhrases(
						wordIndex + 1,
						[...currentPhrase, suggestion.suggestedWord],
						currentConfidence + suggestion.confidence
					)
				}
			} else {
				// Word is valid, keep it as is
				generatePhrases(
					wordIndex + 1,
					[...currentPhrase, currentWord],
					currentConfidence + 1.0 // Perfect confidence for valid words
				)
			}
		}

		generatePhrases(0, [], 0)

		// Remove duplicates and sort by confidence
		const uniqueSuggestions = phraseSuggestions
			.filter(
				(item, index, self) => index === self.findIndex(s => s.suggestion === item.suggestion)
			)
			.filter(item => item.suggestion !== cleanSearchTerm) // Don't suggest the original term
			.sort((a, b) => b.confidence - a.confidence)
			.slice(0, maxSuggestions)

		return uniqueSuggestions
	}

	/**
	 * Check if search results are minimal (indicating possible misspelling)
	 */
	shouldSuggestAlternatives(totalResults: number, searchTerm: string): boolean {
		// Suggest alternatives if:
		// 1. No results found, OR
		// 2. Very few results (< 3) for search terms longer than 3 characters
		return totalResults === 0 || (totalResults < 3 && searchTerm.trim().length > 3)
	}
}
