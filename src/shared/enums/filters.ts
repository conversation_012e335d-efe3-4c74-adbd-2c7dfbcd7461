export enum SortTypes {
	Newest = 'newest',
	Discount = 'discount',
	HighestCbPercent = 'highestCbPercent',
	HighestCbAmount = 'highestCbAmount',
	Alphabetical = 'alphabetical',
	Popular = 'popular',
}

export enum ReferralTypes {
	Newest = 'newest',
	Oldest = 'oldest',
	HighToLow = 'highToLow',
	LowToHigh = 'lowToHigh',
	NoOfOrders = 'noOfOrders',
}
export enum PaymentSortTypes {
	Newest = 'newest',
	Oldest = 'oldest',
	Amount = 'amount',
}

export enum CashbackSortTypes {
	Newest = 'newest',
	Oldest = 'oldest',
	CashbackAmount = 'cashbackAmount',
}

export enum EarningStatusTypes {
	Pending = 'pending',
	Confirmed = 'confirmed',
	Cancelled = 'cancelled',
}

export enum UserTypes {
	New = 'new',
	Existing = 'existing',
	Both = 'both',
}

export enum OfferTypes {
	Coupons = 'coupons',
	Deals = 'deals',
	Trending = 'trending',
	Both = 'both',
}

export enum BannerTypes {
	Context = 'context',
	GiftCard = 'giftCard',
}
export enum PaymentTypes {
	Bank = 'bank',
	Upi = 'upi',
}

export enum ReviewTypes {
	Newest = 'newest',
	Rating = 'rating',
}
export enum RedeemHistoryTypes {
	Date = 'date',
	Amount = 'amount',
}

export enum StoreSortTypes {
	Newest = 'newest',
	Alphabetical = 'alphabetical',
}

export const Payment = {
	balance: 'balance',
	razorpay: 'razorpay',
} as const

export type PaymentType = (typeof Payment)[keyof typeof Payment]

export const ReportMissingSortTypes = {
	newest: 'newest',
	oldest: 'oldest',
} as const
