import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator'

export function IsOptionalEnum(entity: object, validationOptions?: ValidationOptions) {
	return (object: object, propertyName: string) => {
		registerDecorator({
			name: 'isOptionalEnum',
			target: object.constructor,
			propertyName,
			constraints: [entity],
			options: validationOptions,
			validator: {
				// biome-ignore lint/suspicious/noExplicitAny: required for external API
				validate(value: any, args: ValidationArguments) {
					const [enumEntity] = args.constraints
					return (
						value === undefined ||
						value === null ||
						value === '' ||
						Object.values(enumEntity).includes(value)
					)
				},
			},
		})
	}
}
