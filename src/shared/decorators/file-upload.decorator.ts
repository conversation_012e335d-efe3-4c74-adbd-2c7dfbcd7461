import crypto from 'node:crypto'
import { extname } from 'node:path'
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3'
import { applyDecorators, NotAcceptableException, UseInterceptors } from '@nestjs/common'
import { FileInterceptor } from '@nestjs/platform-express'
import * as cloudinary from 'cloudinary'
import { env } from 'config'
import { diskStorage } from 'multer'

const awsClient = new S3Client({
	region: env.AWS_BUCKET.region,
	credentials: {
		accessKeyId: env.AWS_BUCKET.accessKeyId,
		secretAccessKey: env.AWS_BUCKET.secretAccessKey,
	},
})

cloudinary.v2.config({
	// biome-ignore lint/style/useNamingConvention: required for external API
	cloud_name: env.CLOUDINARY.cloudName,
	// biome-ignore lint/style/useNamingConvention: required for external API
	api_key: env.CLOUDINARY.apiKey,
	// biome-ignore lint/style/useNamingConvention: required for external API
	api_secret: env.CLOUDINARY.apiSecret,
})

const _storage = diskStorage({
	destination: './uploads',
	filename: (_req, file, cb) => {
		// Generating a random 32-character string as the filename
		const randomName = new Array(32)
			.fill(null)
			.map(() => Math.round(Math.random() * 16).toString(16))
			.join('')
		// Calling the callback passing the random name generated with the original extension name
		cb(null, `${randomName}${extname(file.originalname)}`)
	},
})

// biome-ignore lint/suspicious/noExplicitAny: required for external API
const imageFileFilter = (_req: Request, file: Express.Multer.File, cb: any) => {
	// Check if the file is a PNG, JPG, JPEG, or SVG image
	if (
		file.mimetype === 'image/png' ||
		file.mimetype === 'image/jpg' ||
		file.mimetype === 'image/jpeg' ||
		file.mimetype === 'image/webp' ||
		file.mimetype === 'image/svg+xml'
	) {
		cb(null, true) // Accept the file
	} else {
		cb(
			new NotAcceptableException(
				`${file.fieldname} must be in PNG, JPG, JPEG, WebP or SVG format.`
			),
			false
		)
	}
}

// biome-ignore lint/suspicious/noExplicitAny: required for external API
const fileFilterForAws = (_req: Request, file: Express.Multer.File, cb: any) => {
	if (
		file.mimetype === 'image/png' ||
		file.mimetype === 'image/jpg' ||
		file.mimetype === 'image/jpeg' ||
		file.mimetype === 'image/webp' ||
		file.mimetype === 'application/pdf'
	) {
		cb(null, true) // Accept the file
	} else {
		cb(
			new NotAcceptableException(
				`${file.fieldname} must be in PNG, JPG, JPEG, WebP or PDF format.`
			),
			false
		)
	}
}

export function FileUpload(fieldName: string) {
	return applyDecorators(
		UseInterceptors(
			FileInterceptor(fieldName, {
				fileFilter: imageFileFilter,
				limits: {
					fileSize: 2048 * 1024, // 2MB
				},
			})
		)
	)
}

export type Response = {
	secureUrl: string
	publicId: string
}
export function uploadImageToCloudinary(bufferFile: Buffer): Promise<Response> {
	return new Promise(resolve => {
		cloudinary.v2.uploader
			// biome-ignore lint/suspicious/noExplicitAny: required for external API
			.upload_stream((_error: any, uploadResult: any) => {
				return resolve(uploadResult)
			})
			.end(bufferFile)
		// biome-ignore lint/suspicious/noExplicitAny: required for external API
	}).then((uploadResult: any) => {
		return {
			publicId: uploadResult.public_id,
			secureUrl: uploadResult.secure_url,
		}
	})
}

export function AwsFileUpload(fieldName: string) {
	return applyDecorators(
		UseInterceptors(
			FileInterceptor(fieldName, {
				fileFilter: fileFilterForAws,
				limits: {
					fileSize: 2048 * 1024, // 2MB
				},
			})
		)
	)
}

export async function uploadFileToAwsBucket(file: Express.Multer.File) {
	const filename = `${crypto.randomUUID()}-${file.originalname}`
	const _uploadResult = await awsClient.send(
		new PutObjectCommand({
			// biome-ignore lint/style/useNamingConvention: required for external API
			Bucket: env.AWS_BUCKET.bucketName,
			// biome-ignore lint/style/useNamingConvention: required for external API
			Key: filename,
			// biome-ignore lint/style/useNamingConvention: required for external API
			Body: file.buffer,
		})
	)
	// const signedUrl = await getSignedUrl(awsClient, result, {
	// 	expiresIn: 3600,
	// })
	// console.log('🚀 ~ uploadFileToAwsBucket ~ signedUrl:', signedUrl)
	return {
		key: filename,
		location: filename,
	}
}
