import { Types } from 'mongoose'

export function buildStoreCategoryByStore(storeId: string) {
	const aggregationPipeline = []
	aggregationPipeline.push({
		$match: {
			store: new Types.ObjectId(storeId),
			active: true,
		},
	})
	aggregationPipeline.push({
		$project: {
			_id: 0,
			name: 1,
			uid: 1,
			oldUserOfferAmount: 1,
			oldUserOfferPercent: 1,
			newUserOfferAmount: 1,
			newUserOfferPercent: 1,
			description: 1,
		},
	})

	return aggregationPipeline
}
