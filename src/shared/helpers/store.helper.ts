import { Types } from 'mongoose'
import { SortTypes } from 'shared/enums'
import { GetAllStoresDto } from '../../app/store/dto/get-all-stores.dto'

export function buildStoreQuery(queryParams: GetAllStoresDto): object {
	const conditions = []

	if (queryParams?.searchParam) {
		conditions.push({
			$or: [
				{ name: { $regex: new RegExp(queryParams.searchParam, 'i') } },
				{
					storeOffer: { $regex: new RegExp(queryParams.searchParam, 'i') },
				},
			],
		})
	}

	//remove percent filter from store
	// if (
	// 	queryParams &&
	// 	queryParams.minPercent !== undefined &&
	// 	queryParams.maxPercent !== undefined
	// ) {
	// 	conditions.push({
	// 		cashbackAmount: {
	// 			$gte: queryParams.minPercent,
	// 			$lte: queryParams.maxPercent,
	// 		},
	// 	})
	// }

	return conditions.length > 0 ? { $and: conditions } : {}
}

export function buildSortQuery(queryParams: GetAllStoresDto): Record<string, 1 | -1> {
	switch (queryParams?.sortType) {
		case SortTypes.Newest:
			return { createdAt: -1 }
		case SortTypes.Discount:
			return { cashbackAmount: -1 }
		case SortTypes.Alphabetical:
			return { name: 1 }
		default:
			return { createdAt: -1 } // Default sort
	}
}

export function buildStoreAggregateQuery({
	subCategoryNames,
	subCategoryIds,
	sortQuery,
	skip,
	limit,
	queryConditions,
}: {
	subCategoryNames?: string[]
	subCategoryIds?: string[]
	sortQuery?: Record<string, 1 | -1>
	skip?: number
	limit?: number
	queryConditions?: object
	userId?: string
}) {
	const aggregationPipeline = []
	// Match stage
	if (queryConditions) {
		aggregationPipeline.push({
			$match: queryConditions,
		})
	}

	// Conditionally include subCategory Ids match
	if (subCategoryIds && subCategoryIds.length > 0) {
		const objectIdSubCategoryIds = subCategoryIds.map(id => new Types.ObjectId(id))
		aggregationPipeline.push({
			$match: {
				'categories.subCategories': { $in: objectIdSubCategoryIds },
			},
		})
	}
	// Conditionally include subCategory names match
	if (subCategoryNames && subCategoryNames.length > 0) {
		aggregationPipeline.push({
			$match: {
				categories: {
					$elemMatch: {
						subCategories: {
							$elemMatch: {
								name: { $in: subCategoryNames },
							},
						},
					},
				},
			},
		})
	}

	// Match stage to only return items where active is true
	aggregationPipeline.push({
		$match: { active: true },
	})

	// Filter pipeline for facet stage
	const filterPipeline = []

	// Sort stage
	if (sortQuery) {
		filterPipeline.push({ $sort: sortQuery })
	}

	// Skip stage
	if (skip) {
		filterPipeline.push({ $skip: skip })
	}
	// Limit stage
	if (limit) {
		filterPipeline.push({ $limit: limit })
	}

	// Project stage
	filterPipeline.push({
		$project: {
			name: 1,
			uid: 1,
			storeOffer: 1,
			bgColor: 1,
			logo: 1,
			categories: 1,
		},
	})

	// Facet stage
	aggregationPipeline.push({
		$facet: {
			documents: filterPipeline,
			totalCount: [{ $count: 'count' }],
		},
	})
	return aggregationPipeline
}
