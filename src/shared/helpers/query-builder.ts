import { GetSavedOffersDto } from 'app/saved-item/dto/saved-item.dto'
import {
	OfferTypes,
	PaymentSortTypes,
	RedeemHistoryTypes,
	ReferralTypes,
	ReviewTypes,
	SortTypes,
	StoreSortTypes,
	UserTypes,
} from 'shared/enums'

export type OrQuery<T> = {
	$or?: T[]
}

export type UserTypeQuery = {
	userType: UserTypes
}

export function buildUserHistorySortQuery(
	sortType: ReferralTypes | undefined,
	dateNewestField: string,
	dateOldestField: string
): Record<string, 1 | -1> {
	switch (sortType) {
		case ReferralTypes.Newest:
			return { [dateNewestField]: -1 }
		case ReferralTypes.Oldest:
			return { [dateOldestField]: 1 }
		case ReferralTypes.HighToLow:
			return { [dateOldestField]: -1 }
		case ReferralTypes.LowToHigh:
			return { [dateOldestField]: 1 }
		default:
			return { [dateNewestField]: -1 } // Default sort
	}
}

export function buildSortQuery(
	sortType: SortTypes | undefined,
	dateField: string,
	alphabeticalSortField: string,
	discountField = ''
): Record<string, 1 | -1> {
	switch (sortType) {
		case SortTypes.Newest:
			return { [dateField]: -1 }
		case SortTypes.HighestCbAmount:
			return { [discountField]: -1 }
		case SortTypes.HighestCbPercent:
			return { [discountField]: -1 }
		case SortTypes.Alphabetical:
			return { [alphabeticalSortField]: 1 }
		case SortTypes.Popular:
			return { priority: -1, [alphabeticalSortField]: 1 }
		default:
			return { [dateField]: -1 } // Default sort
	}
}

export function buildStoreSortQuery(
	sortType: StoreSortTypes | undefined,
	dateField: string,
	alphabeticalSortField: string
): Record<string, 1 | -1> {
	switch (sortType) {
		case StoreSortTypes.Newest:
			return { [dateField]: -1 }
		case StoreSortTypes.Alphabetical:
			return { [alphabeticalSortField]: 1 }
		default:
			return { [dateField]: -1 } // Default sort
	}
}

export function buildOfferQuery(
	offerType: OfferTypes | undefined,
	offerTypeField: string
): Record<string, unknown> {
	switch (offerType) {
		case OfferTypes.Coupons:
			return {
				$and: [
					{ [offerTypeField]: { $ne: null } }, // Check if couponCode is not null
					{ [offerTypeField]: { $ne: '' } }, // Check if couponCode is not an empty string
				],
			}
		case OfferTypes.Trending:
			return { trending: true } // Return only trending offers
		// case OfferTypes.Deals:
		//     return { [offerTypeField]: { $in: [null, ""] } }; // Documents without a couponCode
		default:
			return {} // Default to an empty query
	}
}
export function buildUserTypeQuery(
	userType: UserTypes | undefined,
	userTypeField: string
): OrQuery<UserTypeQuery> {
	switch (userType) {
		case UserTypes.New:
			return {
				$or: [
					{ [userTypeField as keyof UserTypeQuery]: UserTypes.New },
					{ [userTypeField as keyof UserTypeQuery]: UserTypes.Both },
				],
			}
		case UserTypes.Existing:
			return {
				$or: [
					{ [userTypeField as keyof UserTypeQuery]: UserTypes.Existing },
					{ [userTypeField as keyof UserTypeQuery]: UserTypes.Both },
				],
			}
		// case UserTypes.Both:
		//     return { $or: [{ [userTypeField as keyof UserTypeQuery]: UserTypes.Both }] };// Default
		default:
			return {
				$or: [
					{ [userTypeField as keyof UserTypeQuery]: UserTypes.Existing },
					{ [userTypeField as keyof UserTypeQuery]: UserTypes.New },
					{ [userTypeField as keyof UserTypeQuery]: UserTypes.Both },
				],
			}
	}
}

export interface FilterQueryOptions {
	itemType: string
	userId: string
	lookUpFrom: string
	nestedLookUpFrom: string
	nestedLookUpField: string
	queryParams: GetSavedOffersDto
}

export function buildFilterQuery(options: FilterQueryOptions) {
	const { itemType, userId, lookUpFrom, nestedLookUpFrom, nestedLookUpField, queryParams } = options

	//Filter items
	const userTypeQuery = buildUserTypeQuery(queryParams.userType, 'offer.userType')
	const subCategoriesArray = queryParams.subCategoriesArray
	const searchParam = queryParams?.searchParam ?? ''

	const aggregationPipeline = []

	// Match stage to filter user's deal documents based on user _id
	aggregationPipeline.push({
		$match: {
			type: itemType,
			user: userId,
		},
	})

	// Lookup stage to populate "offers" field
	aggregationPipeline.push({
		$lookup: {
			from: lookUpFrom, // Assuming the collection name is 'offers'
			localField: 'item', // Assuming the field containing offer IDs is 'offers'
			foreignField: '_id',
			as: 'item',
		},
	})

	// Unwind the "offers" array to get individual offer documents
	aggregationPipeline.push({
		$unwind: '$item',
	})

	// Lookup stage to populate "store" field inside "offers" documents
	aggregationPipeline.push({
		$lookup: {
			from: nestedLookUpFrom, // Assuming the collection name is 'stores'
			localField: nestedLookUpField, // Assuming the field containing store IDs is 'store'
			foreignField: '_id',
			as: 'nestedItem',
		},
	})

	// Unwind the "offers.store" array to get individual store documents inside each offer
	aggregationPipeline.push({
		$unwind: '$nestedItem',
	})

	// Match stage to filter offers based on your criteria
	aggregationPipeline.push({
		$match: {
			//TO DO -add user type query after the schema has it.
			...userTypeQuery,

			// Conditionally include subCategories match
			...(subCategoriesArray && subCategoriesArray.length > 0
				? {
						'item.categories.subCategories.uid': {
							$in: subCategoriesArray,
						},
					}
				: {}),

			// Conditionally include title search if searchParam is a non-empty string
			...(searchParam && typeof searchParam === 'string' && searchParam.trim() !== ''
				? { 'item.title': { $regex: searchParam, $options: 'i' } } //i -> case insensitive
				: {}),
		},
	})
}

// export async savedDealQuery () {
//
//
//
// }

export function buildStoreReviewSortQuery(
	sortType: ReviewTypes | undefined,
	dateField: string,
	ratesField: string
): Record<string, 1 | -1> {
	switch (sortType) {
		case ReviewTypes.Newest:
			return { [dateField]: -1 }
		case ReviewTypes.Rating:
			return { [ratesField]: -1 }
		default:
			return { [dateField]: -1 } // Default sort
	}
}

export function buildGiftCardRedeemHistorySortQuery(
	sortType: RedeemHistoryTypes | undefined,
	dateField: string,
	ratesField: string
): Record<string, 1 | -1> {
	switch (sortType) {
		case RedeemHistoryTypes.Date:
			return { [dateField]: -1 }
		case RedeemHistoryTypes.Amount:
			return { [ratesField]: -1 }
		default:
			return { [dateField]: -1 } // Default sort
	}
}

export function buildPaymentSortQuery(
	sortType: PaymentSortTypes | undefined,
	dateField: string,
	amountField: string
): Record<string, 1 | -1> {
	switch (sortType) {
		case PaymentSortTypes.Newest:
			return { [dateField]: -1 }
		case PaymentSortTypes.Oldest:
			return { [dateField]: 1 }
		case PaymentSortTypes.Amount:
			return { [amountField]: 1 }
		default:
			return { [dateField]: -1 } // Default sort
	}
}
