import { CouponsAndDealsDto, OngoingSalesDto } from 'app/offer/dto/offer.dto'
import mongoose, { Types } from 'mongoose'
import {
	buildOfferQuery,
	buildSortQuery,
	buildUserTypeQuery,
	OrQuery,
	UserTypeQuery,
} from './query-builder'
import { convertLocalToUTCDate } from './time.helper'

export function buildOfferAggregateQuery(
	queryParams: CouponsAndDealsDto,
	subCategoryIds: string[]
) {
	const page = queryParams.page ?? 1
	const skip = (page - 1) * queryParams.pageSize

	const searchParam = queryParams?.searchParam ?? ''
	const sortQuery = buildSortQuery(
		queryParams.sortType,
		'createdAt',
		'title',
		queryParams?.sortType === 'highestCbAmount' ? 'offerAmount' : 'offerPercent'
	)
	const offerQuery = buildOfferQuery(queryParams.offerType, 'couponCode')

	let userTypeQuery: OrQuery<UserTypeQuery> = {}
	if (queryParams?.userType) {
		userTypeQuery = buildUserTypeQuery(queryParams.userType, 'userType')
	}

	const aggregationPipeline = []

	if (queryParams?.storeId) {
		// Match stage before Offer lookup
		aggregationPipeline.push({
			$match: {
				store: new mongoose.Types.ObjectId(queryParams.storeId),
			},
		})
	}

	// Conditionally include subCategory Ids match
	if (subCategoryIds && subCategoryIds.length > 0) {
		const objectIdSubCategoryIds = subCategoryIds.map(id => new Types.ObjectId(id))
		aggregationPipeline.push({
			$match: {
				'categories.subCategories': { $in: objectIdSubCategoryIds },
			},
		})
	}

	//
	// Match stage
	aggregationPipeline.push({
		$match: {
			dateExpiry: { $gt: convertLocalToUTCDate() },
			active: true,
			...offerQuery,

			// ...userTypeQuery,
			...(userTypeQuery ? userTypeQuery : {}),

			// Conditionally include title search if searchParam is a non-empty string
			...(searchParam && typeof searchParam === 'string' && searchParam.trim() !== ''
				? { title: { $regex: searchParam, $options: 'i' } }
				: {}),
		},
	})

	// Lookup stage to populate "store" field
	aggregationPipeline.push({
		$lookup: {
			from: 'stores', // Assuming the collection name is 'stores'
			localField: 'store', // Assuming the field containing store ID is 'storeId'
			foreignField: '_id',
			as: 'store',
		},
	})

	// Unwind the "store" array to get individual documents
	// without unwind the store.example value are being returned as element in an array- research
	aggregationPipeline.push({
		$unwind: '$store',
	})

	aggregationPipeline.push({
		$match: {
			'store.active': true, // Only keep offers where store.active is true
		},
	})

	// Add a facet stage to get both documents and total count
	aggregationPipeline.push({
		$facet: {
			documents: [
				{ $sort: sortQuery },
				{ $skip: skip },
				{ $limit: queryParams.pageSize },
				{
					$project: {
						_id: 0,
						uid: 1,
						productImage: '$productImage.secureUrl',
						storeLogoUrl: '$store.logo.secureUrl',
						storeName: '$store.name',
						endDate: '$dateExpiry',
						offerTitle: '$title',
						//NOTE - offerCaption needed this level of querying
						offerCaption: {
							$cond: {
								if: {
									$or: [{ $ne: ['$offerAmount', null] }, { $ne: ['$offerPercent', null] }],
								},
								then: {
									$concat: [
										{
											$cond: {
												if: { $eq: ['$offerType', 'upto'] },
												then: 'Up to ',
												else: 'Flat ',
											},
										},
										{
											$cond: {
												if: { $ne: ['$offerAmount', null] },
												then: {
													$concat: ['Rs. ', { $toString: '$offerAmount' }],
												},
												else: '',
											},
										},
										{
											$cond: {
												if: { $ne: ['$offerPercent', null] },
												then: {
													$concat: [{ $toString: '$offerPercent' }, '%'],
												},
												else: '',
											},
										},
										' ',
										{
											$cond: {
												if: { $eq: ['$store.cashbackType', 'cashback'] },
												then: 'Cashback',
												else: 'Reward',
											},
										},
									],
								},
								else: '',
							},
						},
						salePrice: '$itemPrice',
						couponCode: '$couponCode',
						repeatBy: '$repeatBy',
						hideCbTag: { $ifNull: ['$hideCbTag', false] }, // Set default value to false
						isAutoGenerated: { $ifNull: ['$isAutoGenerated', false] }, // Set default value to false
						cashbackType: '$store.cashbackType',
						offerType: '$offerType',
						offerAmount: '$offerAmount',
						offerPercent: '$offerPercent',
						offerUrl: {
							$cond: { if: '$migrated', then: '$url', else: null },
						},
					},
				},
			],
			totalCount: [
				{ $count: 'total' }, // Count total documents
			],
		},
	})

	return aggregationPipeline
}

export function buildOngoingOfferAggregateQuery(
	queryParams: OngoingSalesDto,
	subCategoryIds: string[]
) {
	const page = queryParams.page ?? 1
	const skip = (page - 1) * queryParams.pageSize

	const searchParam = queryParams?.searchParam ?? ''
	const sortQuery = buildSortQuery(
		queryParams.sortType,
		'offers.createdAt',
		'offers.title',
		queryParams?.sortType === 'highestCbAmount' ? 'offers.offerAmount' : 'offers.offerPercent'
	)
	const offerQuery = buildOfferQuery(queryParams.offerType, 'offers.couponCode')
	const saleIdArray = queryParams.saleIdArray

	const aggregationPipeline = []

	// Match stage before Offer lookup
	aggregationPipeline.push({
		$match: {
			...(saleIdArray && saleIdArray.length > 0 ? { uid: { $in: saleIdArray } } : {}),
		},
	})

	// Lookup stage to populate "offers" field
	aggregationPipeline.push({
		$lookup: {
			from: 'offers', // Assuming the collection name is 'offers'
			localField: 'offers', // Assuming the field containing offer IDs is 'offers'
			foreignField: '_id',
			as: 'offers',
		},
	})

	// Unwind the "offers" array to get individual offer documents
	aggregationPipeline.push({
		$unwind: '$offers',
	})

	// Group stage to group by unique UID of the offers
	aggregationPipeline.push({
		$group: {
			_id: '$offers.uid',
			document: { $first: '$$ROOT' }, // Preserve the entire document for each unique UID
		},
	})

	// Project stage to extract the preserved documents
	aggregationPipeline.push({
		$replaceRoot: {
			newRoot: '$document',
		},
	})

	// Conditionally include subCategory Ids match
	if (subCategoryIds && subCategoryIds.length > 0) {
		const objectIdSubCategoryIds = subCategoryIds.map(id => new Types.ObjectId(id))
		aggregationPipeline.push({
			$match: {
				'offers.categories.subCategories': { $in: objectIdSubCategoryIds },
			},
		})
	}

	// Match stage after Offer lookup
	aggregationPipeline.push({
		$match: {
			'offers.dateExpiry': { $gt: convertLocalToUTCDate() },
			'offers.active': true,
			...offerQuery,

			// Conditionally include title search if searchParam is a non-empty string
			...(searchParam && typeof searchParam === 'string' && searchParam.trim() !== ''
				? { 'offers.title': { $regex: searchParam, $options: 'i' } }
				: {}),
		},
	})

	//
	// //pagination & sort
	// aggregationPipeline.push(
	//     { $skip: skip },
	//     { $limit: queryParams.pageSize },
	//     { $sort: sortQuery }
	// )

	// Lookup stage to populate "store" field inside "offers" documents
	aggregationPipeline.push({
		$lookup: {
			from: 'stores', // Assuming the collection name is 'stores'
			localField: 'offers.store', // Assuming the field containing store IDs is 'store'
			foreignField: '_id',
			as: 'offers.store',
		},
	})

	// Unwind the "offers.store" array to get individual store documents inside each offer
	aggregationPipeline.push({
		$unwind: '$offers.store',
	})

	// Add a facet stage to get both documents and total count
	aggregationPipeline.push({
		$facet: {
			// Stage to process documents
			documents: [
				{ $sort: sortQuery }, // Sort stage for documents
				{ $skip: skip }, // Skip documents if you're implementing pagination
				{ $limit: queryParams.pageSize }, // Limit documents if you're implementing pagination

				{
					// Project stage to shape the output documents
					$project: {
						_id: 0,
						uid: '$offers.uid',
						saved: true,
						productImage: '$offers.productImage.secureUrl',
						storeLogoUrl: '$offers.store.logo.secureUrl',
						storeName: '$offers.store.name',
						hideCbTag: '$offers.hideCbTag',
						// offerWarning: '$offers.store.offerWarning',
						endDate: {
							$dateToString: {
								format: '%Y-%m-%d',
								date: '$offers.dateExpiry',
							},
						},
						offerTitle: '$offers.title',
						offerCaption: {
							$cond: {
								if: {
									$or: [
										{ $ne: ['$offers.offerAmount', null] },
										{ $ne: ['$offers.offerPercent', null] },
									],
								},
								then: {
									$concat: [
										{
											$cond: {
												if: { $eq: ['$offers.offerType', 'upto'] },
												then: 'Up to ',
												else: 'Flat ',
											},
										},
										{
											$cond: {
												if: { $ne: ['$offers.offerAmount', null] },
												then: {
													$concat: ['Rs. ', { $toString: '$offers.offerAmount' }],
												},
												else: '',
											},
										},
										{
											$cond: {
												if: { $ne: ['$offers.offerPercent', null] },
												then: {
													$concat: [{ $toString: '$offers.offerPercent' }, '%'],
												},
												else: '',
											},
										},
										' ',
										{
											$cond: {
												if: { $eq: ['$offers.store.cashbackType', 'cashback'] },
												then: 'Cashback',
												else: 'Reward',
											},
										},
									],
								},
								else: '',
							},
						},
						salePrice: '$offers.itemPrice',
						saleLogoUrl: '$saleLogo.secureUrl',
						couponCode: '$offers.couponCode',
						repeatBy: '$offers.repeatBy',
						saleCaption: '$saleName',
						offerUrl: {
							$cond: {
								if: '$offers.migrated',
								then: '$offers.url',
								else: null,
							},
						},
					},
				},
				{ $sort: sortQuery },
			],
			totalCount: [
				{ $count: 'total' }, // Count total documents
			],
		},
	})

	return aggregationPipeline
}

export function buildSavedOfferAggregateQuery(
	queryParams: CouponsAndDealsDto,
	userId: Types.ObjectId,
	type: string
) {
	//Filter items
	const sortQuery = buildSortQuery(
		queryParams.sortType,
		'offer.createdAt',
		'offer.title',
		queryParams?.sortType === 'highestCbAmount' ? 'offer.offerAmount' : 'offer.offerPercent'
	)
	const userTypeQuery = buildUserTypeQuery(queryParams.userType, 'offer.userType')
	const subCategoriesArray = queryParams.subCategoriesArray
	const searchParam = queryParams?.searchParam ?? ''

	const skip = (queryParams.page - 1) * queryParams.pageSize

	const aggregationPipeline = []

	// Match stage to filter user's deal documents based on user _id
	aggregationPipeline.push({
		$match: {
			type: 'offer',
			user: userId,
		},
	})

	// Lookup stage to populate "offers" field
	aggregationPipeline.push({
		$lookup: {
			from: 'offers', // Assuming the collection name is 'offers'
			localField: 'item', // Assuming the field containing offer IDs is 'offers'
			foreignField: '_id',
			as: 'offer',
		},
	})

	// Unwind the "offers" array to get individual offer documents
	aggregationPipeline.push({
		$unwind: '$offer',
	})

	// Lookup stage to populate "store" field inside "offers" documents
	aggregationPipeline.push({
		$lookup: {
			from: 'stores', // Assuming the collection name is 'stores'
			localField: 'offer.store', // Assuming the field containing store IDs is 'store'
			foreignField: '_id',
			as: 'offer.store',
		},
	})

	// Unwind the "offers.store" array to get individual store documents inside each offer
	aggregationPipeline.push({
		$unwind: '$offer.store',
	})

	// Match stage to filter offers based on your criteria
	aggregationPipeline.push({
		$match: {
			//TO DO -add user type query after the schema has it.
			...userTypeQuery,

			// Conditionally include subCategories match
			...(subCategoriesArray && subCategoriesArray.length > 0
				? {
						'offer.categories.subCategories.uid': {
							$in: subCategoriesArray,
						},
					}
				: {}),

			// Conditionally include title search if searchParam is a non-empty string
			...(searchParam && typeof searchParam === 'string' && searchParam.trim() !== ''
				? { 'offer.title': { $regex: searchParam, $options: 'i' } } //i -> case insensitive
				: {}),
		},
	})

	if (type === 'coupon') {
		aggregationPipeline.push({
			$match: {
				'offer.couponCode': { $exists: true, $ne: '' },
			},
		})
	}

	// Unwind the "offers" array to get individual offer documents
	aggregationPipeline.push({
		$unwind: '$offer',
	})

	// Add a facet stage to get both documents and total count
	aggregationPipeline.push({
		$facet: {
			// Stage to process documents
			documents: [
				{ $sort: sortQuery },
				{ $skip: skip }, // Skip documents if you're implementing pagination
				{ $limit: queryParams.pageSize }, // Limit documents if you're implementing pagination

				{
					// Project stage to shape the output documents
					$project: {
						_id: 0,
						uid: '$offer.uid',
						productImage: '$offer.productImage.secureUrl',
						storeLogoUrl: '$offer.store.logo.secureUrl',
						storeName: '$offer.store.name',
						endDate: {
							$dateToString: {
								format: '%Y-%m-%d',
								date: '$offer.dateExpiry',
							},
						},
						offerTitle: '$offer.title',
						couponCode: '$offer.couponCode',
						repeatBy: '$offer.repeatBy',
						offerCaption: {
							$cond: {
								if: {
									$or: [
										{ $ne: ['$offer.offerAmount', null] },
										{ $ne: ['$offer.offerPercent', null] },
									],
								},
								then: {
									$concat: [
										{
											$cond: {
												if: { $eq: ['$offer.offerType', 'upto'] },
												then: 'Up to ',
												else: 'Flat ',
											},
										},
										{
											$cond: {
												if: { $ne: ['$offer.offerAmount', null] },
												then: {
													$concat: ['Rs. ', { $toString: '$offer.offerAmount' }],
												},
												else: '',
											},
										},
										{
											$cond: {
												if: { $ne: ['$offer.offerPercent', null] },
												then: {
													$concat: [{ $toString: '$offer.offerPercent' }, '%'],
												},
												else: '',
											},
										},
										' ',
										{
											$cond: {
												if: { $eq: ['$offer.store.cashbackType', 'cashback'] },
												then: 'Cashback',
												else: 'Reward',
											},
										},
									],
								},
								else: '',
							},
						},
						salePrice: '$offer.itemPrice',
						offerUrl: {
							$cond: {
								if: '$offer.migrated',
								then: '$offer.url',
								else: null,
							},
						},
					},
				},
				{
					$addFields: {
						saved: true,
					},
				},
			],
			totalCount: [
				{ $count: 'total' }, // Count total documents
			],
		},
	})

	return aggregationPipeline
}

export function buildSingleOfferAndSimilarOffers(uid: number, storeId: Types.ObjectId) {
	const aggregatePipeline = []
	aggregatePipeline.push({
		$match: {
			store: storeId,
			dateExpiry: { $gt: convertLocalToUTCDate() },
			active: true,
		},
	})
	aggregatePipeline.push({ $match: { uid: { $ne: uid } } })
	aggregatePipeline.push({
		$lookup: {
			from: 'stores',
			localField: 'store',
			foreignField: '_id',
			as: 'store',
		},
	})
	aggregatePipeline.push({
		$project: {
			uid: 1,
			offerTitle: '$title',
			productImage: '$productImage.secureUrl',
			storeLogoUrl: { $arrayElemAt: ['$store.logo.secureUrl', 0] },
			storeName: { $arrayElemAt: ['$store.name', 0] },
			endDate: '$dateExpiry',
			offerCaption: {
				$cond: {
					if: {
						$or: [{ $ne: ['$offerAmount', null] }, { $ne: ['$offerPercent', null] }],
					},
					then: {
						$concat: [
							{
								$cond: {
									if: { $eq: ['$offerType', 'upto'] },
									then: 'Up to ',
									else: 'Flat ',
								},
							},
							{
								$cond: {
									if: { $ne: ['$offerAmount', null] },
									then: {
										$concat: ['Rs. ', { $toString: '$offerAmount' }],
									},
									else: '',
								},
							},
							{
								$cond: {
									if: { $ne: ['$offerPercent', null] },
									then: {
										$concat: [{ $toString: '$offerPercent' }, '%'],
									},
									else: '',
								},
							},
							' ',
							{
								$cond: {
									if: { $eq: ['$store.cashbackType', 'cashback'] },
									then: 'Cashback',
									else: 'Reward',
								},
							},
						],
					},
					else: '',
				},
			},
			salePrice: '$itemPrice',
			offerPercentage: '$offerPercent',
			repeatBy: '$repeatBy',
			couponCode: 1,
		},
	})

	return aggregatePipeline
}

interface OfferDetails {
	offerType: 'upto' | 'flat' // Restrict to specific string values
	offerAmount?: number // Optional, as it might not always be provided
	offerPercent?: number // Optional, as it might not always be provided
	cashbackType: 'cashback' | 'reward' // Restrict to specific string values
}

export function generateOfferCaption({
	offerType,
	offerAmount,
	offerPercent,
	cashbackType,
}: OfferDetails): string {
	// Return an empty string if both offerAmount and offerPercent are missing
	if (!(offerAmount || offerPercent)) {
		return ''
	}

	const offerTypeText = offerType === 'upto' ? 'Up to' : 'Flat'
	const offerAmountText = offerAmount ? `Rs. ${offerAmount}` : ''
	const offerPercentText = offerPercent ? `${offerPercent}%` : ''
	const cashbackTypeText = cashbackType === 'cashback' ? 'Cashback' : 'Reward'

	return `${offerTypeText} ${offerAmountText}${offerPercentText} ${cashbackTypeText}`
}
