import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { User, UserDocument } from '../entities/user.entity'

@Injectable()
export class EmailNormalizationService {
	private readonly logger = new Logger(EmailNormalizationService.name)

	constructor(
		@InjectModel(User.name)
		private userModel: Model<UserDocument>
	) {}

	/**
	 * Normalize email address for storage and comparison
	 * Handles Gmail dot notation and + aliases
	 */
	normalizeEmail(email: string): string {
		if (!email) {
			return ''
		}

		const [local, domain] = email.toLowerCase().split('@')

		if (local && domain === 'gmail.com') {
			// Remove dots and everything after + for Gmail
			const cleanLocal = local.replace(/\./g, '').split('+')[0]
			return `${cleanLocal}@${domain}`
		}

		return email.toLowerCase()
	}

	/**
	 * Check if two emails are the same after normalization
	 * Useful for detecting Gmail aliases
	 */
	areEmailsEquivalent(email1: string, email2: string): boolean {
		return this.normalizeEmail(email1) === this.normalizeEmail(email2)
	}

	/**
	 * Check if a normalized email already exists in the database
	 */
	async isNormalizedEmailTaken(email: string): Promise<boolean> {
		const normalizedEmail = this.normalizeEmail(email)
		const existingUser = await this.userModel
			.findOne({
				normalizedEmail,
			})
			.exec()

		return !!existingUser
	}

	/**
	 * Check if an email conflicts with existing users (including aliases)
	 */
	async checkForEmailConflicts(
		email: string,
		excludeUserId?: string
	): Promise<{
		hasConflict: boolean
		conflictingUser?: UserDocument
		conflictType?: 'exact' | 'alias'
	}> {
		const normalizedEmail = this.normalizeEmail(email)

		const query: any = { normalizedEmail }
		if (excludeUserId) {
			query._id = { $ne: excludeUserId }
		}

		const conflictingUser = await this.userModel.findOne(query).exec()

		if (conflictingUser) {
			const conflictType = conflictingUser.email === email ? 'exact' : 'alias'
			return {
				hasConflict: true,
				conflictingUser,
				conflictType,
			}
		}

		return { hasConflict: false }
	}

	/**
	 * Find user by email (checks both original and normalized)
	 */
	async findUserByEmail(email: string): Promise<UserDocument | null> {
		const normalizedEmail = this.normalizeEmail(email)

		// First try to find by normalized email (most reliable)
		let user = await this.userModel.findOne({ normalizedEmail }).exec()

		// Fallback to case-insensitive search on original email for backward compatibility
		if (!user) {
			user = await this.userModel
				.findOne({
					email: { $regex: new RegExp(`^${email}$`, 'i') },
				})
				.exec()
		}

		return user
	}

	/**
	 * Validate email for registration/update
	 */
	async validateEmailForRegistration(email: string): Promise<{
		isValid: boolean
		error?: string
		normalizedEmail: string
	}> {
		const normalizedEmail = this.normalizeEmail(email)

		if (!email?.includes('@')) {
			return {
				isValid: false,
				error: 'Invalid email format',
				normalizedEmail,
			}
		}

		const conflict = await this.checkForEmailConflicts(email)
		if (conflict.hasConflict) {
			const errorMessage =
				conflict.conflictType === 'exact'
					? 'User already exists with this email'
					: 'User already exists with this email address (Gmail alias detected)'

			return {
				isValid: false,
				error: errorMessage,
				normalizedEmail,
			}
		}

		return {
			isValid: true,
			normalizedEmail,
		}
	}

	/**
	 * Check if user is trying to refer themselves using aliases
	 */
	async checkSelfReferralByAlias(userEmail: string, referrerEmail: string): Promise<boolean> {
		if (!(userEmail && referrerEmail)) {
			return false
		}

		return this.areEmailsEquivalent(userEmail, referrerEmail)
	}

	/**
	 * Get original email for delivery purposes
	 * Preserves the original email format for email delivery
	 */
	getEmailForDelivery(user: UserDocument): string {
		return user.email // Always use original email for delivery
	}

	/**
	 * Migrate existing user to add normalized email
	 */
	async migrateUserNormalizedEmail(userId: string): Promise<boolean> {
		try {
			const user = await this.userModel.findById(userId).exec()
			if (!user) {
				return false
			}

			const normalizedEmail = this.normalizeEmail(user.email)

			// Check if normalized email would conflict with another user
			const conflict = await this.checkForEmailConflicts(user.email, userId)
			if (conflict.hasConflict) {
				this.logger.warn(
					`Migration conflict for user ${userId}: ${user.email} conflicts with ${conflict.conflictingUser?.email}`
				)
				return false
			}

			await this.userModel.updateOne({ _id: userId }, { $set: { normalizedEmail } })

			return true
		} catch (error) {
			this.logger.error(`Failed to migrate user ${userId}:`, error)
			return false
		}
	}
}
