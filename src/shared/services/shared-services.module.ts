import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { User, UserSchema } from '../entities/user.entity'
import { EmailNormalizationService } from './email-normalization.service'

@Module({
	imports: [MongooseModule.forFeature([{ name: User.name, schema: UserSchema }])],
	providers: [EmailNormalizationService],
	exports: [EmailNormalizationService],
})
export class SharedServicesModule {}
