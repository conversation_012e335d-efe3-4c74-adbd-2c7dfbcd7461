import { Injectable } from '@nestjs/common'
import { env } from 'config'
import { InjectRazorpay } from 'nestjs-razorpay'
import Razorpay from 'razorpay'
import { validatePaymentVerification } from 'razorpay/dist/utils/razorpay-utils'

export type OrderData = {
	amount: number
	currency: 'INR' | 'USD' | 'AED' | 'EUR'
	receipt: string
	// biome-ignore lint/suspicious/noExplicitAny: required for external API
	notes: { [key: string]: any }
}

@Injectable()
export class RazorpayService {
	constructor(@InjectRazorpay() private readonly razorpayClient: Razorpay) {}

	async createOrder(orderDta: OrderData) {
		try {
			const order = await this.razorpayClient.orders.create(orderDta)
			return order
		} catch (error) {
			throw new Error(error)
		}
	}

	async capturePayment(paymentId: string, amount: number, currency: string) {
		try {
			const payment = await this.razorpayClient.payments.capture(paymentId, amount, currency)
			return payment
		} catch (error) {
			throw new Error(error)
		}
	}

	async verifyPaymentSignature(orderId: string, paymentId: string, signature: string) {
		return validatePaymentVerification(
			{
				// biome-ignore lint/style/useNamingConvention: required for external API
				order_id: orderId,
				// biome-ignore lint/style/useNamingConvention: required for external API
				payment_id: paymentId,
			},
			signature,
			env.RAZORPAY.keySecret
		)
	}
}
