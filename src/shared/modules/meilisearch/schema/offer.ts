interface Store {
	name: string
}

interface Category {
	name: string
}

export class OfferSchema {
	type: string
	id: string
	uid: number
	active: boolean
	title: string
	url: string
	caption: string
	description: string
	offerPercent: number // Assuming a numerical value
	offerAmount: number // Assuming a numerical value
	offerWarning: string
	name: string
	categories: Category[]
	subcategories: Category[]
	storecategories: Category[]
	couponCode: string
	offerType: string
	newUserOffer: string
	oldUserOffer: string
	dateExpiry: number // we need update it as number to filter

	constructor({
		id,
		uid,
		active,
		title,
		url,
		offerType,
		description,
		caption,
		couponCode,
		offerPercent,
		offerAmount,
		offerWarning,
		name,
		dateExpiry,
		categories,
		subcategories,
		newUserOffer,
		oldUserOffer,
		storecategories,
	}: {
		id: string
		uid: number
		active: boolean
		title: string
		url: string
		caption: string
		offerType: string
		description: string
		couponCode: string
		offerPercent: number
		offerAmount: number
		offerWarning: string
		newUserOffer: string
		oldUserOffer: string
		name: string
		dateExpiry: number
		categories: Category[]
		subcategories: Category[]
		storecategories: Category[]
	}) {
		this.type = 'offer'
		this.id = id
		this.uid = uid
		this.active = active
		this.title = title
		this.url = url
		this.caption = caption
		this.description = description
		this.offerPercent = offerPercent
		this.offerAmount = offerAmount
		this.offerWarning = offerWarning
		this.name = name
		this.categories = categories
		this.subcategories = subcategories
		this.storecategories = storecategories
		this.couponCode = couponCode
		this.offerType = offerType
		this.newUserOffer = newUserOffer
		this.oldUserOffer = oldUserOffer
		this.dateExpiry = dateExpiry
	}
}
