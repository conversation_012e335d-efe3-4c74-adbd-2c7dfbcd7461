interface Category {
	name: string
}

export class GiftCardSchema {
	type: string // Ensure this property always has the value "giftCard"
	id: string // Assuming the ID is a string
	uid: number
	url: string
	active: boolean
	name: string
	description: string
	cashbackGiving: number
	relatedInstantStore: string
	categories: Category[]
	subcategories: Category[]
	storecategories: Category[]

	constructor({
		id,
		uid,
		url,
		active,
		name,
		description,
		cashbackGiving,
		relatedInstantStore,
		categories,
		subcategories,
		storecategories,
	}: {
		id: string
		uid: number
		url: string
		active: boolean
		name: string
		description: string
		cashbackGiving: number
		relatedInstantStore: string
		categories: Category[]
		subcategories: Category[]
		storecategories: Category[]
	}) {
		this.type = 'giftCard'
		this.id = id
		this.uid = uid
		this.url = url
		this.active = active
		this.name = name
		this.description = description
		this.cashbackGiving = cashbackGiving
		this.relatedInstantStore = relatedInstantStore
		this.categories = categories
		this.subcategories = subcategories
		this.storecategories = storecategories
	}
}
