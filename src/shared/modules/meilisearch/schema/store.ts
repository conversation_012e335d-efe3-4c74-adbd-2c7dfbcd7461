interface Category {
	name: string
}

export class StoreSchema {
	type: string
	id: string
	uid: number
	active: boolean
	url: string
	name: string
	description: string
	detailedDescription: string
	offerWarning: string
	giftCard: string
	categories: Category[]
	subcategories: Category[]
	storecategories: Category[]

	constructor({
		id,
		uid,
		active,
		url,
		name,
		description,
		detailedDescription,
		offerWarning,
		giftCard,
		categories,
		subcategories,
		storecategories,
	}: {
		id: string
		uid: number
		active: boolean
		url: string
		name: string
		description: string
		detailedDescription: string
		offerWarning: string
		giftCard: string
		categories: Category[]
		subcategories: Category[]
		storecategories: Category[]
	}) {
		this.type = 'store'
		this.id = id
		this.uid = uid
		this.active = active
		this.url = url
		this.name = name
		this.description = description
		this.detailedDescription = detailedDescription
		this.offerWarning = offerWarning
		this.giftCard = giftCard
		this.categories = categories
		this.subcategories = subcategories
		this.storecategories = storecategories
	}
}
