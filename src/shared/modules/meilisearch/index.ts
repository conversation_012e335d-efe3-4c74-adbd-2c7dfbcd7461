import { MeiliSearchResponse } from 'app/context/search/search.types'
import { env } from 'config'
import { Index, MeiliSearch } from 'meilisearch'

export class MeiliSearchService {
	indexName: string
	client: MeiliSearch
	constructor() {
		this.indexName = env.MEILISEARCH.name
		this.client = new MeiliSearch({
			host: env.MEILISEARCH.url,
			apiKey: env.MEILISEARCH.key,
		})
	}

	async checkOrCreateIndex(indexName: string): Promise<Index> {
		try {
			const index = await this.client.getIndex(indexName)
			if (index && index.uid === indexName) {
				return index as Index
			}
		} catch (_error) {
			await this.client.createIndex(indexName, {
				primaryKey: 'id',
			})
			const newIndex = await this.client.getIndex(indexName)
			await newIndex.updateSearchableAttributes([
				'type',
				'id',
				'uid',
				'active',
				'name',
				'description',
				'detailedDescription',
				'offerWarning',
				'giftCard',
				'categories',
				'subcategories',
				'storecategories',
				'cashbackGiving',
				'relatedInstantStore',
				'title',
				'offerType',
				'couponCode',
				'offerPercent',
				'offerAmount',
				'store',
				'dateExpiry',
			])
			await new Promise(resolve => setTimeout(resolve, 15_000))
			return newIndex as Index
		}
		return undefined as unknown as Index
	}

	async deleteAllIndexes() {
		const indexes = await this.client.getIndexes()
		for (const index of indexes.results) {
			await index.delete()
		}
	}

	// biome-ignore lint/suspicious/noExplicitAny: required for external API
	async addDocuments(documents: any) {
		const index = await this.checkOrCreateIndex(this.indexName)
		const response = await index.addDocuments(documents)
		return response
	}

	// biome-ignore lint/suspicious/noExplicitAny: required for external API
	async updateDocuments(documents: any) {
		const index = await this.checkOrCreateIndex(this.indexName)
		const response = await index.updateDocuments([documents])
		return response
	}

	// biome-ignore lint/suspicious/noExplicitAny: required for external API
	async deleteDocuments(documents: any) {
		const index = await this.checkOrCreateIndex(this.indexName)
		const response = await index.deleteDocuments([documents])
		return response
	}

	async searchDocuments(query: string) {
		const index = await this.checkOrCreateIndex(this.indexName)
		const response = await index.search<MeiliSearchResponse>(query, {
			limit: 1000,
		})
		return response
	}
}
