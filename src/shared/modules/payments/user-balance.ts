import { NotAcceptableException } from '@nestjs/common'
import { OrderGiftCardDto } from 'app/gift-card/dto/order-giftcard.dto'
import { UserService } from 'app/user/user.service'
import { ReqUser } from 'shared/entities'
import { PaymentType } from 'shared/enums'
import { PaymentProcessor } from './payment-processor'

export class BalancePaymentProcessor implements PaymentProcessor {
	type: PaymentType = 'balance'
	constructor(private userService: UserService) {}

	async processPayment(
		user: ReqUser,
		amount: number,
		orderData: OrderGiftCardDto
	): Promise<{
		amount: number
		order: null
		status: 'success'
	}> {
		const userBalance = await this.userService.getUserBalance(user.id)
		const amountToLock = Math.min(userBalance.balance, amount)
		const onlyBalanceTransaction = orderData.paymentMethods.length === 1

		if (
			(amountToLock > 0 && !onlyBalanceTransaction) ||
			(onlyBalanceTransaction && amountToLock === amount)
		) {
			await this.userService.lockUserBalance(user.id, amountToLock)
			return {
				amount: amountToLock,
				order: null,
				status: 'success',
			}
		}
		throw new NotAcceptableException('Insufficient balance')
	}
}
