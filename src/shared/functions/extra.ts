/**
 * @deprecated Use EmailNormalizationService.normalizeEmail() instead
 * Legacy function for backward compatibility
 */
export function normalizeGmail(email: string): string {
	const [local, domain] = email.toLowerCase().split('@')
	return domain === 'gmail.com' ? `${local?.replace(/\./g, '')}@${domain}` : email.toLowerCase()
}

/**
 * Enhanced email normalization that handles Gmail aliases properly
 */
export function normalizeEmail(email: string): string {
	if (!email) {
		return ''
	}

	const [local, domain] = email.toLowerCase().split('@')

	if (domain === 'gmail.com' && local) {
		// Remove dots and everything after + for Gmail
		const cleanLocal = local.replace(/\./g, '').split('+')[0]
		return `${cleanLocal}@${domain}`
	}

	return email.toLowerCase()
}

export function isFakeGmailAlias(newMail: string, existing: string): boolean {
	const normalize = (email: string): string => {
		const [local, domain] = email.toLowerCase().split('@')
		if (domain === 'gmail.com') {
			return `${local?.replace(/\./g, '').split('+')[0]}@${domain}`
		}
		return email.toLowerCase()
	}

	return normalize(newMail) === normalize(existing)
}
