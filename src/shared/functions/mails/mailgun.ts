/* cSpell:disable */
// import { env } from 'config'
// import FormData = require('form-data')
// import fs from 'node:fs'
// import path from 'node:path'
// import Mailgun from 'mailgun.js'

// type MailPayload = {
// 	to: string
// 	subject: string
// 	text: string
// }

export type GifCardPayload = {
	cardName: string
	giftCard: number
	pin: number
	amount: number
	expiryDate: Date
	name: string
	email: string
	msg: string
}

export type OtpVerificationPayload = {
	name: string
	otp: number
	to: string
}

// export async function sendMail(payload: MailPayload) {
// 	const mailgun = new Mailgun(FormData)
// 	const client = mailgun.client({ username: 'api', key: env.MAIL.mailgunApiKey })

// 	try {
// 		const res = await client.messages.create(env.MAIL.domain, payload)
// 		console.log('🚀 ~ sendMail ~ res:', res)
// 		return res
// 	} catch (error) {
// 		console.log(error)
// 	}
// }

// await sendOtpMail({ otp: 1234, to: '<EMAIL>' })

// export async function sendGiftCardMail(payload: GifCardPayload) {
// 	const messageData = {
// 		from: `IndianCashback <${env.MAIL.from}>`,
// 		to: payload.email,
// 		subject: 'Your Gift Card From Indiancashback',
// 		text: `Here is your gift card: ${payload.giftCard}`,
// 		html: `<!DOCTYPE html>
//         <html lang="en">
//         <head>
//             <title>Your Gift Card</title>
//             <meta charset="UTF-8" />
//             <meta name="viewport" content="width=device-width" />
//             <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;800&display=swap" rel="stylesheet" />
//         </head>
//         <body>
//             <div>
//                 <h1>Here is your gift card: ${payload.giftCard}</h1>
//                 <p>Card Name: ${payload.cardName}</p>
//                 <p>Pin: ${payload.pin}</p>
//                 <p>Amount: ${payload.amount}</p>
//                 <p>Expiry Date: ${payload.expiryDate}</p>
//                 <p>Name: ${payload.name}</p>
//                 <p>Email: ${payload.email}</p>
//                 <p>Message: ${payload.msg}</p>
//             </div>
//         </body>
//         </html>`,
// 	}
// 	return sendMail(messageData)
// }

// export async function sendOtpVerificationMail(payload: OtpVerificationPayload) {
// 	const htmlFilePath = path.resolve(__dirname, 'login-otp.html')
// 	// const htmlFilePath = path.resolve(__dirname, "login-otp.html");
// 	const htmlTemplate = fs.readFileSync(htmlFilePath, 'utf-8')

// 	const htmlContent = htmlTemplate
// 		.replace('{{name}}', payload.name)
// 		.replace('{{otp}}', payload.otp.toString())

// 	const messageData = {
// 		from: `IndianCashback <${process.env.MAILGUN_FROM}>`,
// 		to: payload.to,
// 		subject: 'Login OTP',
// 		text: `Your OTP is ${payload.otp.toString()}`,
// 		html: htmlContent,
// 	}
// 	return sendMail(messageData)
// }
