import fs from 'node:fs'
import path from 'node:path'
import {
	SendSmtpEmail,
	TransactionalEmailsApi,
	TransactionalEmailsApiApiKeys,
} from '@getbrevo/brevo'
import { env } from 'config'

type MailPayload = {
	to: string
	subject: string
	text: string
}

export type GifCardPayload = {
	cardName: string
	giftCard: number
	pin: number
	amount: number
	expiryDate: Date
	name: string
	email: string
	msg: string
}

export type OtpVerificationPayload = {
	name: string
	otp: number
	to: string
}

const apiInstance = new TransactionalEmailsApi()
apiInstance.setApiKey(TransactionalEmailsApiApiKeys.apiKey, env.MAIL.brevoApiKey)

export async function sendMail(payload: MailPayload) {
	const sendSmtpEmail = new SendSmtpEmail()

	sendSmtpEmail.subject = payload.subject
	sendSmtpEmail.htmlContent = payload.text // You can modify this to accept HTML content
	sendSmtpEmail.sender = {
		name: 'IndianCashback',
		email: env.MAIL.from,
	}
	sendSmtpEmail.to = [{ email: payload.to }]

	const { response } = await apiInstance.sendTransacEmail(sendSmtpEmail)
	return response
}

export async function sendGiftCardMail(payload: GifCardPayload) {
	const htmlContent = `<!DOCTYPE html>
				<html lang="en">
				<head>
						<title>Your Gift Card</title>
						<meta charset="UTF-8" />
						<meta name="viewport" content="width=device-width" />
						<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;800&display=swap" rel="stylesheet" />
				</head>
				<body>
						<div>
								<h1>Here is your gift card: ${payload.giftCard}</h1>
								<p>Card Name: ${payload.cardName}</p>
								<p>Pin: ${payload.pin}</p>
								<p>Amount: ${payload.amount}</p>
								<p>Expiry Date: ${payload.expiryDate}</p>
								<p>Name: ${payload.name}</p>
								<p>Email: ${payload.email}</p>
								<p>Message: ${payload.msg}</p>
						</div>
				</body>
				</html>`

	const messageData = {
		to: payload.email,
		subject: 'Your Gift Card From Indiancashback',
		text: htmlContent,
	}

	return sendMail(messageData)
}

export async function sendOtpVerificationMail(payload: OtpVerificationPayload) {
	const htmlFilePath = path.resolve(__dirname, 'login-otp.html')
	const htmlTemplate = fs.readFileSync(htmlFilePath, 'utf-8')

	const htmlContent = htmlTemplate
		.replace('{{name}}', payload.name)
		.replace('{{otp}}', payload.otp.toString())

	const messageData = {
		to: payload.to,
		subject: 'Login OTP',
		text: htmlContent,
	}

	return sendMail(messageData)
}
