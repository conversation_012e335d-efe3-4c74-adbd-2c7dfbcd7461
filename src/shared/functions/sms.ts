import { env } from 'config'

export type OtpSmsPayload = {
	to: number
	otp: number
}

async function sendSmsHelper(payload: {
	to: number
	sms: string
	campaign: string
	templateId: bigint
}): Promise<void> {
	const { to, sms, campaign, templateId } = payload
	const smsUrl = `http://api.msg91.com/api/v2/sendsms?campaign=${campaign}&message=${sms}&authkey=${env.SMS.authKey}&mobiles=${to}&route=${env.SMS.route}&sender=${env.SMS.sender}&country=${env.SMS.country}&DLT_TE_ID=${templateId}`

	try {
		const response = await fetch(smsUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'multipart/form-data',
				'Cache-Control': 'no-cache',
			},
		})

		if (!response.ok) {
			throw new Error(`HTTP error! Status: ${response.status}`)
		}
	} catch (_error) {}
}

export async function smsOtp(payload: OtpSmsPayload): Promise<void> {
	const { to, otp } = payload
	const sms = `${otp} is the OTP to verify your mobile number. -IndianCashback`
	const templateId = 1307161025865621879n
	await sendSmsHelper({ to, sms, templateId, campaign: 'mob_otp' })
}
