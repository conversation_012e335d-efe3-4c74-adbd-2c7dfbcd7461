import { env } from '@config'
import { Logger } from '@nestjs/common'
import { ofetch } from 'ofetch'

interface EmailData {
	[key: string]: any
}

interface SendEmailOptions {
	subscriberEmail: string
	templateId: number
	data: EmailData
}
// t - 5 - Signup Welcome - data:{name,amount}
// t - 6 - Referral Welcome - data:{referrer_name,referred_name,amount}

const logger = new Logger('ListmonkService')

export async function sendTransactionalEmail({
	subscriberEmail,
	templateId,
	data,
}: SendEmailOptions): Promise<void> {
	try {
		logger.log('Sending transactional email', {
			subscriberEmail,
			templateId,
			dataKeys: Object.keys(data),
		})

		await ofetch('/api/tx', {
			method: 'POST',
			// biome-ignore lint/style/useNamingConvention: required for external API
			baseURL: env.LISTMONK.baseUrl,
			headers: {
				'Content-Type': 'application/json',
				// biome-ignore lint/style/useNamingConvention: required for external API
				Authorization: `Basic ${btoa(`${env.LISTMONK.username}:${env.LISTMONK.password}`)}`,
			},
			body: {
				// biome-ignore lint/style/useNamingConvention: required for external API
				subscriber_email: subscriberEmail,
				// biome-ignore lint/style/useNamingConvention: required for external API
				template_id: templateId,
				data,
				// biome-ignore lint/style/useNamingConvention: required for external API
				content_type: 'html',
			},
			onResponseError({ response }) {
				throw new Error(`Failed to send email: ${response._data.message}`)
			},
		})

		logger.log('Transactional email sent successfully', {
			subscriberEmail,
			templateId,
		})
	} catch (error) {
		logger.error('Failed to send transactional email', {
			subscriberEmail,
			templateId,
			dataKeys: Object.keys(data),
			error: error.message,
			stack: error.stack,
		})
		throw error
	}
}

interface SubscriberAttribs {
	[key: string]: string | number | boolean | string[] | Record<string, unknown>
}

interface CreateSubscriberOptions {
	email: string
	name: string
	referralCode: string
	status?: string
}

export async function createSubscriber({
	email,
	name,
	referralCode,
	status,
}: CreateSubscriberOptions) {
	try {
		logger.log('Creating subscriber', {
			email,
			name,
			hasReferralCode: !!referralCode,
			status: status || 'enabled',
		})

		await ofetch('/api/subscribers', {
			method: 'POST',
			// biome-ignore lint/style/useNamingConvention: required for external API
			baseURL: env.LISTMONK.baseUrl,
			headers: {
				'Content-Type': 'application/json',
				// biome-ignore lint/style/useNamingConvention: required for external API
				Authorization: `Basic ${btoa(`${env.LISTMONK.username}:${env.LISTMONK.password}`)}`,
			},
			body: {
				email,
				name,
				referralCode,
				status: status || 'enabled',
			},
			onResponseError({ response }) {
				throw new Error(`Failed to create subscriber: ${response._data.message}`)
			},
		})

		logger.log('Subscriber created successfully', {
			email,
			name,
		})
	} catch (error) {
		logger.error('Failed to create subscriber', {
			email,
			name,
			hasReferralCode: !!referralCode,
			status: status || 'enabled',
			error: error.message,
			stack: error.stack,
		})
		throw error
	}
}
