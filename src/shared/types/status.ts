export const Status = {
	active: 'active',
	inactive: 'inactive',
	blocked: 'blocked',
} as const

export const Status2 = {
	confirmed: 'confirmed',
	pending: 'pending',
	cancelled: 'cancelled',
} as const

export type StatusType = (typeof Status)[keyof typeof Status]
export const Payment = {
	bank: 'Bank',
	upi: 'UPI',
	giftVoucher: 'Gift Voucher',
	recharge: 'Recharge',
	wallet: 'Wallet',
} as const

export type PaymentTypes = (typeof Payment)[keyof typeof Payment]

export const PaymentStatus = {
	paid: 'Paid',
	requested: 'Requested',
	cancelled: 'Cancelled',
} as const

export type PaymentStatusType = (typeof PaymentStatus)[keyof typeof PaymentStatus]
