import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Status, StatusType } from 'shared/types'
import { User } from './user.entity'

export type CookieSessionDocument = HydratedDocument<CookieSession>

@Schema({
	timestamps: true,
})
export class CookieSession {
	@Prop({ required: false })
	ip!: string

	@Prop({ required: false })
	device!: string

	@Prop({ required: false })
	browser!: string

	@Prop({ required: false })
	os!: string

	@Prop({ type: Types.ObjectId, ref: 'User' })
	user!: User | Types.ObjectId

	@Prop({ required: false })
	userAgent!: string

	@Prop({
		type: String,
		enum: Status,
		default: Status.active,
	})
	status!: StatusType

	@Prop({ required: false })
	token?: string
}

export const CookieSessionSchema = SchemaFactory.createForClass(CookieSession)
