import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { User } from './user.entity'

export type BankAccountDetailsDocument = HydratedDocument<BankAccountDetails>

@Schema({ timestamps: true })
export class BankAccountDetails {
	@Prop({ type: Number, required: true, default: 0 })
	uid: number

	@Prop({ type: String, required: true })
	holderName: string

	@Prop({ type: String, required: true })
	bankName: string

	@Prop({ type: String, required: true })
	branchName: string

	@Prop({ type: String, required: true, trim: true })
	accountNumber: string

	@Prop({ type: String, required: true })
	ifsc: string

	@Prop({ type: String })
	upi: string

	@Prop({ type: Boolean, default: true })
	active: boolean

	@Prop({ type: Types.ObjectId, ref: 'User' })
	user!: User
}

export const BankAccountDetailsSchema = SchemaFactory.createForClass(BankAccountDetails)
