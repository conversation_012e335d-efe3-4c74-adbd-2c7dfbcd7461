import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'

@Schema({ timestamps: true })
export class Admin {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: String, required: true })
	name!: string

	@Prop({
		type: String,
		unique: true,
	})
	email!: string

	@Prop({
		type: String,
		required: [true, 'Please enter your password'],
		minlength: [8, 'Your password must be longer than 8 characters'],
		select: false,
	})
	password!: string

	@Prop({ type: String, default: 'admin' })
	role!: string

	@Prop({ type: Date, default: Date.now })
	createdAt!: Date

	@Prop({ type: Boolean, default: false })
	block!: boolean

	@Prop()
	resetPasswordToken?: string

	@Prop()
	resetPasswordExpire?: Date
}
export type AdminDocument = HydratedDocument<Admin>
export const AdminSchema = SchemaFactory.createForClass(Admin)
