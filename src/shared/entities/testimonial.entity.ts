import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model } from 'mongoose'
import { Image, ImageSchema } from './image.entity'

export type TestimonialDocument = HydratedDocument<Testimonial>

@Schema({ timestamps: true })
export class Testimonial {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: String, required: true })
	reviewerName!: string

	@Prop({ type: ImageSchema })
	reviewerAvatar: Image

	@Prop({ type: Number, default: 1, enum: [1, 2, 3, 4, 5], required: true })
	rating: 1 | 2 | 3 | 4 | 5

	@Prop({ type: String })
	review: string

	@Prop({
		type: Boolean,
		default: true,
	})
	active!: boolean

	createdAt: string
}

export const TestimonialSchema = SchemaFactory.createForClass(Testimonial)

TestimonialSchema.pre('save', async function (next) {
	const review = this.constructor as Model<TestimonialDocument>
	const lastDocument = await review.findOne().sort({ _id: -1 })

	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}
	next()
})
