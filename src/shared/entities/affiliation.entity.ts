import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from './admin.entity'

export type AffiliationDocument = HydratedDocument<Affiliation>

@Schema({ timestamps: true })
export class Affiliation {
	@Prop({ type: Number, required: true, default: 0 })
	uid: number

	@Prop({ type: String, required: true })
	name: string

	@Prop({ type: String })
	apiName: string

	@Prop({ type: String })
	apiKey: string

	@Prop({ type: String, required: false })
	accessToken: string

	@Prop({ type: Boolean, default: true })
	active: boolean

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin

	@Prop({ type: Number, default: 0 })
	oldId: number
}

export const AffiliationSchema = SchemaFactory.createForClass(Affiliation)
