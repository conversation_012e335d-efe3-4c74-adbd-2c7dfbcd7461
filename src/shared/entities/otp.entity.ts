import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'

export type OtpDocument = HydratedDocument<Otp>

@Schema({ timestamps: true })
export class Otp {
	@Prop({ type: Number, required: true })
	otp: number

	@Prop({ type: String, required: false })
	email: string

	@Prop({ type: String, enum: ['email', 'mobile'], default: 'email' })
	type: 'email' | 'mobile'

	@Prop({ type: String, required: false })
	userEmail: string

	@Prop({ type: String, required: false })
	mobile: string

	@Prop({ type: Date })
	createdAt!: Date
}
export const OtpSchema = SchemaFactory.createForClass(Otp)

OtpSchema.set('toJSON', {
	transform: (_doc, ret) => {
		ret.id = ret._id // Rename _id to id
		ret._id = undefined
		ret.__v = undefined
	},
})
