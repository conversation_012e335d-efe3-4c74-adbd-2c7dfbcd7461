import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model, Types } from 'mongoose'
import { hash } from 'ohash'
import { Admin } from './admin.entity'
import { Affiliation } from './affiliation.entity'
import { Store } from './store.entity'
import { User } from './user.entity'

export type LinkDocument = HydratedDocument<Link>

@Schema({ timestamps: true })
export class Link {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: String, required: true })
	originalUrl!: string

	@Prop({ type: String, required: true })
	generatedUrl!: string

	@Prop({ type: String, unique: true })
	linkId: string

	@Prop({ type: String, unique: true })
	shortUrl: string

	@Prop({ type: Types.ObjectId, ref: 'User', required: true })
	user!: User

	@Prop({ type: Types.ObjectId, ref: 'Store', required: true })
	store!: Store

	@Prop({ type: Types.ObjectId, ref: 'Affiliation' })
	affiliation?: Affiliation

	@Prop({ type: Number, default: 0 })
	clickCount: number

	@Prop({ type: Boolean, default: true })
	active: boolean

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy?: Admin

	createdAt!: Date
	updatedAt!: Date
}

export const LinkSchema = SchemaFactory.createForClass(Link)

// Auto-increment UID and generate reference ID on save
LinkSchema.pre('save', async function (next) {
	if (!this.isNew) {
		return next()
	}

	const link = this.constructor as Model<LinkDocument>
	const lastDocument = await link.findOne().sort({ uid: -1 }).exec()

	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}

	// Generate a unique link ID if not already set
	if (!this.linkId) {
		this.linkId = `${hash({
			uid: this.uid,
			user: this.user,
			store: this.store,
			url: this.originalUrl,
			createdAt: new Date(),
		})
			.toUpperCase()
			.substring(0, 12)}`
	}

	next()
})
