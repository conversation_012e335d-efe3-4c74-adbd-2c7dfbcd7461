import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { Image, ImageSchema } from './image.entity'
export type QuickAccessDocument = HydratedDocument<QuickAccess>

@Schema({ timestamps: true })
export class QuickAccess {
	@Prop({
		type: Number,
		required: true,
		unique: true,
		index: true,
	})
	uid!: number

	@Prop({ type: String })
	title?: string

	@Prop({ type: String })
	redirectUrl?: string

	@Prop({
		type: ImageSchema,
	})
	icon!: Image

	@Prop({ type: Types.ObjectId, required: true, ref: 'Admin' }) // Assuming 'Admin' is the name of the model for admins
	createdBy!: Admin

	@Prop({
		type: Boolean,
		default: true,
	})
	active!: boolean
}

export const QuickAccessSchema = SchemaFactory.createForClass(QuickAccess)
