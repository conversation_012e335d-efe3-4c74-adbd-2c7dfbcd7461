import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { PaymentType } from 'shared/enums'
import { GiftCard } from './gift-card.entity'
import { PaymentSession } from './payment-session.entity'
import { User } from './user.entity'

@Schema({ _id: false, autoCreate: true })
export class Card {
	@Prop()
	amount!: number

	@Prop()
	quantity!: number
}

export const CardSchema = SchemaFactory.createForClass(Card)

@Schema({ timestamps: true })
export class GiftCardOrder {
	@Prop({ type: Types.ObjectId, ref: 'User', required: true })
	user!: User | Types.ObjectId

	@Prop({ type: Types.ObjectId, ref: 'GiftCard', required: true })
	giftCard!: GiftCard | Types.ObjectId

	@Prop({ type: Number, required: true })
	totalAmount!: number

	@Prop({
		type: [CardSchema],
		required: true,
	})
	cards!: Card[]

	@Prop({ type: Number, required: true, default: 0 })
	icbBalance: number

	@Prop({ type: Number, required: false, default: 0 })
	mobile?: number

	@Prop({ type: String, required: true })
	email: string

	@Prop({ type: String, required: true })
	name: string

	@Prop({ type: [String], required: true })
	paymentType!: PaymentType[]

	@Prop({ type: String, required: false })
	orderId?: string

	@Prop({ type: String, required: false })
	msg?: string

	@Prop({ type: Types.ObjectId, ref: 'PaymentSession' })
	paymentSession!: Types.ObjectId | PaymentSession

	@Prop({ type: Boolean, default: false })
	paymentVerified?: boolean
}

export type GiftCardOrderDocument = HydratedDocument<GiftCardOrder>
export const GiftCardOrderSchema = SchemaFactory.createForClass(GiftCardOrder)
