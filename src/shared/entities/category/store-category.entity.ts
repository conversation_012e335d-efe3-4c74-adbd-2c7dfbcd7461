import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from '../admin.entity'
import { Affiliation } from '../affiliation.entity'
import { Image, ImageSchema } from '../image.entity'
import { Store } from '../store.entity'

@Schema({ timestamps: true })
export class StoreCategory {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number
	@Prop({ type: Types.ObjectId, required: true, ref: 'Store' })
	store!: Store
	@Prop({ type: Types.ObjectId, required: true, ref: 'Affiliation' })
	affiliation!: Affiliation
	@Prop({ type: String })
	name!: string
	@Prop({ type: String })
	description: string
	@Prop({ type: Number })
	oldUserOfferAmount?: number
	@Prop({ type: Number })
	newUserOfferAmount?: number
	@Prop({ type: Number })
	oldUserOfferPercent?: number
	@Prop({ type: Number })
	newUserOfferPercent?: number
	@Prop({ type: String, enum: ['percent', 'amount'] })
	gettingType: 'percent' | 'amount'
	@Prop({ type: String, enum: ['percent', 'amount'] })
	givingType: 'percent' | 'amount'
	@Prop({ type: Number, default: 0 })
	gettingNewUserRate!: number
	@Prop({ type: Number, default: 0 })
	gettingOldUserRate!: number
	@Prop({ type: String })
	sectionLink!: string
	@Prop({ type: String })
	couponCode!: string
	@Prop({ type: String })
	type!: string
	@Prop({ type: String, enum: ['app', 'website'] })
	device!: string
	@Prop({
		type: ImageSchema,
	})
	weeklyImage!: Image
	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin
	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin
	@Prop({ type: Date })
	dateExpiry!: Date
	@Prop({ type: String })
	notes!: string
	@Prop({ type: Boolean, default: true })
	active!: boolean
}

export type StoreCategoryDocument = HydratedDocument<StoreCategory>

export const StoreCategorySchema = SchemaFactory.createForClass(StoreCategory)
