import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from '../admin.entity'
import { Image, ImageSchema } from '../image.entity'
import { CategoryDocument } from './category.entity'

export type SubCategoryDocument = HydratedDocument<SubCategory>

@Schema({ timestamps: true })
export class SubCategory {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({
		type: ImageSchema,
	})
	image!: Image

	@Prop({ default: true })
	active!: boolean

	@Prop({ required: false, default: null })
	name!: string

	@Prop({ required: false, default: null })
	description!: string

	@Prop({
		type: Types.ObjectId,
		ref: 'Category',
		required: false,
		default: null,
	})
	category!: CategoryDocument

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin

	@Prop({ type: Number, default: 0 })
	oldId?: number
}

export const SubCategorySchema = SchemaFactory.createForClass(SubCategory)

SubCategorySchema.index(
	{ name: 'text', description: 'text' },
	{
		weights: {
			name: 5,
			description: 1,
		},
	}
)
