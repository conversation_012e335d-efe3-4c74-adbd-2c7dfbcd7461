import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { CategoryDocument } from './category.entity'
import { SubCategoryDocument } from './sub-category.entity'

@Schema({ _id: false, autoCreate: true })
export class Categories {
	@Prop({ type: Types.ObjectId, ref: 'Category' })
	category: CategoryDocument
	@Prop({ type: [Types.ObjectId], ref: 'SubCategory' })
	subCategories: SubCategoryDocument[]
}

export const CategoriesSchema = SchemaFactory.createForClass(Categories)
