import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { Image, ImageSchema } from './image.entity'
import { Store } from './store.entity'

export type MobileStoryDocument = HydratedDocument<MobileStory>

@Schema({ timestamps: true })
export class MobileStory {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({
		type: ImageSchema,
	})
	image!: Image

	@Prop({ type: Number })
	duration!: number

	@Prop({ type: String })
	title!: string

	@Prop({ type: String })
	description!: string

	@Prop({ type: String })
	buttonText!: string

	@Prop({ type: String })
	redirectUrl!: string

	@Prop({ type: Date, required: true })
	expiryDate!: Date

	@Prop({ type: Types.ObjectId, required: true, ref: 'Admin' }) // Assuming 'Admin' is the name of the model for admins
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, required: true, ref: 'Admin' }) // Assuming 'Admin' is the name of the model for admins
	updatedBy!: Admin

	@Prop({ type: Types.ObjectId, required: true, ref: 'Store' }) // Assuming 'Admin' is the name of the model for admins
	store!: Store

	@Prop({
		type: Boolean,
		default: true,
	})
	active!: boolean
}
export const MobileStorySchema = SchemaFactory.createForClass(MobileStory)
MobileStorySchema.set('toJSON', {
	transform: (_doc, ret) => {
		ret.id = ret._id // Rename _id to id
		ret._id = undefined
		ret.__v = undefined
		ret.createdBy = undefined
	},
})
