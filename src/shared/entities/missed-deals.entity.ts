import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { Offer } from './offer.entity'

export type MissedDealsDocument = HydratedDocument<MissedDeals>

@Schema({ timestamps: true })
export class MissedDeals {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: Types.ObjectId, required: true, ref: 'Offer' })
	cashback!: Offer

	@Prop({ type: Types.ObjectId, required: true, ref: 'Admin' })
	addedBy!: Admin

	@Prop({ type: Number, default: 0 })
	priority?: number
}

export const MissedDealsSchema = SchemaFactory.createForClass(MissedDeals)
