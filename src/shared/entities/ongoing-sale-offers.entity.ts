import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { Image, ImageSchema } from './image.entity'
import { Offer } from './offer.entity'

@Schema({ timestamps: true })
export class OngoingSaleOffers {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: String })
	saleName: string

	@Prop({ type: Date })
	saleStartDate?: Date

	@Prop({ type: Date })
	saleEndDate?: Date

	@Prop({ type: ImageSchema })
	saleLogo?: Image

	@Prop({ type: Boolean, default: true })
	active?: boolean

	@Prop([{ type: Types.ObjectId, required: true, ref: 'Offer' }])
	offers!: Offer[]

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin
}
export type OngoingSaleOffersDocument = HydratedDocument<OngoingSaleOffers>

export const OngoingSaleOffersSchema = SchemaFactory.createForClass(OngoingSaleOffers)
