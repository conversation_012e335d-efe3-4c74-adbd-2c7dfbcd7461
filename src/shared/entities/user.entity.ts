import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose'
import { type HydratedDocument, type Model, Types } from 'mongoose'
import { hash } from 'ohash'
import { convertLocalToUTCDate } from 'shared/helpers/time.helper'
import { Status, type StatusType } from 'shared/types'
import type { Admin } from './admin.entity'
import { type Image, ImageSchema } from './image.entity'
import type { PersonalInterest } from './personal-interest.entity'

// Email normalization function for use in schema
function normalizeEmailForSchema(email: string): string {
	if (!email) {
		return ''
	}

	const [local, domain] = email.toLowerCase().split('@')

	if (domain === 'gmail.com' && local) {
		// Remove dots and everything after + for Gmail
		const cleanLocal = local.replace(/\./g, '').split('+')[0]
		return `${cleanLocal}@${domain}`
	}

	return email.toLowerCase()
}

export type UserDocument = HydratedDocument<User>

export type ReqUser = {
	id: Types.ObjectId
	email: string
}

@Schema({ timestamps: true })
export class User {
	@Prop({ type: Number, required: true, default: 0, unique: true, index: true })
	uid: number

	@Prop()
	name: string

	@Prop({ type: ImageSchema })
	avatar: Image

	@Prop({ unique: true, index: true, required: true })
	email: string

	@Prop({ unique: true, index: true, required: false })
	normalizedEmail: string

	@Prop({
		type: Number,
		unique: true,
		sparse: true,
		required: false,
		set: (v: number | null | undefined) => (v === null ? undefined : v),
	})
	mobile?: number

	@Prop({ type: Types.ObjectId, ref: 'User' })
	referral: User

	@Prop()
	referralCode: string

	@Prop()
	fbUserId: string

	@Prop()
	googleId: string

	@Prop()
	userNotes: string

	@Prop()
	address: string

	@Prop({ type: String })
	postcode: string

	@Prop({ type: Date, default: convertLocalToUTCDate() })
	dateRegistered: Date

	@Prop()
	lastLoggedDate: Date

	@Prop()
	ambassador: string

	@Prop({ default: false })
	rcBlock: boolean

	@Prop({ required: true, default: 0 })
	balance: number

	@Prop({ required: true, default: 0 })
	totalEarned: number

	@Prop({ required: true, default: 0 })
	flipkartRewardPoints: number

	@Prop({ required: true, default: 0 })
	pendingFlipkartRewardPoints: number

	@Prop({ required: true, default: 0 })
	pendingBalance: number

	@Prop({ required: true, default: 0 })
	giftCardBalance: number

	@Prop()
	specialCampaign: string

	@Prop()
	campaignDone: string

	@Prop()
	isWithdrawReminded: string

	@Prop({ type: Date, default: convertLocalToUTCDate() })
	withdrawRemindDate: Date

	@Prop()
	safeUser: string

	@Prop({ default: false })
	mobileVerified: boolean

	@Prop({ default: false })
	sendNotification: boolean

	@Prop({ type: [Types.ObjectId], ref: 'PersonalInterest', default: [] })
	personalInterest?: PersonalInterest[]

	@Prop({ enum: Status, default: Status.inactive, type: String })
	status: StatusType

	@Prop({ default: 0 })
	rewardPoints: number

	@Prop({ default: 0 })
	pendingRewardPoints: number

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy?: Admin

	@Prop({ type: Boolean, default: false })
	migrated?: boolean

	@Prop({ type: Number, default: 0 })
	oldId: number

	@Prop({ required: true, default: 0 })
	countConfirmed: number

	@Prop({ required: true, default: 0 })
	countPending: number

	@Prop({ required: true, default: 0 })
	paidAmount: number

	@Prop({ required: true, default: 0 })
	flipkartConfirmedAmount: number

	@Prop({ required: true, default: 0 })
	countFlipkartConfirmed: number

	@Prop({ required: true, default: 0 })
	flipkartPendingAmount: number

	@Prop({ required: true, default: 0 })
	countFlipkartPending: number
}
export const UserSchema = SchemaFactory.createForClass(User)

UserSchema.set('toJSON', {
	transform: (_doc, ret) => {
		ret.id = ret._id // Rename _id to id
		ret._id = undefined
		ret.__v = undefined
		ret.createdAt = undefined
		ret.updatedAt = undefined
	},
})

UserSchema.pre('save', async function (next) {
	try {
		const userModel = this.constructor as Model<Document & UserDocument>
		if (!this.isNew) {
			return next()
		}

		// Find the document with the highest UID in the collection
		const lastDocument = await userModel.findOne({}, { uid: 1 }).sort({ uid: -1 })

		// If a document is found, increment the UID, otherwise start with 1
		if (lastDocument) {
			this.uid = lastDocument.uid + 1
		} else {
			this.uid = 1 // This is the first document in the collection
		}

		// Ensure dateRegistered is set if not present or NaN
		if (!this.dateRegistered || Number.isNaN(this.dateRegistered.getTime())) {
			this.dateRegistered = new Date()
		}

		// Generate normalized email
		this.normalizedEmail = normalizeEmailForSchema(this.email)

		// Proceed to the next middleware
		this.referralCode = hash({
			email: this.email,
		})
			.toUpperCase()
			.slice(0, 6)
		next()
	} catch (error) {
		next(error) // Pass any errors to the next middleware
	}
})
