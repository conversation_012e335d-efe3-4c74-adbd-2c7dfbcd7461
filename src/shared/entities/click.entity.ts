import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Model, Types } from 'mongoose'
import { hash } from 'ohash'
import { Admin } from './admin.entity'
import { Affiliation } from './affiliation.entity'
import { StoreCategory } from './category'
import { Link } from './link.entity'
import { Offer } from './offer.entity'
import { Store } from './store.entity'
import { User } from './user.entity'

export type ClickDocument = HydratedDocument<Click>

@Schema({ timestamps: true })
export class Click {
	@Prop({ type: Number, required: true, unique: true, index: true })
	uid!: number

	@Prop({ type: String, required: true, unique: true, index: true })
	referenceId!: string

	@Prop({ type: Types.ObjectId, ref: 'Offer' })
	offer!: Offer

	@Prop({ type: Types.ObjectId, ref: 'Store' })
	store!: Store

	@Prop({ type: Types.ObjectId, ref: 'StoreCategory' })
	storeCategory!: StoreCategory

	@Prop({ type: String, required: false })
	userIp!: string

	@Prop({ type: String, required: true })
	title!: string

	@Prop({ type: String, required: true })
	url!: string

	@Prop({ type: String, required: false })
	offerUrl: string

	@Prop({ type: String, required: false })
	userCity!: string

	@Prop({ type: String, enum: ['offer', 'express', 'rates'], required: true })
	type!: 'offer' | 'express' | 'rates'

	@Prop({
		type: String,
		required: true,
		enum: ['clicked', 'tracked', 'confirmed', 'cancelled'],
		default: 'clicked',
	})
	status!: 'clicked' | 'tracked' | 'confirmed' | 'cancelled'

	@Prop({ type: String, required: false })
	device!: string

	@Prop({ type: Types.ObjectId, required: false, ref: 'User' })
	user!: User

	@Prop({ type: Types.ObjectId, required: true, ref: 'Affiliation' })
	affiliation!: Affiliation

	@Prop({ type: Types.ObjectId, required: false, ref: 'Link' })
	link: Link

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin

	createdAt!: Date
}

export const ClickSchema = SchemaFactory.createForClass(Click)

ClickSchema.pre('save', async function (next) {
	if (!this.isNew) {
		return next()
	}
	const click = this.constructor as Model<ClickDocument>
	const lastDocument = await click.findOne().sort({ _id: -1 })
	if (lastDocument) {
		this.uid = lastDocument.uid + 1
	} else {
		this.uid = 1
	}
	this.referenceId = `CBCLK${hash({
		uid: this.uid,
		user: this.user,
		affiliation: this.affiliation,
		time: Date.now(),
	}).toUpperCase()}`
	this.status = 'clicked'
	next()
})
