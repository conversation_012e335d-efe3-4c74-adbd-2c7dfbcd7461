import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose'
import { type HydratedDocument, Types } from 'mongoose'
import { hash } from 'ohash'
import { Status, type StatusType } from 'shared/types'
import type { Admin } from './admin.entity'
import { type Image, ImageSchema } from './image.entity'
import type { PersonalInterest } from './personal-interest.entity'

export type UnverifiedUserDocument = HydratedDocument<UnverifiedUser>

export type ReqUnverifedUser = {
	id: Types.ObjectId
	email: string
}

@Schema({ timestamps: true })
export class UnverifiedUser {
	@Prop()
	name: string

	@Prop({ type: ImageSchema })
	avatar: Image

	@Prop({ unique: true, index: true, required: true })
	email: string

	@Prop({
		type: Number,
		unique: true,
		sparse: true,
		required: false,
		set: (v: number | null | undefined) => (v === null ? undefined : v),
	})
	mobile?: number

	@Prop({ type: Types.ObjectId, ref: 'UnverifedUser' })
	referral: UnverifiedUser

	@Prop()
	referralCode: string

	@Prop()
	fbUnverifedUserId: string

	@Prop()
	googleId: string

	// @Prop({ default: false })
	// isNotified: boolean;

	@Prop()
	userNotes: string

	@Prop()
	address: string

	@Prop({ type: String })
	postcode: string

	@Prop({ type: Date, default: new Date() })
	dateRegistered: Date

	@Prop()
	lastLoggedDate: Date

	@Prop()
	ambassador: string

	@Prop({ default: false })
	rcBlock: boolean

	@Prop({ required: true, default: 0 })
	balance: number

	@Prop({ required: true, default: 0 })
	flipkartRewardPoints: number

	@Prop({ required: true, default: 0 })
	pendingFlipkartRewardPoints: number

	@Prop({ required: true, default: 0 })
	pendingBalance: number

	@Prop({ required: true, default: 0 })
	giftCardBalance: number

	@Prop()
	specialCampaign: string

	@Prop()
	campaignDone: string

	@Prop()
	isWithdrawReminded: string

	@Prop({ default: new Date() })
	withdrawRemindDate: Date

	@Prop()
	safeUnverifedUser: string

	@Prop({ default: false })
	mobileVerified: boolean

	@Prop({ default: false })
	sendNotification: boolean

	@Prop({ type: [Types.ObjectId], ref: 'PersonalInterest', default: [] })
	personalInterest?: PersonalInterest[]

	@Prop({ enum: Status, default: Status.inactive, type: String })
	status: StatusType

	@Prop({ default: 0 })
	flipkartRewardPoint: number

	@Prop({ default: 0 })
	rewardPoints: number

	@Prop({ default: 0 })
	pendingRewardPoints: number

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy?: Admin

	@Prop({ type: Boolean, default: false })
	migrated?: boolean

	@Prop({ type: Number, default: 0 })
	oldId: number
}
export const UnverifiedUserSchema = SchemaFactory.createForClass(UnverifiedUser)

UnverifiedUserSchema.set('toJSON', {
	transform: (_doc, ret) => {
		ret.id = ret._id // Rename _id to id
		ret._id = undefined
		ret.__v = undefined
		ret.createdAt = undefined
		ret.updatedAt = undefined
	},
})

UnverifiedUserSchema.pre('save', async function (next) {
	try {
		if (!this.isNew) {
			return next()
		}
		if (this.mobile === null) {
			this.mobile = undefined
		}

		// Ensure dateRegistered is set if not present or NaN
		if (!this.dateRegistered || Number.isNaN(this.dateRegistered.getTime())) {
			this.dateRegistered = new Date()
		}

		// Proceed to the next middleware
		this.referralCode = hash({
			email: this.email,
		})
			.toUpperCase()
			.slice(0, 6)
		next()
	} catch (error) {
		next(error) // Pass any errors to the next middleware
	}
})
