import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'

export type PaymentSessionDocument = HydratedDocument<PaymentSession>

const PaymentSessionStatus = {
	active: 'active',
	completed: 'completed',
	cancelled: 'cancelled',
	expired: 'expired',
} as const

@Schema({
	timestamps: true,
})
export class PaymentSession {
	@Prop({ type: Types.ObjectId, ref: 'User', required: true })
	user!: Types.ObjectId

	@Prop({ required: true, type: Number })
	lockedBalance!: number

	@Prop({
		required: true,
		default: new Date(Date.now() + 3 * 60_000),
	})
	expiryDate!: Date

	@Prop({
		type: String,
		required: true,
		enum: PaymentSessionStatus,
		default: PaymentSessionStatus.active,
	})
	status!: (typeof PaymentSessionStatus)[keyof typeof PaymentSessionStatus]
}

export const PaymentSessionSchema = SchemaFactory.createForClass(PaymentSession)
