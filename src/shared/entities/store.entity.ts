import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument, Types } from 'mongoose'
import { Admin } from './admin.entity'
import { Affiliation } from './affiliation.entity'
import { Categories } from './category/category-collection.entity'
import { GiftCard } from './gift-card.entity'
import { Image, ImageSchema } from './image.entity'

@Schema({ timestamps: true })
export class Store {
	@Prop({ type: Number, required: true, default: 0, index: true })
	uid: number

	@Prop({ type: String, required: true })
	name: string

	@Prop({
		type: ImageSchema,
	})
	logo: Image

	@Prop({
		type: ImageSchema,
	})
	banner: Image

	@Prop()
	offerWarning: `<ul>${string}</ul>`

	@Prop([
		{
			type: Types.ObjectId,
			required: true,
			ref: 'Categories',
		},
	])
	categories!: Categories[]

	@Prop({ type: Types.ObjectId, ref: 'Affiliation' })
	affiliation: Affiliation

	@Prop({ type: String, required: true })
	affiliateLink: string

	@Prop()
	campaignType: string

	@Prop({ type: String })
	affiliateOfferId: string

	@Prop()
	storeOffer: string

	@Prop([{ type: Types.ObjectId, ref: 'Store' }])
	relatedStores: Store[]

	@Prop({ type: Number, default: 0 })
	minimumAmount: number

	@Prop({ type: Number, default: 0 })
	maximumAmount: number

	@Prop()
	description: string

	@Prop()
	detailedDescription: string

	@Prop()
	reliability: number

	@Prop({ type: Boolean, default: false })
	trackable: boolean

	@Prop({ type: Number, default: 0 })
	priority: number

	@Prop({ type: Boolean, default: true })
	autoCheck: boolean

	@Prop()
	homeOffer: string

	@Prop({
		type: String,
		enum: ['none', 'special', 'new', 'high', '100%'],
		default: 'none',
	})
	isSpecial: 'none' | 'special' | 'new' | 'high' | '100%'

	@Prop()
	storeWarning: string

	@Prop()
	storeTopWarning: string

	@Prop()
	topWarningLink: string

	@Prop()
	topWarningShowInactive: boolean

	@Prop({ type: String, default: 'all' })
	warningType: string | 'all'

	@Prop()
	storeHowToGet: string

	@Prop()
	storeTerms: string

	@Prop({ type: Boolean, default: false })
	noAppSaleCategory: boolean

	@Prop({ type: Boolean, default: false })
	noMobileWebSaleCategory: boolean

	@Prop({ type: Boolean, default: false })
	noDesktopWebSaleCategory: boolean

	@Prop({ type: Types.ObjectId, ref: 'GiftCard' })
	giftCard: GiftCard

	@Prop({ type: Types.ObjectId, ref: 'Store' })
	instantStore: Store

	@Prop({ type: Number, default: 0 })
	cashbackAmount: number

	@Prop({ type: Number, default: 0 })
	cashbackPercent: number

	@Prop({ type: String, enum: ['flat', 'upto'], default: 'upto' })
	offerType: 'flat' | 'upto'

	@Prop({ type: String, enum: ['reward', 'cashback'], default: 'cashback' })
	cashbackType: 'reward' | 'cashback'

	@Prop({ type: Boolean, default: false })
	isInstant: boolean

	@Prop({ type: Boolean, default: false })
	deepLinkEnable: boolean

	@Prop()
	trackingTime: string

	@Prop()
	confirmationTime: string

	@Prop()
	importantPoints: string

	@Prop({ type: Boolean, default: false })
	missingAccepted: boolean

	@Prop({ type: String, default: '#70367c' })
	bgColor: string

	@Prop({ type: Boolean, default: false })
	trending: boolean

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin

	@Prop({ type: Boolean, default: true })
	active: boolean

	@Prop({ type: Boolean, default: false })
	isDeleted: boolean

	@Prop({ type: Number })
	ratesTotal: number

	@Prop({ type: Number })
	ratesCount: number

	@Prop({ type: Number, default: 0 })
	oldId: number

	updatedAt: Date
}

export type StoreDocument = HydratedDocument<Store>
const StoreSchema = SchemaFactory.createForClass(Store)

export { StoreSchema }
