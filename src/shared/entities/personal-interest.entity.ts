import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { type HydratedDocument, Types } from 'mongoose'
import type { Admin } from './admin.entity'

export type PersonalInterestDocument = HydratedDocument<PersonalInterest>

@Schema({ timestamps: true })
export class PersonalInterest {
	_id: Types.ObjectId
	@Prop({ type: Number, required: true, default: 0 })
	uid: number

	@Prop({ type: String, required: true })
	name: string

	@Prop({ type: Boolean, default: true })
	active: boolean

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	createdBy!: Admin

	@Prop({ type: Types.ObjectId, ref: 'Admin' })
	updatedBy!: Admin
}

export const PersonalInterestSchema = SchemaFactory.createForClass(PersonalInterest)
