import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'

export type ChatMessageDocument = HydratedDocument<ChatMessage>

export type MessageRole = 'user' | 'assistant' | 'system'

@Schema({ timestamps: true })
export class ChatMessage {
	@Prop({ required: true, index: true })
	sessionId: string

	@Prop({ required: true, enum: ['user', 'assistant', 'system'] })
	role: MessageRole

	@Prop({ required: true })
	content: string

	@Prop({ type: Object, required: false })
	metadata?: {
		tokensUsed?: number
		model?: string
		latency?: number
		contextRetrieved?: boolean
		contextSources?: string[]
		toolsUsed?: string[]
		iterations?: number
		errorMessage?: string
	}

	@Prop({ type: Date, default: Date.now })
	createdAt: Date

	@Prop({ type: Date, default: Date.now })
	updatedAt: Date
}

export const ChatMessageSchema = SchemaFactory.createForClass(ChatMessage)

// Add indexes for efficient querying
ChatMessageSchema.index({ sessionId: 1, createdAt: -1 })
ChatMessageSchema.index({ createdAt: -1 })

// Transform output to match API expectations
ChatMessageSchema.set('toJSON', {
	transform: (_doc, ret) => {
		ret.id = ret._id
		ret._id = undefined
		ret.__v = undefined
		return ret
	},
})
