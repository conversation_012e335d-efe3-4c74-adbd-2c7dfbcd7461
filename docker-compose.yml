services:
  # caddy:
  #   image: caddy
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   networks:
  #     - backend-net
  #   volumes:
  #     - ./data/:/data/
  #     - ./config/:/config/
  #     - ./Caddyfile:/etc/caddy/Caddyfile

  icb_api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: icb_api

    restart: unless-stopped

    ports:
      - "3000:3000" # Adjust this port as needed
    env_file:
      - .env
    # volumes:
    #   - ./data:/usr/src/app/data
    networks:
      - backend-net


volumes:
  api_data:

networks:
  backend-net:
