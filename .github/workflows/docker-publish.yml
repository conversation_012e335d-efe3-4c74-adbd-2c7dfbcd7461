name: ICB API Main Deployment

# on:
#   release:
#     types: [published]
on:
  push:
    branches: [ not-right-now ]


env:
  # Use docker.io for Docker Hub if empty
  REGISTRY: ghcr.io
  API_NAME: ${{ github.repository_owner }}/icb_api:latest

jobs:

  publish:
    name: publish api image
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
      - name: Login
        run: |
          echo ${{ secrets.PAT }} | docker login ghcr.io -u ${{ github.actor }} --password-stdin
      - name: Build and Publish API
        run: |
          IMAGE_NAME_LOWER=$(echo ${{ env.API_NAME }} | tr '[:upper:]' '[:lower:]')
          docker build . -f Dockerfile --tag ${{ env.REGISTRY }}/$IMAGE_NAME_LOWER
          docker push ${{ env.REGISTRY }}/$IMAGE_NAME_LOWER

  deploy:
    needs: publish
    name: deploy image
    runs-on: icb-main
    steps:
      - name: pull and deploy
        working-directory: /home/<USER>/development

        run: |
          docker compose -f docker-compose.yaml down -v && \
          docker compose -f docker-compose.yaml pull && \
          docker compose -f docker-compose.yaml up -d
      - name: Remove dangling Docker images
        run: |
          docker image prune -f
