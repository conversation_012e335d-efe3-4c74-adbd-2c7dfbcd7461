name: Lefthook Pre-commit Checks

on:
  pull_request:
    branches:
      - development

jobs:
  lefthook-checks:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
        name: Checkout code

      - name: Install pnpm
        uses: pnpm/action-setup@v2

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: lts/*

      - name: Setup
        run: npm i -g @antfu/ni

      - name: Install
        run: nci

      - name: Run Lefthook Check Command
        run: |
          npx biome check --no-errors-on-unmatched --files-ignore-unknown=true

      - name: Run Lefthook Spellcheck Command
        run: |
          npx cspell "**/*.ts" || echo "Spell check completed with warnings"
