###################
# BASE IMAGE
###################
FROM oven/bun:latest AS base

# Install Chrome dependencies for Puppeteer
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libx11-xcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    --no-install-recommends \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /usr/src/app
COPY package.json ./
COPY bun.lock ./


###################
# BUILD FOR LOCAL DEVELOPMENT
###################
FROM base AS development
RUN bun i
COPY . .
USER bun

###################
# BUILD FOR PRODUCTION
###################
FROM base AS build
RUN bun i
COPY . .
RUN bun run build

###################
# PRODUCTION
###################
FROM base AS production
COPY --from=build /usr/src/app/dist ./dist
COPY --from=build /usr/src/app/node_modules ./node_modules
# Copy tsconfig.json to ensure path mappings work correctly
COPY --from=build /usr/src/app/tsconfig.json ./
COPY package.json ./

# Set Puppeteer environment variables for containerized environment
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=false
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Install Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# USER bun
# RUN mkdir -p uploads && \
#     chown -R bun:bun /usr/src/app/uploads && \
#     chmod -R 755 /usr/src/app/uploads
ENV NODE_ENV=production
CMD [ "bun", "run", "start:prod" ]
