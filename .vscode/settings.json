{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "cSpell.enabled": true, "cSpell.logLevel": "Debug", "editor.tabSize": 2, "cSpell.words": ["apdex", "camelcase", "CLOUDINARY", "destr", "mailgun", "<PERSON><PERSON><PERSON>", "resave", "Subdocument"], "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "peacock.remoteColor": "#dd0531", "remote.autoForwardPortsFallback": 0, "workbench.colorCustomizations": {"activityBar.activeBackground": "#fa1b49", "activityBar.background": "#fa1b49", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#155e02", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#fa1b49", "statusBar.background": "#dd0531", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#fa1b49", "statusBarItem.remoteBackground": "#dd0531", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#dd0531", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#dd053199", "titleBar.inactiveForeground": "#e7e7e799"}}